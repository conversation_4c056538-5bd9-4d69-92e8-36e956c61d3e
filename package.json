{"name": "leap-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@intentui/icons": "^1.11.0", "@internationalized/date": "^3.8.1", "@radix-ui/react-scroll-area": "^1.2.8", "@react-aria/i18n": "^3.12.9", "@react-stately/calendar": "^3.8.1", "@react-types/overlays": "^3.8.15", "@tanstack/react-query": "^5.80.7", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.507.0", "motion": "^12.9.7", "next": "15.3.1", "nuqs": "^2.4.3", "react": "^19.0.0", "react-aria-components": "^1.9.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "sonner": "^2.0.3", "swiper": "^11.2.8", "tailwindcss-react-aria-components": "^2.0.0", "zod": "^3.25.30", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "clsx": "^2.1.1", "next-themes": "^0.4.6", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}