'use server'

import { cookies } from 'next/headers';
import { fakeAuthStore } from '@/lib/fakeAuthStore';

export async function setAuthCookie(userId: number) {
  const cookieStore = await cookies();
  cookieStore.set('auth_user_id', String(userId), { path: '/', maxAge: 60 * 60 * 24 * 7 }); // 7 days
}

export async function clearAuthCookie() {
  const cookieStore = await cookies();
  cookieStore.delete('auth_user_id');
}

export async function getCurrentUser() {
  const cookieStore = await cookies();
  const id = cookieStore.get('auth_user_id')?.value;
  if (!id) return null;
  return fakeAuthStore.findById(Number(id)) ?? null;
}


export async function isCurrentUser(slug: string) {
  const currentUser = await getCurrentUser();
  if (!currentUser || currentUser.type !== "artiste") return false;
  return currentUser.artisteData?.username === slug;
}