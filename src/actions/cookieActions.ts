'use server';

import { cookies } from 'next/headers';

export async function setAuthCookieAction(userId: number) {
  const cookieStore = await cookies();
  cookieStore.set('auth_user_id', String(userId), { path: '/', maxAge: 60 * 60 * 24 * 7 }); // 7 days
  return { success: true };
}

export async function clearAuthCookieAction() {
  const cookieStore = await cookies();
  cookieStore.delete('auth_user_id');
  return { success: true };
}