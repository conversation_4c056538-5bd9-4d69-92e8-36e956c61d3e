"use server"

import { artisteProfiles } from "@/data/artiste-profiles";
import { organisations } from "@/data/organizations";
import { opportunites } from "@/data/opportunites";

function normalizeString(str: string) {
    return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase();
}

export async function getAllArtistes() {
    return artisteProfiles
}


export async function featuredArtistes() {
    return artisteProfiles.filter(artiste => artiste.isFeatured);
}


export async function getFilteredArtistes({
    search,
    genre,
    localisation: location,
    page = 1,
    limit = 10
}: {
    search?: string,
    genre?: string,
    localisation: string,
    page?: number,
    limit?: number
}) {
    let filteredArtistes = await getAllArtistes();
    if (search) {
        // filteredArtistes = filteredArtistes.filter(artiste =>
        //     artiste.about.firstName.toLowerCase().includes(search.toLowerCase()) ||
        //     artiste.about.lastName.toLowerCase().includes(search.toLowerCase()) ||
        //     artiste.about.title.toLowerCase().includes(search.toLowerCase()) ||
        //     artiste.skills.some(skill => skill.toLowerCase().includes(search.toLowerCase()))
        // );

        const normalizedSearch = normalizeString(search);

        filteredArtistes = filteredArtistes.filter(artiste => {
            const { firstName, lastName, title } = artiste.about;
            return (
                normalizeString(firstName).includes(normalizedSearch) ||
                normalizeString(lastName).includes(normalizedSearch) ||
                normalizeString(title).includes(normalizedSearch) ||
                artiste.skills.some(skill =>
                    normalizeString(skill).includes(normalizedSearch)
                )
            );
        });
    }

    if (genre) {
        filteredArtistes = filteredArtistes.filter(artiste =>
            artiste.genre.toLowerCase() === genre.toLowerCase()
        );
    }

    if (location) {
        filteredArtistes = filteredArtistes.filter(artiste =>
            artiste.about.city.toLowerCase().includes(location.toLowerCase())
        );
    }

    const start = (page - 1) * limit;
    const end = start + limit;
    const artistes = filteredArtistes.slice(start, end);
    const hasMore = end < filteredArtistes.length;

    return {
        artistes,
        hasMore,
        total: filteredArtistes.length
    };
}

export async function getArtiste(username: string) {
    return artisteProfiles.find(artiste => artiste.username === username);
}


export async function getOrganismes() {
    return organisations
}

export async function getFilteredOrganismes({
    search,
    location,
    page = 1,
    limit = 10
}: {
    search?: string,
    location?: string,
    page?: number,
    limit?: number
}) {
    let filteredOrganisations = await getOrganismes()

    if (search) {
        filteredOrganisations = filteredOrganisations.filter(org =>
            org.name.toLowerCase().includes(search.toLowerCase()) ||
            org.description.toLowerCase().includes(search.toLowerCase())
        );
    }

    if (location) {
        filteredOrganisations = filteredOrganisations.filter(org =>
            org.location.toLowerCase().includes(location.toLowerCase())
        );
    }

    const start = (page - 1) * limit;
    const end = start + limit;
    const organisations = filteredOrganisations.slice(start, end);
    const hasMore = end < filteredOrganisations.length;

    return {
        organisations,
        hasMore,
        total: filteredOrganisations.length
    };
}
export async function getOrganisme(slug: string) {
    return organisations.find(org => org.slug === slug);
}


export async function getOpportunities() {
    return opportunites
}

export async function getFilteredOpportunities({
    search,
    page = 1,
    limit = 10,
    location,
}: {
    search?: string,
    location?: string,
    page?: number,
    limit?: number
}) {
    let filteredOpportunities = await getOpportunities();

    if (search) {
        filteredOpportunities = filteredOpportunities.filter(opportunity =>
            opportunity.jobTitle.toLowerCase().includes(search.toLowerCase()) ||
            opportunity.description.toLowerCase().includes(search.toLowerCase()) ||
            opportunity.skillsRequired.some(skill => skill.toLowerCase().includes(search.toLowerCase()))
        );
    }
    if (location) {
        filteredOpportunities = filteredOpportunities.filter(opportunity => opportunity.location.city === location)
    }

    const start = (page - 1) * limit;
    const end = start + limit;
    const opportunities = filteredOpportunities.slice(start, end);
    const hasMore = end < filteredOpportunities.length;

    return {
        opportunities,
        hasMore,
        total: filteredOpportunities.length
    };
}
export async function getOpportunity(slug: string) {
    return opportunites.find(opportunity => opportunity.slug === slug);
}