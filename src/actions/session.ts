'use server'

import { getCurrentUser } from './auth';
import { fakeAuthStore } from '@/lib/fakeAuthStore';
import { setAuthCookie, clearAuthCookie } from '@/actions/auth';
import { LoginFormValues, RegisterFormValues, ForgotPasswordFormValues, ResetPasswordFormValues } from '@/lib/auth/types';
import { artisteProfiles } from '@/data/artiste-profiles';
import { organisations } from '@/data/organizations';

export async function getAppServerSession() {
  const user = await getCurrentUser();
  return user ? { user } : null;
}

export const loginUser = async ({ email, password, rememberMe }: LoginFormValues) => {
  // Simulate network request
  await new Promise(resolve => setTimeout(resolve, 300));
  const user = fakeAuthStore.validateCredentials(email, password);
  
  if (!user) {
    throw new Error('Invalid credentials');
  }
  
  await setAuthCookie(user.id);
  return user;
};

export const registerUser = async (values: RegisterFormValues) => {
  // Simulate network request
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const { email, password, userType, name } = values;
  
  // Check if user already exists
  const existingUser = fakeAuthStore.findByEmail(email);
  if (existingUser) {
    throw new Error('User already exists');
  }
  
  let newUser;
  
  if (userType === 'artiste') {
    // Find a template artiste profile to clone
    const template = artisteProfiles[0];
    const newId = Math.max(...fakeAuthStore.getAll().map(u => u.id)) + 1;
    
    newUser = fakeAuthStore.addUser({
      id: newId,
      type: 'artiste',
      email,
      password,
      artisteData: {
        ...template,
        id: newId,
        username: name.toLowerCase().replace(/\s+/g, '-'),
        auth: {
          email,
          phone: template.auth.phone,
        },
        about: {
          ...template.about,
          firstName: name.split(' ')[0] || '',
          lastName: name.split(' ')[1] || '',
        }
      }
    });
  } else {
    // Find a template organization to clone
    const template = organisations[0];
    const newId = Math.max(...fakeAuthStore.getAll().map(u => u.id)) + 1;
    
    newUser = fakeAuthStore.addUser({
      id: newId,
      type: 'org',
      email,
      name,
      password,
      orgData: {
        ...template,
        id: newId,
        name,
        contactEmail: email,
        slug: name.toLowerCase().replace(/\s+/g, '-'),
      }
    });
  }
  
  await setAuthCookie(newUser.id);
  return newUser;
};

export const logoutUser = async () => {
  await clearAuthCookie();
  return true;
};

export const forgotPassword = async ({ email }: ForgotPasswordFormValues) => {
  // Simulate network request
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const user = fakeAuthStore.findByEmail(email);
  if (!user) {
    throw new Error('User not found');
  }
  
  // In a real app, we would send an email with a reset link
  // For our fake auth, we'll just return success
  return true;
};

export const resetPassword = async (token: string, { password }: ResetPasswordFormValues) => {
  // Simulate network request
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // In a real app, we would validate the token and update the user's password
  // For our fake auth, we'll just return success
  return true;
};