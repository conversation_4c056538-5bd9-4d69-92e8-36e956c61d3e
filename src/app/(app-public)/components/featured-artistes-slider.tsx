"use client"

import { ArtistCard } from "@/components/artiste/artist-show-card"
import { artisteProfiles } from "@/data/artiste-profiles"
import { Swiper, SwiperSlide } from "swiper/react"

export const FeaturedArtistesSlider = () => {
    return (
        <>
            <Swiper
                slidesPerView={'auto'}
                spaceBetween={14}
                className="mySwiper mt-16"
            >
                {
                    artisteProfiles.slice(0, 8).map((artiste, index) =>
                        <SwiperSlide className="w-[320px] max-sm:max-w-[87%] sm:w-[47%] md:w-[40%] sm:max-w-[47%] md:max-w-[40%] lg:w-1/4 lg:max-w-[24%]" key={artiste.id}>
                            <ArtistCard className="flex-1 min-w-[72%] sm:min-w-[40%] md:min-w-[35%] lg:max-w-none md:max-w-none" key={`-artiste-${artiste.id} ${index}`}
                                {...artiste}
                            />
                        </SwiperSlide>)
                }
            </Swiper>
        </>
    )
}
