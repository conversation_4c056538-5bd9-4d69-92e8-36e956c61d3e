"use client"

import { ForceVideoPlay } from "@/components/atoms/force-video-play"
import { UiBreakpoint } from "@/components/atoms/ui-breakpoint"
import { Disclosure, DisclosurePanel, DisclosureTrigger } from "@/components/ui/disclosure"
import { BrickWallFire, FoldHorizontal, UserSearch } from "lucide-react"
import { useState, useEffect } from "react"

const features = [
  {
    id: "01",
    title: "Trouvez des opportunités",
    description: "Artistes : accédez à des opportunités ciblées selon votre profil, développez votre visibilité et décrochez des projets en accord avec vos aspirations.",
    video: "/demo_1.mp4",
    icon: BrickWallFire
  },
  {
    id: "02",
    title: "Trouvez l’artiste idéal",
    description: "Organismes : explorez une base d’artistes variés, filtrez selon vos besoins, et trouvez rapidement le profil qui correspond parfaitement à votre projet.",
    video: "/video_large.mp4",
    icon: UserSearch
  },
  {
    id: "03",
    title: "Collaborez en toute simplicité",
    description: "Discutez en direct, gérez les disponibilités, contractualisez et suivez vos collaborations dans un espace fluide et centralisé.",
    video: "/video_large.mp4",
    icon: FoldHorizontal
  }
]

export const FeaturesHome = () => {
  const [activeItem, setActiveItem] = useState(features[0].id)
  const [activeItemVal, setActiveItemVal] = useState(features[0])
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    if (progress < 100) {
      const timer = setInterval(() => {
        setProgress(prev => Math.min(prev + 2, 100))
      }, 100)
      return () => clearInterval(timer)
    } else {
      // First reset progress
      setProgress(0)
      // Then use setTimeout to change the active item after a brief delay
      setTimeout(() => {
        const nextIndex = (features.findIndex(f => f.id === activeItem) + 1) % features.length
        setActiveItem(features[nextIndex].id)
        setActiveItemVal(features[nextIndex])
      }, 50)
    }
  }, [progress, activeItem])

  const handleExpandedChange = (id: string) => {
    setProgress(0)
    setActiveItem(id)
  }

  return (
    <section className="lg:max-w-7xl mx-auto w-full grid lg:grid-cols-2 gap-4 lg:gap-16 px-4 sm:px-6 lg:px-4 mt-24">
      <div className="lg:py-6 flex flex-col">
        <span className="text-fg-title/70 font-medium bg-muted rounded-xl px-3 py-0.5 border border-border/50 text-sm w-max max-w-full">
          Découvrir, recruter et collaborer
        </span>
        <h2 className="font-medium text-3xl sm:text-4xl/snug text-fg-title mt-4">
          Une plateforme pensée pour connecter les talents du spectacle vivant
        </h2>
        <div className={"flex flex-col gap-2 mt-12 *:data-[slot=disclosure-content]:space-y-2"}>
          {features.map((feature, index) => (
            <Disclosure
              isExpanded={feature.id === activeItem}
              key={feature.id}
              onExpandedChange={() => {
                setActiveItemVal(feature)
                handleExpandedChange(feature.id)
              }}
              id={feature.id}
              className={"overflow-hidden rounded-xl border border-border/70 bg-bg/40 !pb-0"}
            >
              <DisclosureTrigger className={"flex items-start gap-x-3 w-full text-left p-4"}>
                <span className="size-8 rounded-lg bg-muted border text-fg flex items-center justify-center">
                  <feature.icon strokeWidth={1.2} className="size-4" />
                </span>
                <h3 className="font-semibold text-fg-title/80 sm:text-lg flex-1">
                  {feature.title}
                </h3>
              </DisclosureTrigger>
              <DisclosurePanel className={"duration-200 ease-linear overflow-hidden *:[data-slot=disclosure-panel-content]:pb-0"}>
                <div className="space-y-4 px-4 py-4 border-t border-border">
                  <p className="text-fg">
                    {feature.description}
                  </p>
                  <UiBreakpoint size="mobile-sm-tablet">
                    <div className="grid">
                      <ForceVideoPlay className="w-full h-auto rounded-xl" src={feature.video} />
                    </div>
                  </UiBreakpoint>
                  <div className="w-full h-1 rounded-full bg-bg-surface">
                    <span className="flex h-full rounded-full bg-fg-title ease-linear transition-[width]" style={{ width: `${progress}%` }}></span>
                  </div>
                </div>
              </DisclosurePanel>
            </Disclosure>
          ))}
        </div>
      </div>
      <div className="hidden lg:grid">
        <div className="xl:w-[90%] ml-auto bg-bg-surface overflow-hidden rounded-xl relative">

          <UiBreakpoint size="lg">
            <>
              <ForceVideoPlay className="size-full object-cover rounded-xl object-left-top" src={activeItemVal.video} />
            </>
          </UiBreakpoint>
        </div>
      </div>
    </section>
  )
}


