"use client"

import { motion } from "framer-motion";
import { ForceVideoPlay } from "@/components/atoms/force-video-play";

const videos = [
    "/shorts/axel.mp4",
    "/shorts/aurel-zola-2.0.mp4",
    "/shorts/the-revolutionary-i.mp4",
    "/shorts/victoria-pallern.mp4",
];

export const HeroVideoPlay= ()=> {


    return (
        <div className="flex justify-center overflow-hidden lg:grid lg:grid-cols-4 gap-2 sm:gap-4">
            {videos.map((src, index) => (
                <motion.div
                    key={index}
                    className="w-[46%] min-w-[46%] sm:w-[32.3%] sm:min-w-[32.3%] lg:w-full flex aspect-[9/16] rounded-xl overflow-hidden bg-bg border px-px"
                    initial={{ opacity: 0.3, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                >
                    <ForceVideoPlay
                        src={src}
                        playsInline
                        autoPlay
                        muted
                        loop
                        className="w-full h-full object-cover"
                        preload="auto"
                    />
                </motion.div>
            ))}
        </div>
    );
}
