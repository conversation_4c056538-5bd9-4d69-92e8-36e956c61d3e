import { Calendar1, CalendarCheck, FileSpreadsheet, MessageSquare } from "lucide-react"
import Image from "next/image"

const app_features = [
    {
        id: 1,
        icon: FileSpreadsheet,
        text: "Contrats simplifiés et centralisés"
    },
    {
        id: 2,
        icon: Calendar1,
        text: "Calendrier de disponibilité synchronisé"
    },
    {
        id: 3,
        icon: MessageSquare,
        text: "Messagerie intégrée pour collaborer facilement"
    },
    {
        id: 4,
        icon: CalendarCheck,
        text: "Réservations rapides et sécurisées"
    }
]

export const HomeAllFeatures = () => {
    return (
        <section className="lg:max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-4 mt-24">
            <div className="grid lg:grid-cols-2 gap-4 lg:gap-16 mt-10">
                
                <div className="hidden lg:grid">
                    <div className="xl:w-[90%] bg-bg-surface overflow-hidden rounded-xl relative">
                        <Image src={"/recruiter.webp"} alt="Image app features" width={1900} height={1330} className="size-full object-cover"/>
                    </div>
                </div>
                <div className={"lg:py-3 flex flex-col gap-2"}>
                    <div className="max-w-xl">
                        <span className="text-fg-title/70 font-medium bg-muted rounded-xl px-3 py-0.5 border border-border/50 text-sm w-max max-w-full">
                            Pourquoi utiliser cette plateforme ?
                        </span>
                        <h2 className="font-medium text-3xl sm:text-4xl/snug text-fg-title mt-4">
                            Simplifiez vos collaborations dans le spectacle vivant
                        </h2>
                        <p className="text-fg-muted text-sm md:text-base mt-6">
                            Que vous soyez artiste ou structure, cette plateforme a été pensée pour vous faire gagner du temps, professionnaliser vos échanges et sécuriser chaque projet.
                        </p>
                    </div>
                    <ul className="mt-8 flex flex-col divide-y *:py-2 border-y max-w-md">
                        {
                            app_features.map(item => <li className="flex items-center" key={item.id}>
                                <item.icon className="size-5 mr-3" strokeWidth={1.2}/>
                                <span>{item.text}</span>
                            </li>)
                        }
                    </ul>
                </div>
            </div>
        </section>
    )
}
