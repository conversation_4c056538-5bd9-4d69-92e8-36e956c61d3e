"use client"

import { motion } from "framer-motion"

import Link from 'next/link'
import { buttonStyles } from "@/components/ui/button"
import Image from "next/image"
import { drukCondFont } from "@/app/fonts"
import { HeroVideoPlay } from "./hero-video-play"

export const HomeHeroSection = () => {
    return (
        <section className="w-full ">
            <div className="app-container px-4 sm:px-6 lg:px-4 relative pt-24">
                <motion.div initial={{ opacity: .7, filter: "blur(10px)" }}
                    animate={{ opacity: 1, filter: "blur(0px)" }}
                    transition={{ duration: 0.4 }} className="inset-y-0 inset-x-4 md:inset-x-20 xl:inset-x-32 absolute border-x border-border/40 isolate">
                    <div className="absolute opacity-30 dark:opacity-70 [--grid-color:var(--color-bg-surface-muted)] dark:[--grid-color:var(--color-bg-surface)] inset-0 -z-10 h-full w-full bg-bg/40 bg-[linear-gradient(to_right,var(--grid-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--grid-color)_1px,transparent_1px)] bg-[size:6rem_4rem]"></div>
                </motion.div>
                <div className="flex flex-col justify-center text-center relative">
                    <motion.span initial={{ opacity: .01, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }} className="bg-muted rounded-xl px-3 py-0.5 border border-border/50 w-max mx-auto">
                        ✨ chaque artiste est une étoile
                    </motion.span>
                    <motion.h1
                        className={`${drukCondFont.className} leading-[0.9] mt-4 text-[6rem] sm:text-[9rem] md:text-[12.5rem] text-transparent bg-clip-text bg-gradient-to-bl from-fg-title to-primary-900 dark:to-primary-50 mb-4 mx-auto max-w-5xl`}
                        initial={{ opacity: .01, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.1 }}
                    >
                        UNE GALAXIE DE TALENTS
                    </motion.h1>
                    <motion.p
                        className="text-sm md:text-base text-fg-muted mb-10 max-w-lg mx-auto"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                    >
                        Bienvenue sur cette plateforme qui permet la rencontre entre les organisme et les artistes. Découvrez, collaborer et brillez
                    </motion.p>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.5 }}
                        className="flex flex-wrap items-center justify-center gap-2.5">
                        <div className="border w-max rounded-[calc(var(--radius-xl)+2px)] p-0.5">
                            <Link href={""} className={buttonStyles({ className: "rounded-xl md:h-11 sm:px-5" })}>
                                Je suis un artiste
                            </Link>
                        </div>
                        <div className="border w-max rounded-[calc(var(--radius-xl)+2px)] p-0.5">
                            <Link href={""} className={buttonStyles({ intent: "outline", className: "rounded-xl md:h-11 sm:px-5" })}>
                                Je suis un organisme
                            </Link>
                        </div>
                    </motion.div>
                    <motion.div initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.65 }}
                        className="flex items-center gap-2 mx-auto mt-7">
                        <div className="flex items-center -space-x-4 -ml-4">
                            <Image src={"/avatar/m_user_1.webp"} alt="User jo" width={140} height={140} className="size-11 border-4 border-bg rounded-full object-cover" />
                            <Image src={"/avatar/w_user_1.webp"} alt="User jo" width={140} height={140} className="size-11 border-4 border-bg rounded-full object-cover" />
                            <Image src={"/avatar/w_user_3.webp"} alt="User jo" width={140} height={140} className="size-11 border-4 border-bg rounded-full object-cover" />
                        </div>
                        <div className="flex flex-col justify-start items-start text-left">
                            <span className="text-sm text-amber-500">
                                &#9733;&#9733;&#9733;&#9733;&#9733;
                            </span>
                            <span className="text-xs sm:text-sm text-fg-muted">
                                +100 Etoiles brillantes
                            </span>
                        </div>
                    </motion.div>
                </div>
            </div>
            <div className="lg:max-w-7xl mx-auto px-0.5 lg:px-6 pt-12 relative overflow-hidden">
                <HeroVideoPlay/>
            </div>
        </section>
    )
}
