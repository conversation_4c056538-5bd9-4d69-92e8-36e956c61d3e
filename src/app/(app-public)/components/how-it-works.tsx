"use client"

import { Tabs } from "@/components/ui/tabs"
import { UserCircle, Eye, Briefcase, Building2, Search, FileText } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

const artisteSteps = [
  {
    id: "01",
    title: "Créez votre profil",
    description: "Ajoutez vos informations, disciplines, disponibilités et médias. Valorisez votre parcours en quelques clics.",
    icon: UserCircle
  },
  {
    id: "02",
    title: "Soyez visible",
    description: "Les structures culturelles peuvent vous découvrir via le moteur de recherche ou en consultant vos contenus.",
    icon: Eye
  },
  {
    id: "03",
    title: "Recevez des opportunités",
    description: "Répondez à des demandes de booking ou postulez à des projets proposés par les structures.",
    icon: Briefcase
  }
]

const organismeSteps = [
  {
    id: "01",
    title: "Créez un compte structure",
    description: "Indiquez le type de structure, vos besoins et les profils que vous recherchez.",
    icon: Building2
  },
  {
    id: "02",
    title: "Parcourez la base d'artistes",
    description: "Filtrez par style, localisation, disponibilité, expérience… Trouvez le profil idéal.",
    icon: Search
  },
  {
    id: "03",
    title: "Publiez un projet",
    description: "Décrivez votre besoin : répétitions, représentations, résidences ou collaborations à court ou long terme.",
    icon: FileText
  }
]

// Animation variants
const tabVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      when: "beforeChildren",
      staggerChildren: 0.1
    }
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: { duration: 0.2 }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.3 }
  }
}

export const HowItWorks = () => {
  return (
    <section className="relative mt-24 py-24 bg-gradient-to-b from-white/50 to-gray-50/50 dark:from-gray-900/20 dark:to-gray-950/40">
      <div className="inset-x-0 top-0 h-3/5 rounded-b-full overflow-hidden absolute border-x border-border/40 isolate">
        <div className="absolute opacity-30 dark:opacity-70 [--grid-color:var(--color-bg-surface-muted)] dark:[--grid-color:var(--color-bg-surface)] inset-0 -z-10 h-full w-full bg-bg bg-[linear-gradient(to_right,var(--grid-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--grid-color)_1px,transparent_1px)] bg-[size:3rem_2.5rem]"></div>
      </div>
      <div className="lg:max-w-7xl w-full mx-auto px-4 sm:px-6 lg:px-4 relative">
        <div className="flex items-end md:justify-between gap-6">
          <div className="max-w-xl mx-auto text-center">
            <span className="text-fg-title/70 font-medium bg-muted rounded-xl px-3 py-0.5 border border-border/50 text-sm w-max max-w-full">
              Artistes à découvrir
            </span>
            <h2 className="font-medium text-3xl sm:text-4xl/snug text-fg-title mt-4">
              Explorez des talents uniques et créez des liens inspirants
            </h2>
          </div>
        </div>
        <Tabs aria-label="Steps process" className={"w-full flex flex-col mt-10 gap-8"}>
          <Tabs.List className={"p-0.5 min-h-max overflow-hidden overflow-x-auto w-max bg-bg max-w-full border rounded-xl mx-auto"}>
            <Tabs.Tab id="artiste" className={"py-2.5 px-4 rounded-xl"}>Pour artiste</Tabs.Tab>
            <Tabs.Tab id="organisme" className={"py-2.5 px-4 rounded-xl"}>Pour organisme</Tabs.Tab>
          </Tabs.List>
            <Tabs.Panel id="artiste">
          <AnimatePresence key={"ui-tab"} mode="wait">
              <motion.div
                variants={tabVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="grid gap-5 sm:grid-cols-2 md:grid-cols-3"
              >
                {artisteSteps.map((item) => (
                  <motion.div
                    key={item.id}
                    variants={itemVariants}
                    className="flex flex-col p-5 sm:p-8 rounded-xl bg-bg border border-border/70 relative"
                  >
                    <span className="absolute right-5 top-5 sm:top-8 text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-b from-fg-muted/80 via-fg-muted/30 to-transparent via-40% to-80% opacity-60">
                      {item.id}
                    </span>
                    <span className="w-max p-2 bg-muted border rounded-lg mb-3">
                      <item.icon strokeWidth={1.2} className="size-5" />
                    </span>
                    <h3 className="font-medium sm:text-lg text-fg-title">{item.title}</h3>
                    <p className="text-sm sm:text-base text-fg-muted mt-2.5 max-w-xs">{item.description}</p>
                  </motion.div>
                ))}
              </motion.div>
          </AnimatePresence>
            </Tabs.Panel>
            <Tabs.Panel id="organisme">
            <AnimatePresence key={"ui-tab-org"} mode="wait">
              <motion.div
                variants={tabVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="grid gap-5 sm:grid-cols-2 md:grid-cols-3"
              >
                {organismeSteps.map((item) => (
                  <motion.div
                    key={item.id}
                    variants={itemVariants}
                    className="flex flex-col p-5 sm:p-8 rounded-xl bg-bg border border-border/70 relative"
                  >
                    <span className="absolute right-5 top-5 sm:top-8 text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-b from-fg-muted/80 via-fg-muted/30 to-transparent via-40% to-80% opacity-60">
                      {item.id}
                    </span>
                    <span className="w-max p-2 bg-muted border rounded-lg mb-3">
                      <item.icon strokeWidth={1.2} className="size-5" />
                    </span>
                    <h3 className="font-medium sm:text-lg text-fg-title">{item.title}</h3>
                    <p className="text-sm sm:text-base text-fg-muted mt-2.5 max-w-xs">{item.description}</p>
                  </motion.div>
                ))}
              </motion.div>
              </AnimatePresence>
            </Tabs.Panel>
        </Tabs>
      </div>
    </section>
  )
}
