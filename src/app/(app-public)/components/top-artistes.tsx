"use client"

import { ArtistCard } from "@/components/artiste/artist-show-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { artisteProfiles } from "@/data/artiste-profiles"
import { ArrowLeft, ArrowRight } from "lucide-react"
import { useCallback, useRef, useState } from "react"
import { Swiper, SwiperRef, SwiperSlide } from "swiper/react"

export const TopArtistes = () => {
    const [isBeginning, setIsBeginning] = useState(true);
    const [isEnd, setIsEnd] = useState(false);


    const swiperRef = useRef<SwiperRef>(null);
    const goNext = useCallback(() => {
        if (swiperRef.current) {
            swiperRef.current.swiper.slideNext();
        }
    }, []);
    const goPrev = useCallback(() => {
        if (swiperRef.current) {
            swiperRef.current.swiper.slidePrev();
        }
    }, []);

    const handleSlideChange = () => {
        if (swiperRef.current) {
            setIsBeginning(swiperRef.current.swiper.isBeginning);
            setIsEnd(swiperRef.current.swiper.isEnd);
        }
    };
    return (
        <>
            <section className="mt-24">
                <div className="lg:max-w-7xl w-full mx-auto px-4 sm:px-6 lg:px-4">
                    <div className="flex items-end md:justify-between gap-6">
                        <div className="max-w-xl">
                            <span className="text-fg-title/70 font-medium bg-muted rounded-xl px-3 py-0.5 border border-border/50 text-sm w-max max-w-full">
                                Artistes à découvrir
                            </span>
                            <h2 className="font-medium text-3xl sm:text-4xl/snug text-fg-title mt-4">
                                Explorez des talents uniques et créez des liens inspirants
                            </h2>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button isDisabled={isBeginning} intent="plain" size="extra-small" className={"size-9"} onPress={() => goPrev()}>
                                <ArrowLeft strokeWidth={1.2} className="size-4" />
                            </Button>
                            <Button isDisabled={isEnd} intent="plain" size="extra-small" className={"size-9"} onPress={() => goNext()}>
                                <ArrowRight strokeWidth={1.2} className="size-4" />
                            </Button>
                        </div>
                    </div>
                    <Swiper
                    ref={swiperRef}
                        slidesPerView={'auto'}
                        spaceBetween={0}
                        onSlideChange={handleSlideChange}
                        onAfterInit={handleSlideChange}
                        className="mySwiper mt-16"
                    >
                        {
                            artisteProfiles.slice(0, 8).map((artiste, index) =>
                                <SwiperSlide className="w-[320px] max-sm:max-w-[87%] sm:w-[47%] sm:max-w-[47%] md:max-w-[40%] lg:w-1/3 lg:max-w-[32.5%] mr-4" key={artiste.id}>
                                    <ArtistCard className="flex-1" key={`-artiste-${artiste.id} ${index}`}
                                        {...artiste}
                                    />
                                </SwiperSlide>)
                        }
                    </Swiper>
                </div>
            </section>
        </>
    )
}