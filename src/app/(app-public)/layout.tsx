import "./style-app.css"

import { AppNavbar } from "@/components/organisms/app-navbar";
import { Footer } from "@/components/organisms/footer";
import { AppNavbarProvider } from "@/context/app-actions-context";


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <AppNavbarProvider>
        <AppNavbar />
        <main>
          {children}
        </main>
        <Footer />
      </AppNavbarProvider>
    </>
  );
}
