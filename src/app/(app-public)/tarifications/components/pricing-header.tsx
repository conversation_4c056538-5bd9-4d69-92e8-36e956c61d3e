"use client"

import { drukCondFont } from '@/app/fonts'
import { buttonStyles } from '@/components/ui/button'
import { motion } from 'motion/react'
import Link from 'next/link'


export const PricingHeader = () => {
    return (
        <section className="pt-8 w-full ">
            <div className="app-container px-4 sm:px-6 lg:px-4 relative pt-16 pb-8 border-y border-border/70">
                <motion.div initial={{ opacity: .7, filter: "blur(10px)" }}
                    animate={{ opacity: 1, filter: "blur(0px)" }}
                    transition={{ duration: 0.4 }} className="inset-y-0 inset-x-4 md:inset-x-20 xl:inset-x-32 absolute border-x border-border/40 isolate">
                    <div className="absolute opacity-30 dark:opacity-70 [--grid-color:var(--color-bg-surface-muted)] dark:[--grid-color:var(--color-bg-surface)] inset-0 -z-10 h-full w-full bg-bg/40 bg-[linear-gradient(to_right,var(--grid-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--grid-color)_1px,transparent_1px)] bg-[size:6rem_4rem]"></div>
                </motion.div>
                <div className="flex flex-col justify-center text-center relative">

                    <motion.h1
                        className={`${drukCondFont.className} leading-[0.9] mt-4 text-[4.3rem] sm:text-[6.5rem] text-transparent bg-clip-text bg-gradient-to-bl from-fg-title to-primary-900 dark:to-primary-50 mb-4 mx-auto max-w-2xl`}
                        initial={{ opacity: .01, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.1 }}
                    >
                        BOOSTEZ VOTRE VISIBILITE
                    </motion.h1>
                    <motion.p
                        className="text-sm md:text-base text-fg-muted mb-10 max-w-lg mx-auto"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                    >
                        Que vous soyez artiste cherchant à mettre en valeur votre talent ou organisme souhaitant attirer les meilleurs talents, découvrez nos offres adaptées pour maximiser votre impact sur la plateforme.
                    </motion.p>
                </div>
            </div>
        </section>
    )
}
