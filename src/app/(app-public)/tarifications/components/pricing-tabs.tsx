"use client"

import { But<PERSON> } from '@/components/ui/button'
import { Tabs } from '@/components/ui/tabs'
import { Briefcase, Building2, Check, Eye, FileText, Search, UserCircle } from 'lucide-react'
import { AnimatePresence, motion } from 'motion/react'
import React from 'react'

// Pricing data for Organismes
const ORGANISMES_PRICING = [
    {
        title: "Annonce simple",
        description: "Publication d'une annonce standard pour rechercher un artiste.",
        price: "19 €",
        priceUnit: "",
        buttonLabel: "Choisir",
        features: [
            "Annonce standard",
            "Recherche d'artiste"
        ]
    },
    {
        title: "Pack 3 annonces",
        description: "3 annonces à publier quand souhaité.",
        price: "49 €",
        priceUnit: "",
        buttonLabel: "Choisir",
        features: [
            "3 annonces à publier",
            "Flexibilité de publication"
        ]
    },
    {
        title: "Annonce boostée",
        description: "Mise en avant de l'annonce pendant 7 jours.",
        price: "+10 €",
        priceUnit: "en option",
        buttonLabel: "Ajouter le boost",
        features: [
            "Annonce mise en avant 7 jours"
        ]
    },
    {
        title: "Abonnement Agence Pro",
        description: "Annonces illimitées + messagerie directe + filtres avancés.",
        price: "39 €/mois",
        priceUnit: "ou 390 €/an",
        buttonLabel: "S'abonner",
        features: [
            "Annonces illimitées",
            "Messagerie directe",
            "Filtres avancés"
        ]
    }
];

// Pricing data for Artistes
const ARTISTES_PRICING = [
    {
        title: "Boost 7 jours",
        description: "Mise en avant du profil pendant 7 jours.",
        price: "9 €",
        priceUnit: "",
        buttonLabel: "Booster",
        features: [
            "Profil mis en avant 7 jours"
        ]
    },
    {
        title: "Boost 30 jours",
        description: "Mise en avant du profil pendant 30 jours.",
        price: "25 €",
        priceUnit: "",
        buttonLabel: "Booster",
        features: [
            "Profil mis en avant 30 jours"
        ]
    },
    {
        title: "Pack Premium Artiste",
        description: "Badge vérifié + Statistiques de profil + Boost permanent.",
        price: "14,99 €",
        priceUnit: "/mois",
        buttonLabel: "S'abonner Premium",
        features: [
            "Badge vérifié",
            "Statistiques de profil",
            "Boost permanent"
        ]
    },
    {
        title: "Pack Pro Artiste",
        description: "Tout le premium + Accès prioritaire aux annonces + Vidéos illimitées.",
        price: "24,99 €",
        priceUnit: "/mois",
        buttonLabel: "S'abonner Pro",
        features: [
            "Tout le premium",
            "Accès prioritaire aux annonces",
            "Vidéos illimitées"
        ]
    }
];

// Animation variants
const tabVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.3,
            when: "beforeChildren",
            staggerChildren: 0.1
        }
    },
    exit: {
        opacity: 0,
        y: -20,
        transition: { duration: 0.2 }
    }
}

const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.3 }
    }
}

export const PricingTabs = () => {
    return (
        <>
            <Tabs aria-label="Steps process" className={"w-full flex flex-col -mt-7 relative gap-8"}>
                <Tabs.List className={"p-0.5 min-h-max overflow-hidden overflow-x-auto w-max bg-bg max-w-full border rounded-xl mx-auto"}>
                    <Tabs.Tab id="pricing-tab-artiste" className={"py-2.5 px-4 rounded-xl"}>Pour artiste</Tabs.Tab>
                    <Tabs.Tab id="pricing-tab-organisme" className={"py-2.5 px-4 rounded-xl"}>Pour organisme</Tabs.Tab>
                </Tabs.List>
                <Tabs.Panel id="pricing-tab-artiste">
                    <AnimatePresence key={"pricing-tabs-artiste"} mode="wait">
                        <motion.div
                            variants={tabVariants}
                            initial="hidden"
                            animate="visible"
                            exit="exit"
                            className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4"
                        >
                            {ARTISTES_PRICING.map((item, idx) => (
                                <motion.div
                                    variants={itemVariants} key={item.title + idx} className="p-5 rounded-xl bg-bg border border-border/70 relative">
                                    <div className="mb-6">
                                        <h3 className="text-lg lg:text-xl font-semibold text-fg-tittle mb-3">{item.title}</h3>
                                        <p className="text-fg-muted text-sm leading-relaxed">{item.description}</p>
                                    </div>
                                    <div className="mb-8">
                                        <div className="flex items-baseline">
                                            <span className="text-4xl font-bold text-fg-title">{item.price}</span>
                                            {item.priceUnit && <span className="text-fg-muted/80 ml-2">{item.priceUnit}</span>}
                                        </div>
                                    </div>
                                    <Button size='small' intent='outline' className="w-full justify-center mb-8">
                                        {item.buttonLabel}
                                    </Button>
                                    <ul className="space-y-4">
                                        {item.features.map((feature, fidx) => (
                                            <li key={fidx} className="flex items-center">
                                                <Check strokeWidth={1.2} className='size-4 mr-2.5 text-fg-primary' />
                                                <span className="text-fg-muted">{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}
                        </motion.div>
                    </AnimatePresence>
                </Tabs.Panel>
                <Tabs.Panel id="pricing-tab-organisme">
                    <AnimatePresence key={"ui-tab-org"} mode="wait">
                        <motion.div
                            variants={tabVariants}
                            initial="hidden"
                            animate="visible"
                            exit="exit"
                            className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4"
                        >
                            {ORGANISMES_PRICING.map((item, idx) => (
                                <motion.div
                                    variants={itemVariants} key={item.title + idx} className="p-5 rounded-xl bg-bg border border-border/70 relative">
                                    <div className="mb-6">
                                        <h3 className="text-lg lg:text-xl font-semibold text-fg-tittle mb-3">{item.title}</h3>
                                        <p className="text-fg-muted text-sm leading-relaxed">{item.description}</p>
                                    </div>
                                    <div className="mb-8">
                                        <div className="flex items-baseline">
                                            <span className="text-4xl font-bold text-fg-title">{item.price}</span>
                                            {item.priceUnit && <span className="text-fg-muted/80 ml-2">{item.priceUnit}</span>}
                                        </div>
                                    </div>
                                    <Button size='small' intent='outline' className="w-full justify-center mb-8">
                                        {item.buttonLabel}
                                    </Button>
                                    <ul className="space-y-4">
                                        {item.features.map((feature, fidx) => (
                                            <li key={fidx} className="flex items-center">
                                                <Check strokeWidth={1.2} className='size-4 mr-2.5 text-fg-primary' />
                                                <span className="text-fg-muted">{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </motion.div>
                            ))}

                        </motion.div>
                    </AnimatePresence>
                </Tabs.Panel>
            </Tabs>
        </>
    )
}
