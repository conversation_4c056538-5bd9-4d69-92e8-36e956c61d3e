"use client"
import { ReactNode } from "react"
import { HeaderA<PERSON> } from "./header-auth"
import { HeroVideoVibe } from "@/components/molecules/hero/video-vibe"
import { ArtistCard } from "@/components/artiste/artist-show-card"
import { artisteProfiles } from "@/data/artiste-profiles"
import { motion } from "motion/react"
import { cn } from "@/lib/utils"


export const AuthFormWrapper = ({ children, header, topCol, wrapper }: { children: ReactNode, wrapper?:{box?:string, container?:string}, topCol?: ReactNode, rightSide: ReactNode, header: { caption: string, link: { href: string, text: string } } }) => {

    return (
        <>
            <div className="auth-wrapper">
                <div className="flex h-full flex-col">
                    <HeaderAuth {...header} />
                    {topCol}
                    <div className={cn("flex-1 flex items-center justify-center md:p-8", wrapper?.container)}>
                        <div className={cn("w-full flex flex-col mx-auto max-w-md px-5 sm:px-8 md:px-10", wrapper?.box)}>
                            {children}
                        </div>
                    </div>
                    <div className="flex py-2 justify-center text-center">
                        <div className="text-sm text-fg-subtext">
                            © {new Date().getFullYear()} Leap
                        </div>
                    </div>
                </div>
                <div className="hidden p-2 pl-0 lg:block lg:sticky lg:top-0 lg:max-h-dvh">
                    <div className="bg-muted rounded-l-xl flex w-full h-full rounded-lg items-center justify-center relative overflow-hidden">
                        <motion.div initial={{ opacity: 0, filter: "blur(10px)" }}
                            animate={{ opacity: 1, filter: "blur(0px)" }}
                            transition={{ duration: 0.4 }} className="inset-0 absolute flex">
                            <div className="absolute opacity-30 dark:opacity-70 [--grid-color:var(--color-gray-300)] dark:[--grid-color:var(--color-bg-surface)] inset-0 -z-10 h-full w-full bg-bg/40 bg-[linear-gradient(to_right,var(--grid-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--grid-color)_1px,transparent_1px)] bg-[size:6rem_4rem]"></div>
                        </motion.div>
                        <div className="mx-auto max-w-sm">
                            <HeroVideoVibe />
                        </div>
                        <div className="absolute right-10 top-1/2 w-72">
                            <ArtistCard {...artisteProfiles[0]} />
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}