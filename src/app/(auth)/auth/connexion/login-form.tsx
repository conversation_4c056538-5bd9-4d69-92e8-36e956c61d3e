"use client"

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { ButtonSaveLoading } from "@/components/atoms/ButtonSaveLoading";
import { FormFieldGroup } from "@/components/atoms/FormFieldGroup";
import { SpanError } from "@/components/atoms/span-error";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/field";
import { TextField } from "@/components/ui/text-field";
import Link from "next/link";
import { loginSchema, type LoginFormData } from "@/lib/auth/validationSchemas";
import { useAuthStore } from "@/lib/auth/authStore";

export const LoginForm = () => {
    const router = useRouter();
    const { login, isLoading, error, user } = useAuthStore();

    const {
        setValue,
        handleSubmit,
        formState: { errors },
    } = useForm<LoginFormData>({
        resolver: zod<PERSON><PERSON><PERSON>ver(loginSchema),
    });

    const onSubmit = async (data: LoginFormData) => {
        await login(data);

        if (user) { 
            router.push("/dashboard");
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 pt-6">
            {error && (
                <div className="rounded-md bg-red-50 p-4">
                    <div className="text-sm text-red-700">{error}</div>
                </div>
            )}

            <FormFieldGroup>
                <Label className="block text-sm font-medium">
                    Adresse e-mail <span className="text-error">*</span>
                </Label>
                <div className="relative">
                    <TextField 
                        type="email" 
                        placeholder="<EMAIL>" 
                        className={`[&_input]:ps-10`} 
                        onChange={(e) => setValue("email", e)}
                    />
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="size-4 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                </div>
                {errors.email?.message && <SpanError text={errors.email.message} />}
            </FormFieldGroup>

            <div className="space-y-2">
                <label className="block text-sm font-medium">
                    Mot de passe <span className="text-error">*</span>
                </label>
                <div className="relative">
                    <TextField 
                       onChange={e=>setValue("password", e)}
                        type="password" 
                        placeholder="••••••••" 
                        className={`[&_input]:ps-10`} 
                    />
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="size-4 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                    </div>
                </div>
                {errors.password?.message && <SpanError text={errors.password.message} />}
            </div>

            <div className="flex flex-wrap items-center justify-between gap-4 mt-8">
                <div className="flex items-center gap-x-1">
                    <Checkbox id="remember-me" label="Se souvenir de moi" />
                </div>
                <div className="text-sm">
                    <Link href="/forgot-password" className="text-fg-primary hover:underline font-semibold">
                        Mot de passe oublié ?
                    </Link>
                </div>
            </div>
            <ButtonSaveLoading 
                loading={isLoading} 
                text="Se connecter" 
                className="flex items-center justify-center w-full" 
            />
        </form>
    );
}; 