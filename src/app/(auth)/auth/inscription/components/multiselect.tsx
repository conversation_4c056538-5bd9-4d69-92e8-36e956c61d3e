"use client"
import { useState, KeyboardEvent } from "react";
import { X } from "lucide-react";

import { motion, AnimatePresence } from "framer-motion";
import { Input } from "@/components/ui/field";

interface MultiSelectProps {
  value: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
}

export function MultiSelect({ value, onChange, placeholder }: MultiSelectProps) {
  const [inputValue, setInputValue] = useState("");

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && inputValue.trim()) {
      e.preventDefault();
      const newValue = inputValue.trim();
      if (!value.includes(newValue)) {
        onChange([...value, newValue]);
      }
      setInputValue("");
    } else if (e.key === "Backspace" && !inputValue && value.length > 0) {
      onChange(value.slice(0, -1));
    }
  };

  const removeTag = (indexToRemove: number) => {
    onChange(value.filter((_, index) => index !== indexToRemove));
  };

  return (
    <div className="border border-slate-300 rounded-lg p-3 min-h-[48px] focus-within:ring-2 focus-within:ring-primary focus-within:border-transparent transition-all duration-200">
      <div className="flex flex-wrap gap-2 mb-2">
        <AnimatePresence>
          {value.map((tag, index) => (
            <motion.span
              key={tag}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary text-white"
            >
              {tag}
              <button
                onClick={() => removeTag(index)}
                className="ml-2 hover:bg-indigo-700 rounded-full p-0.5 transition-colors"
              >
                <X className="h-3 w-3" />
              </button>
            </motion.span>
          ))}
        </AnimatePresence>
      </div>
      <Input
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="border-0 outline-none text-sm p-0 h-auto focus-visible:ring-0"
      />
    </div>
  );
}
