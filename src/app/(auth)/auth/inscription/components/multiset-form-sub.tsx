"use client"

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useMutation } from "@tanstack/react-query";


import { ProgressStepper } from "./progress-steper";
import { StepOne } from "./step-one";
import { StepTwo } from "./step-two";
import { StepThree } from "./step-three";
import { StepFour } from "./step-four";
import { CheckCircle } from "lucide-react";
import { toast } from "sonner";
import { AuthFormWrapper } from "../../components/auth-form-wrapper";
// import { useToast } from "@/hooks/use-toast";

export type UserType = "artist" | "organization" | null;

export interface FormData {
  // Account Information
  username: string;
  email: string;
  password: string;
  confirmPassword: string;

  // Identity Details - Artist
  fullName?: string;
  stageName?: string;
  danceStyles?: string[];

  // Identity Details - Organization
  organizationName?: string;
  organizationType?: string;
  contactPerson?: string;

  // Common Identity Details
  city: string;
  country: string;

  // Experience & Portfolio
  bio: string;
  experience: string;
  videoUrl?: string;

  // Files
  images?: File[];
  videoFile?: File;
}

export function MultiStepForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const [userType, setUserType] = useState<UserType>(null);
  const [formData, setFormData] = useState<FormData>({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    city: "",
    country: "",
    bio: "",
    experience: "",
    danceStyles: [],
  });
  const [isSuccess, setIsSuccess] = useState(false);

  const mutation = useMutation({
    mutationFn: async (data: { formData: FormData; userType: UserType }) => {
      const formDataToSend = new FormData();

      // Add form data as JSON
      const jsonData = {
        userType: data.userType,
        ...data.formData,
      };
      delete jsonData.images;
      delete jsonData.videoFile;

      formDataToSend.append('formData', JSON.stringify(jsonData));

      // Add files
      if (data.formData.images) {
        data.formData.images.forEach((file) => {
          formDataToSend.append('images', file);
        });
      }

      if (data.formData.videoFile) {
        formDataToSend.append('videoFile', data.formData.videoFile);
      }

      const response = await fetch('/api/register', {
        method: 'POST',
        body: formDataToSend,
        credentials: 'include',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Registration failed');
      }

      return response.json();
    },
    onError: (error) => {
      //   toast({
      //     title: "Registration Failed",
      //     description: error.message,
      //     variant: "destructive",
      //   });
    },
    onSuccess: () => {
      setIsSuccess(true);
      //   toast({
      //     title: "Registration Successful!",
      //     description: "Welcome to the community!",
      //   });
    },
  });

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const previousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const submitForm = () => {
    if (!userType) {
      //   toast({
      //     title: "Error",
      //     description: "Please select a user type",
      //     variant: "destructive",
      //   });
      return;
    }

    // mutation.mutate({ formData, userType });
  };

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0,
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 300 : -300,
      opacity: 0,
    }),
  };

  if (isSuccess) {
    return (
      <AuthFormWrapper header={{
        caption: "Deja membre?",
        link: {
          href: "/auth/connexion",
          text: "Se connecter"
        }
      }} topCol={<>
        
      </>}
        rightSide={<></>}>
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className=""
        >
          <div className="space-y-6">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="text-white text-2xl" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-fg-title mb-2">Bienvenue dans la Communauté !</h1>
              <p className="text-fg-muted">Votre inscription a été complétée avec succès.</p>
            </div>
            <button className="px-8 py-3 bg-primary text-white rounded-lg hover:bg-indigo-600 transition-colors duration-200">
              Commencer
            </button>
          </div>
        </motion.div>
      </AuthFormWrapper>
    );
  }

  return (
    <>
      <AuthFormWrapper header={{
        caption: "Deja membre?",
        link: {
          href: "/auth/connexion",
          text: "Se connecter"
        }
      }} topCol={<>
        <ProgressStepper currentStep={currentStep} />
      </>} wrapper={{box:"md:px-0"}}
        rightSide={<></>}>

          <AnimatePresence mode="wait" custom={1}>
            <motion.div
              key={currentStep}
              custom={1}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                type: "tween",
                duration: 0.3,
              }}
              className="w-full"
            >
              {currentStep === 1 && (
                <StepOne
                  userType={userType}
                  setUserType={setUserType}
                  onNext={nextStep}
                />
              )}
              {currentStep === 2 && (
                <StepTwo
                  formData={formData}
                  setFormData={setFormData}
                  onNext={nextStep}
                  onPrevious={previousStep}
                />
              )}
              {currentStep === 3 && (
                <StepThree
                  userType={userType}
                  formData={formData}
                  setFormData={setFormData}
                  onNext={nextStep}
                  onPrevious={previousStep}
                />
              )}
              {currentStep === 4 && (
                <StepFour
                  formData={formData}
                  setFormData={setFormData}
                  onPrevious={previousStep}
                  onSubmit={submitForm}
                  isLoading={mutation.isPending}
                />
              )}
            </motion.div>
          </AnimatePresence>
      </AuthFormWrapper>
    </>
  );
}
