import { motion } from "framer-motion";

interface ProgressStepperProps {
  currentStep: number;
}

export function ProgressStepper({ currentStep }: ProgressStepperProps) {
  const progress = (currentStep / 4) * 100;

  return (
    <div className="mb-8 py-3 px-5 sm:px-8 lg:px-6 xl:px-14">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-fg-muted">
          Étape {currentStep} sur 4
        </span>
        <span className="text-sm font-medium text-fg-muted">
          {Math.round(progress)}%
        </span>
      </div>
      <div className="h-2 bg-slate-200 rounded-full overflow-hidden">
        <motion.div
          className="h-full bg-gradient-to-r from-primary to-secondary rounded-full"
          initial={{ width: "25%" }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        />
      </div>
    </div>
  );
}
