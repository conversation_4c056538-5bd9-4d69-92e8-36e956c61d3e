import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

import { Textarea } from "@/components/ui/textarea";

import { Select } from "@/components/ui/select";

import type { FormData } from "./multiset-form-sub";
import { Input, Label } from "@/components/ui/field";
import { FileUpload } from "@/components/ui/file-upload";

interface StepFourProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
  onPrevious: () => void;
  onSubmit: () => void;
  isLoading: boolean;
}

export function StepFour({ formData, setFormData, onPrevious, onSubmit, isLoading }: StepFourProps) {
  const [charCount, setCharCount] = useState(formData.bio.length);

  const updateField = (field: keyof FormData, value: any) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleBioChange = (value: string) => {
    if (value.length <= 500) {
      updateField("bio", value);
      setCharCount(value.length);
    }
  };

  const isFormValid = () => {
    return formData.bio.trim() && formData.experience;
  };

  const handleSubmit = () => {
    if (isFormValid()) {
      onSubmit();
    }
  };

  return (
    <div>
      <div className="text-center mb-8">
        <h1 className="text-2xl font-semibold text-fg-title mb-2">Partagez Votre Travail</h1>
        <p className="text-fg-muted">Présentez votre expérience et votre portfolio</p>
      </div>

      <div className="space-y-6">
        <div className="form-group">
          <Textarea label="Biographie courte"
            id="bio"
            placeholder="Parlez-nous de votre parcours dans la danse..."
            value={formData.bio}
            onChange={(e) => handleBioChange(e)}
            className="resize-none"
           
          />
          <p className="text-xs text-slate-500 mt-1">
            {charCount}/500 caractères
          </p>
        </div>

        <div className="form-group">
          <Select label="Années d'expérience" placeholder="Sélectionnez votre niveau d'expérience" selectedKey={formData.experience} onSelectionChange={(value) => updateField("experience", value)}>
            <Select.Trigger  />
            <Select.List>
              <Select.Option id="0-1" textValue="Moins d'un an">Moins d'un an</Select.Option>
              <Select.Option id="1-3" textValue="1-3 ans">1-3 ans</Select.Option>
              <Select.Option id="3-5" textValue="3-5 ans">3-5 ans</Select.Option>
              <Select.Option id="5-10" textValue="5-10 ans">5-10 ans</Select.Option>
              <Select.Option id="10+" textValue="Plus de 10 ans">Plus de 10 ans</Select.Option>
            </Select.List>
          </Select>
        </div>

        <div className="form-group">
          <Label>Télécharger des images</Label>
          <FileUpload
            accept="image/*"
            multiple
            onFilesChange={(files) => updateField("images", files)}
            maxSize={10 * 1024 * 1024} // 10MB par image
          >
            Déposez vos images ici ou cliquez pour parcourir
          </FileUpload>
          {formData.images && formData.images.length > 0 && (
            <p className="text-sm text-fg-muted mt-2">
              {formData.images.length} image(s) sélectionnée(s)
            </p>
          )}
        </div>

        
      </div>

      <div className="flex gap-4 mt-8">
        <Button intent="outline" className="flex-1" onClick={onPrevious} isDisabled={isLoading}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <Button 
          className="flex-1 bg-gradient-to-r from-primary to-secondary hover:shadow-lg transform hover:scale-105 transition-all duration-200" 
          onClick={handleSubmit}
          isDisabled={!isFormValid() || isLoading}
        >
          {isLoading ? (
            "Finalisation de l'inscription..."
          ) : (
            <>
              <Check className="mr-2 h-4 w-4" />
              Terminer l'inscription
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
