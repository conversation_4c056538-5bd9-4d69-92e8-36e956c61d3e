import { motion } from "framer-motion";
import { Palette, Users } from "lucide-react";
import type { UserType } from "./multiset-form-sub";


interface StepOneProps {
  userType: UserType;
  setUserType: (type: UserType) => void;
  onNext: () => void;
}

export function StepOne({ userType, setUserType, onNext }: StepOneProps) {
  const selectUserType = (type: UserType) => {
    setUserType(type);
    setTimeout(() => {
      onNext();
    }, 500);
  };

  return (
    <div>
      <div className="text-center mb-8">
        <h1 className="text-2xl font-semibold text-fg-title mb-2">Qui êtes-vous ?</h1>
        <p className="text-fg-muted">Choisissez l'option qui vous décrit le mieux</p>
      </div>

      <div className="space-y-4">
        <motion.div
          className={`cursor-pointer border-2 rounded-xl p-6 transition-all duration-200 hover:border-primary hover:shadow-md ${userType === "artist" ? "border-primary bg-primary/5" : "border-slate-200"
            }`}
          onClick={() => selectUserType("artist")}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0 mr-4">
              <div className="w-6 h-6 border-2 border-slate-300 rounded-full flex items-center justify-center">
                <motion.div
                  className="w-3 h-3 bg-primary rounded-full"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{
                    opacity: userType === "artist" ? 1 : 0,
                    scale: userType === "artist" ? 1 : 0,
                  }}
                  transition={{ duration: 0.2 }}
                />
              </div>
            </div>
            <div className="flex-1">
              <div className="flex items-center mb-2">
                <Palette className="text-secondary text-xl mr-3" />
                <h3 className="text-lg font-medium text-slate-800">Je suis un Artiste</h3>
              </div>
              <p className="text-sm text-fg-muted">Danseur individuel, chorégraphe ou interprète</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          className={`cursor-pointer border-2 rounded-xl p-6 transition-all duration-200 hover:border-primary hover:shadow-md ${userType === "organization" ? "border-primary bg-primary/5" : "border-slate-200"
            }`}
          onClick={() => selectUserType("organization")}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0 mr-4">
              <div className="w-6 h-6 border-2 border-slate-300 rounded-full flex items-center justify-center">
                <motion.div
                  className="w-3 h-3 bg-primary rounded-full"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{
                    opacity: userType === "organization" ? 1 : 0,
                    scale: userType === "organization" ? 1 : 0,
                  }}
                  transition={{ duration: 0.2 }}
                />
              </div>
            </div>
            <div className="flex-1">
              <div className="flex items-center mb-2">
                <Users className="text-secondary text-xl mr-3" />
                <h3 className="text-lg font-medium text-slate-800">Je suis une Organisation</h3>
              </div>
              <p className="text-sm text-fg-muted">Compagnie de danse, crew, studio ou institution</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
