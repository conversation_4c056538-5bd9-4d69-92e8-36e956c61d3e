import { Arrow<PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

import { Select } from "@/components/ui/select";
import { MultiSelect } from "./multiselect";
import type { FormData, UserType } from "./multiset-form-sub";
import { Label } from "@/components/ui/field";
import { TextField } from "@/components/ui/text-field";

interface StepThreeProps {
  userType: UserType;
  formData: FormData;
  setFormData: (data: FormData) => void;
  onNext: () => void;
  onPrevious: () => void;
}

export function StepThree({ userType, formData, setFormData, onNext, onPrevious }: StepThreeProps) {
  const updateField = (field: keyof FormData, value: any) => {
    setFormData({ ...formData, [field]: value });
  };

  const isFormValid = () => {
    if (userType === "artist") {
      return formData.fullName?.trim() && formData.city.trim() && formData.country;
    } else {
      return formData.organizationName?.trim() && formData.organizationType && formData.city.trim() && formData.country && formData.contactPerson?.trim();
    }
  };

  const handleNext = () => {
    if (isFormValid()) {
      onNext();
    }
  };

  return (
    <div>
      <div className="text-center mb-8">
        <h1 className="text-2xl font-semibold text-fg-title mb-2">
          {userType === "artist" ? "Parlez-nous de vous" : "À propos de votre organisation"}
        </h1>
        <p className="text-fg-muted">
          {userType === "artist" ? "Partagez votre identité artistique" : "Parlez-nous de votre organisation de danse"}
        </p>
      </div>

      <div className="space-y-6">
        {userType === "artist" ? (
          <>
            <div className="form-group">
                <TextField label="Nom complet" type="text"
                placeholder="Entrez votre nom complet"
                value={formData.fullName || ""}
                onChange={(e) => updateField("fullName", e)}/>
              
            </div>

            <div className="form-group">
            <TextField label="Nom de scène (Optionnel)" type="text"
                placeholder="Entrez votre nom de scène"
                value={formData.stageName || ""}
                onChange={(e) => updateField("stageName", e)}/>
              
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="form-group">
                
                <TextField label="Ville" type="text"
               placeholder="Votre ville"
               value={formData.city}
               onChange={(e) => updateField("city", e)}/>
              

              </div>
              <div className="form-group">
                <Select 
                  label="Pays" 
                  placeholder="Sélectionnez un pays" 
                  selectedKey={formData.country} 
                  onSelectionChange={(value) => updateField("country", value)}
                >
                  <Select.Trigger />
                  <Select.List>
                    <Select.Option id="us" textValue="États-Unis">États-Unis</Select.Option>
                    <Select.Option id="uk" textValue="Royaume-Uni">Royaume-Uni</Select.Option>
                    <Select.Option id="ca" textValue="Canada">Canada</Select.Option>
                    <Select.Option id="au" textValue="Australie">Australie</Select.Option>
                    <Select.Option id="other" textValue="Autre">Autre</Select.Option>
                  </Select.List>
                </Select>
              </div>
            </div>

            <div className="form-group">
              <Label>Styles de danse</Label>
              <MultiSelect
                value={formData.danceStyles || []}
                onChange={(styles) => updateField("danceStyles", styles)}
                placeholder="Tapez pour ajouter des styles de danse..."
              />
              <p className="text-xs text-slate-500 mt-1">Appuyez sur Entrée pour ajouter des styles</p>
            </div>
          </>
        ) : (
          <>
            <div className="form-group">
              <TextField label="Nom de l'organisation" type="text"
               placeholder="Entrez le nom de l'organisation"
                value={formData.organizationName || ""}
                onChange={(e) => updateField("organizationName", e)}/>
            </div>

            <div className="form-group">
              <Select 
                label="Type d'organisation"
                placeholder="Sélectionnez un type"
                selectedKey={formData.organizationType}
                onSelectionChange={(value) => updateField("organizationType", value)}
              >
                <Select.Trigger />
                <Select.List>
                  <Select.Option id="company" textValue="Compagnie de danse">Compagnie de danse</Select.Option>
                  <Select.Option id="crew" textValue="Crew de danse">Crew de danse</Select.Option>
                  <Select.Option id="studio" textValue="Studio de danse">Studio de danse</Select.Option>
                  <Select.Option id="school" textValue="École de danse">École de danse</Select.Option>
                  <Select.Option id="ngo" textValue="Organisation à but non lucratif">Organisation à but non lucratif</Select.Option>
                  <Select.Option id="other" textValue="Autre">Autre</Select.Option>
                </Select.List>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="form-group">
                <TextField 
                  label="Ville"
                  type="text"
                  placeholder="Ville de l'organisation"
                  value={formData.city}
                  onChange={(e) => updateField("city", e)}
                />
              </div>
              <div className="form-group">
                <Select 
                  label="Country" 
                  placeholder="Select country" 
                  selectedKey={formData.country} 
                  onSelectionChange={(value) => updateField("country", value)}
                >
                  <Select.Trigger />
                  <Select.List>
                    <Select.Option id="us" textValue="United States">United States</Select.Option>
                    <Select.Option id="uk" textValue="United Kingdom">United Kingdom</Select.Option>
                    <Select.Option id="ca" textValue="Canada">Canada</Select.Option>
                    <Select.Option id="au" textValue="Australia">Australia</Select.Option>
                    <Select.Option id="other" textValue="Other">Other</Select.Option>
                  </Select.List>
                </Select>
              </div>
            </div>

            <div className="form-group">
              <TextField 
                label="Personne à contacter"
                type="text"
                placeholder="Nom du contact principal"
                value={formData.contactPerson || ""}
                onChange={(e) => updateField("contactPerson", e)}
              />
            </div>
          </>
        )}
      </div>

      <div className="flex gap-4 mt-8">
        <Button intent="outline" className="flex-1" onClick={onPrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <Button className="flex-1" onClick={handleNext} isDisabled={!isFormValid()}>
          Suivant
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
