import { useState } from "react";
import { <PERSON><PERSON><PERSON>t, ArrowRight, Eye, EyeOff } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

import type { FormData } from "./multiset-form-sub";
import { TextField } from "@/components/ui/text-field";
import Image from "next/image";

interface StepTwoProps {
    formData: FormData;
    setFormData: (data: FormData) => void;
    onNext: () => void;
    onPrevious: () => void;
}

export function StepTwo({ formData, setFormData, onNext, onPrevious }: StepTwoProps) {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});

    const validateForm = () => {
        const newErrors: Record<string, string> = {};

        if (!formData.username.trim()) {
            newErrors.username = "Le nom d'utilisateur est requis";
        }

        if (!formData.email.trim()) {
            newErrors.email = "L'email est requis";
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = "Veuillez entrer un email valide";
        }

        if (!formData.password) {
            newErrors.password = "Le mot de passe est requis";
        } else if (formData.password.length < 6) {
            newErrors.password = "Le mot de passe doit contenir au moins 6 caractères";
        }

        if (!formData.confirmPassword) {
            newErrors.confirmPassword = "Veuillez confirmer votre mot de passe";
        } else if (formData.password !== formData.confirmPassword) {
            newErrors.confirmPassword = "Les mots de passe ne correspondent pas";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleNext = () => {
        if (validateForm()) {
            onNext();
        }
    };

    const updateField = (field: keyof FormData, value: string) => {
        setFormData({ ...formData, [field]: value });
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors({ ...errors, [field]: "" });
        }
    };

    return (
        <div>
            <div className="text-center mb-8">
                <h1 className="text-2xl font-semibold text-fg-title mb-2">Créez Votre Compte</h1>
                <p className="text-fg-muted">Configurez vos identifiants de connexion</p>
            </div>

            <div className="space-y-4">
                <div className="grid">
                    <Button intent="outline" className="w-full flex items-center justify-center gap-2 ">
                        <Image src="/google.svg" alt="google logo" width={100} height={100} className="w-5 h-5" />
                        <span className="text-fg-subtext">Continuer avec google</span>
                    </Button>
                </div>
                <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-border"></div>
                    </div>
                    <div className="relative flex justify-center text-sm">
                        <span className="px-2 bg-bg text-gray-500">Ou</span>
                    </div>
                </div>
            </div>

            <div className="space-y-6">
                <div className="form-group">
                    <TextField
                        label="Nom d'utilisateur"
                        type="text"
                        placeholder="Entrez votre nom d'utilisateur"
                        value={formData.username || ""}
                        onChange={(e) => updateField("username", e)}
                    />
                    {errors.username && <p className="text-red-500 text-sm mt-1">{errors.username}</p>}
                </div>

                <div className="form-group">
                    <TextField
                        label="Adresse Email"
                        type="email"
                        placeholder="Entrez votre email"
                        value={formData.email || ""}
                        onChange={(e) => updateField("email", e)}
                    />
                    {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                </div>

                <div className="relative">
                    <div className="relative">
                        <TextField
                            label="Mot de passe"
                            type={showPassword ? "text" : "password"}
                            placeholder="Créez un mot de passe"
                            value={formData.password}
                            onChange={(e) => updateField("password", e)}
                            className={errors.password ? "border-red-500" : ""}
                        />
                        <Button
                            type="button"
                            intent="plain"
                            size="small"
                            className="absolute right-2 top-1/2 -translate-y-1/2 h-auto p-1"
                            onClick={() => setShowPassword(!showPassword)}
                        >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                    </div>
                    {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password}</p>}
                </div>

                <div className="relative">
                    <div className="relative">
                        <TextField
                            label="Confirmer le mot de passe"
                            type={showConfirmPassword ? "text" : "password"}
                            placeholder="Confirmez votre mot de passe"
                            value={formData.confirmPassword}
                            onChange={(e) => updateField("confirmPassword", e)}
                            className={errors.confirmPassword ? "border-red-500" : ""}
                        />
                        <Button
                            type="button"
                            intent="plain"
                            size="small"
                            className="absolute right-2 top-2 h-auto p-1"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                            {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                    </div>
                    {errors.confirmPassword && <p className="text-red-500 text-sm mt-1">{errors.confirmPassword}</p>}
                </div>
            </div>

            <div className="flex gap-4 mt-8">
                <Button intent="outline" className="flex-1" onClick={onPrevious}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Retour
                </Button>
                <Button className="flex-1" onClick={handleNext}>
                    Suivant
                    <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
            </div>
        </div>
    );
}
