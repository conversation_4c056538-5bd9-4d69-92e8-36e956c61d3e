import { UserPlus } from "lucide-react";
import { AuthFormWrapper } from "../components/auth-form-wrapper";
import Image from "next/image";
import { RegisterForm } from "./register-form";
import { getAppServerSession } from "@/actions/session";
import { redirect } from "next/navigation";
import { Button } from "@/components/ui/button";

export default async function RegisterPage() {
  const session = await getAppServerSession()
  
  if(session?.user){
    redirect("/")
  }

  return (
    <AuthFormWrapper header={{
      caption: "Already have an account?",
      link: {
        href: "/login",
        text: "Sign in"
      }
    }} rightSide={<></>}>
      <>
        {/* Welcome Text */}
        <div className="mb-10 flex text-center flex-col items-center space-y-2">
          <div className="size-20 bg-gradient-to-b from-bg-high/60 to-transparent rounded-full p-2">
            <span className="size-full border border-border/50 rounded-full bg-bg flex items-center justify-center">
              <UserPlus/>
            </span>
          </div>
          <h1 className="text-lg font-semibold text-fg-title">Create Account</h1>
          <p className="text-fg-subtle">Join our community today</p>
        </div>

        {/* Social Login */}
        <div className="space-y-4">
          <div className="grid">
            <Button intent="outline" className="w-full flex items-center justify-center gap-2">
              <Image src="/google.svg" alt="google logo" width={100} height={100} className="w-5 h-5" />
              <span className="text-fg-subtext">Continue with Google</span>
            </Button>
          </div>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-border"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-bg text-gray-500">Or</span>
            </div>
          </div>
        </div>

        {/* Register Form */}
        <RegisterForm/>
      </>
    </AuthFormWrapper>
  )
} 