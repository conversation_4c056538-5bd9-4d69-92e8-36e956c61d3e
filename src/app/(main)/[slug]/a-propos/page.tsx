import { artisteProfiles } from "@/data/artiste-profiles";
import { notFound } from "next/navigation";
import { ProfileWrapperLayout } from "../components/profile-wrapper-layout";
import Link from "next/link";
import { MapPin, User } from "lucide-react";
import { Badge } from "@/components/ui/badge";

type Props = {
  params: Promise<{ slug: string }>
}

export default async function ArtisteProfileAboutPage({ params }: Props) {
  const { slug } = await params;
  const artiste = artisteProfiles.find(a => a.username === slug);
  if (!artiste) {
    notFound();
  }
  return (
    <>
      <ProfileWrapperLayout artiste={artiste}>
        <div className="space-y-6 pt-3">
          <div className="bg-bg border border-border/70 p-5 sm:p-8 lg:p-10 rounded-xl divide-y divide-border/80 *:py-6 *:first:pt-0 *:last:pb-0">
            <div>
              <h2 className="text-lg font-semibold text-fg-title">
                Biographie
              </h2>
              <p className="text-fg mt-4">
                {artiste.about.biography}
              </p>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-fg-title">Compétences & Expertise</h2>
              <div className="flex flex-wrap gap-2.5 mt-5">
                {artiste.skills.map((skill, index) => (
                  <Badge key={index} intent="none" className="rounded-lg bg-primary-950 dark:bg-primary-50 text-primary-50 dark:text-primary-900">
                    {skill}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
          <div className="bg-bg border border-border/70 p-5 sm:p-8 lg:p-10 rounded-xl">
            <h2 className="text-lg font-semibold text-fg-title">
              Expériences
            </h2>
            <div className="mt-6 divide-y divide-border/80 *:py-6 *:first:pt-0 *:last:pb-0">
              {
                artiste.experiences.map((experience, index) => (
                  <div key={index} className="flex flex-col">
                    <div className="flex mb-4">
                      <p className="text-sm font-semibold">{experience.title}</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="col-span-full flex justify-between items-center">
                        <div className="flex items-center gap-2 flex-1">
                          <div className="w-5 h-5 bg-gray-200 dark:bg-gray-800 rounded flex items-center justify-center text-[10px]">
                            {experience.organization[0]}
                          </div>
                          <p className="text-sm sm:text-base text-fg">{experience.organization}</p>
                        </div>
                        <span className="flex items-center text-fg-muted text-sm">
                          <MapPin strokeWidth={1.2} className="size-4 mr-0.5" />
                          {experience.location || 'Non renseigné'}
                        </span>
                      </div>


                      <div>
                        <p className="text-sm text-fg-muted mb-1">Type de contrat</p>
                        <p className="text-sm sm:text-base text-fg">
                          {experience.contractType || 'Non renseigné'}
                        </p>
                      </div>

                      <div>
                        <p className="text-sm text-fg-muted mb-1">Periode</p>
                        <p className="text-sm sm:text-base text-fg">
                          De {experience.startDate} à {experience.endDate}
                        </p>
                      </div>

                      <div className="col-span-full">
                        <p className="text-sm text-fg-muted mb-1">Description, missions</p>
                        <p className="text-sm sm:text-base text-balance text-fg">
                          {experience.description || 'Non renseigné'}
                        </p>
                      </div>
                      <div className="col-span-full flex flex-wrap gap-1.5">
                        {
                          experience.skills.map((skill, index) => (
                            <Badge key={`index-${index}-${[...skill].join(' ')}`} intent="none" className="rounded-lg bg-primary-950 dark:bg-primary-50 text-primary-50 dark:text-primary-900 mr-2 mb-2 inline-block">
                              {skill}
                            </Badge>
                          ))
                        }
                      </div>
                    </div>
                  </div>
                ))
              }
            </div>
          </div>
        </div>
      </ProfileWrapperLayout>
    </>
  )
}
