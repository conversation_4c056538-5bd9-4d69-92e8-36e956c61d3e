import { artisteProfiles } from "@/data/artiste-profiles";
import { notFound } from "next/navigation";
import { ProfileWrapperLayout } from "../components/profile-wrapper-layout";

import { reviewData, testimonials } from '@/data/reviews';
import ReviewSummary from "@/components/review/review-summary";
import { TestimonialCard } from "@/components/review/testimonial-card";

type Props = {
  params: Promise<{ slug: string }>
}

export default async function ArtisteProfileReviewsPage({ params }: Props) {
  const { slug } = await params;
  const artiste = artisteProfiles.find(a => a.username === slug);
  if (!artiste) {
    notFound();
  }
  return (
    <>
      <ProfileWrapperLayout artiste={artiste}>
        <div className="grid">
          <ReviewSummary
            totalReviews={reviewData.totalReviews}
            averageRating={reviewData.averageRating}
            ratingDistribution={reviewData.ratingDistribution}
            ratingCategories={reviewData.ratingCategories}
          />
          <div className="mt-8 column-1 sm:columns-2 *:break-inside-avoid space-y-4 gap-4">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard
                key={index}
                note={testimonial.note}
                quote={testimonial.quote}
                description={testimonial.description}
                author={testimonial.author}
              />
            ))}
          </div>
        </div>
      </ProfileWrapperLayout>
    </>
  )
}
