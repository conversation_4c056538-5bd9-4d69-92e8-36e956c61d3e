"use client"

import { getCurrentUser } from "@/actions/auth"
import { App_User } from "@/lib/fakeAuthStore"
import { useEffect, useState } from "react"
import type { ReactNode } from "react"

async function getCurrentUserClient() {
    return await getCurrentUser()
}

export const ActionOwner = ({
    notOwner,
    children,
    slug,
    orgUser,
}: {
    notOwner: ReactNode
    children: ReactNode
    slug: string
    orgUser?: ReactNode
}) => {
    const [user, setUser] = useState<App_User | null>(null)
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        const fetchUser = async () => {
            try {
                const userData = await getCurrentUserClient()
                setUser(userData)
            } catch (error) {
                console.error("Failed to fetch user:", error)
                setUser(null)
            } finally {
                setLoading(false)
            }
        }
        fetchUser()
    }, [])

    if (!user) return <>{notOwner}</>

    if (user.type === "org" && orgUser) return <>{orgUser}</>

    if (user.type === "artiste" && user.artisteData.username === slug) {
        return <>{children}</>
    }

    return <></>
}
