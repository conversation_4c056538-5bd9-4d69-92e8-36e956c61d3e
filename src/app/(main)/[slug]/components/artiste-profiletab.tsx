"use client"

import { BookOpenText, FileVideo, Image, Notebook, SendToBack } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

const items = [
    { id: 1, name: "<PERSON><PERSON><PERSON>", href: "", icon: <SendToBack strokeWidth={1.2} className="size-4" /> },
    { id: 2, name: "Images", href: "/images", icon: <Image strokeWidth={1.2} className="size-4" /> },
    { id: 3, name: "Videos", href: "/videos", icon: <FileVideo strokeWidth={1.2} className="size-4" /> },
    { id: 4, name: "Avis", href: "/avis", icon: <Notebook strokeWidth={1.2} className="size-4" /> },
    { id: 5, name: "A propos", href: "/a-propos", icon: <BookOpenText strokeWidth={1.2} className="size-4" /> },
]
export default function ArtisteProfileTab({ slug }: { slug: string }) {
    const pathname = usePathname()

    return (
        <div className="my-5 w-full overflow-hidden border-y border-border/70 px-2 sm:px-0">
            <ul className="flex items-center justify-center sm:justify-start gap-0.5 text-fg-muted w-full">
                {
                    items.map(item => {
                        const isActive = pathname === `/${slug}${item.href}`
                        return <li key={item.id} className="flex-1 flex text-center">
                            <Link
                                data-state={isActive ? "active" : ""} href={`/${slug}${item.href}`} className={"flex flex-col sm:flex-row w-full justify-center text-center items-center gap-0.5 sm:gap-2 px-0.5 py-1.5 hover:bg-bg-surface-muted data-[state=active]:text-fg-title hover:text-fg-title border-b-2 border-transparent data-[state=active]:border-fg-title"}>
                                {item.icon}
                                <span className="max-[360px]:hidden inline-flex text-nowrap text-xs sm:text-sm md:text-base">
                                    {item.name}
                                </span>
                            </Link>
                        </li>
                    })
                }
            </ul>
        </div>
    )
}
