"use client"

import { buttonStyles } from "@/components/ui/button"
import { Menu } from "@/components/ui/menu"
import { IconDotsHorizontal } from "@intentui/icons"
import { BookmarkPlus, OctagonAlert } from "lucide-react"

export const MoreOptionsProfile = () => {
    return (
        <>
            <Menu>
                <Menu.Trigger className={buttonStyles({ size: "square-petite", intent: "outline", className:"rounded-xl" })}>
                    <IconDotsHorizontal className="size-4" />
                </Menu.Trigger>
                <Menu.Content placement="bottom" className="w-dvw sm:w-auto">
                    <Menu.Item>
                        <OctagonAlert className="size-4 mr-2" strokeWidth={1.2}/>
                        <Menu.Label>
                            Signaler
                        </Menu.Label>
                    </Menu.Item>
                    <Menu.Item>
                        <BookmarkPlus className="size-4 mr-2" strokeWidth={1.2}/>
                        <Menu.Label>
                            Ajouter a la liste
                        </Menu.Label>
                    </Menu.Item>
                </Menu.Content>
            </Menu>
        </>
    )
}
