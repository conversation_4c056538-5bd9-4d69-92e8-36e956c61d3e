import { Badge } from '@/components/ui/badge'
import { ArtisteProfile } from '@/types/artiste-profile'
import { BrickWallFire, Layers2, MapPin, Settings, Star, Tag, Timer } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import { MoreOptionsProfile } from './more-options-profile'
import { ShareOptionsProfile } from '@/components/artiste/options-share-profile'

import { GetStartedChat } from '@/components/artiste/get-started-chat'
import { Button } from '@/components/ui/button'
import { genres } from '@/data/genres'

export const ArtisteProfileHeader = ({ artiste, isCurrent: is_current_user }: { artiste: ArtisteProfile, isCurrent: boolean }) => {

    return (
        <>
            <header className="sm:rounded-xl sm:p-1 sm:bg-bg sm:border border-border/70">
                <div className="h-40 sm:rounded-lg bg-primary-50 overflow-hidden relative">
                    <Image
                        src={`${artiste.mediaCover}`}
                        alt={"Cover app profile"}
                        width={1000}
                        height={1000} className="size-full object-cover sm:rounded-lg" />
                    <span className="absolute inset-0 hidden dark:flex bg-gradient-to-b from-primary-950 via-primary-400/20 to-transparent"></span>
                </div>
                <div className="px-3 sm:px-4 md:px-5 -mt-16 relative flex items-end justify-between">
                    <div className="flex justify-center sm:justify-start flex-1">
                        <div className="size-24 overflow-hidden rounded-full border-4 border-bg">
                            <Image
                                src={artiste.avatar}
                                width={200}
                                height={200}
                                alt={"artistName"}
                                className="size-full object-cover"
                            />
                        </div>
                    </div>
                    <div className="absolute right-1 bottom-1 sm:relative">
                        <ShareOptionsProfile />
                    </div>
                </div>
                <div className="text-center sm:text-left px-4 md:px-5 pb-4 md:pb-5 mt-3">
                    <div className='flex flex-wrap items-center justify-center sm:justify-start'>
                        <h1 className='font-semibold text-fg-title'>
                            {artiste.about.firstName} {artiste.about.lastName}
                        </h1>
                        <span className="mx-2.5 size-1 rounded-full bg-fg flex"></span>
                        <span className='text-sm text-fg-muted'>
                            @{artiste.username}
                        </span>
                    </div>
                    <h3 className='mt-1 text-fg text-sm'>
                        {artiste.about.title}
                    </h3>
                    <div className="mt-3.5 flex items-center justify-center sm:justify-start flex-wrap gap-x-5 gap-y-2.5">
                        <Badge intent="none" className="bg-primary text-fg-primary-btn w-max rounded-xl">
                            <Tag strokeWidth={1.2} className='size-4' />
                            {genres.find(genre => genre.slug === artiste.genre)?.name}
                        </Badge>
                        <Link href={`/${artiste.username}/avis`} className="flex items-center hover:underline text-sm text-fg-muted">
                            <Star strokeWidth={0} className="size-4 fill-amber-500 mr-0.5" />
                            {artiste.rating}
                        </Link>
                        <span className='flex items-center text-sm text-fg-muted'>
                            <BrickWallFire strokeWidth={1.2} className='size-4 mr-0.5' />
                            {artiste.completedPrestations} prestation{artiste.completedPrestations > 1 ? "s" : ""}
                        </span>
                        <span className='text-sm text-fg-muted flex items-center'>
                            <MapPin className='size-4 mr-0.5' strokeWidth={1.2} />
                            {artiste.about.city}
                        </span>

                        <span className='text-sm text-fg-muted flex items-center'>
                            <Timer className='size-4 mr-0.5' strokeWidth={1.2} />
                            {artiste.quickResponse ? 'Réponse rapide' : `${artiste.responseTime} min`}
                        </span>
                    </div>
                    <div className="mt-5 flex items-center justify-center sm:justify-start gap-2">
                        {
                            is_current_user ?
                                (
                                    <>
                                        <Link href={"/mon-espace"}>
                                            <Button size="small" className={""}>
                                                <Layers2 strokeWidth={1.2} className="mr-0.5 size-4" />
                                                Mon Espace
                                            </Button>
                                        </Link>

                                        <Link href={"/parametres"}>
                                            <Button size="small" intent="outline" className={""}>
                                                <Settings strokeWidth={1.2} className="mr-0.5 size-4" />
                                                Parametres
                                            </Button>
                                        </Link>

                                    </>
                                ) :
                                <>
                                    <GetStartedChat btnClass="rounded-xl" />
                                    <MoreOptionsProfile />
                                </>
                        }
                    </div>
                </div>
            </header>
        </>
    )
}
