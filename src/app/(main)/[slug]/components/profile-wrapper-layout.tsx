import { ArtisteProfile } from '@/types/artiste-profile'
import React, { ReactNode } from 'react'
import ArtisteProfileTab from './artiste-profiletab'
import { ArtisteProfileHeader } from './profile-header'
import { isCurrentUser } from '@/actions/auth'
import { Button } from '@/components/ui/button'
import { ImageDown } from 'lucide-react'
import { ShowProfileCalendar } from './show-profile-calendar'

export const ProfileWrapperLayout = async ({ artiste, children }: { artiste: ArtisteProfile, children: ReactNode }) => {
    const is_current_user = await isCurrentUser(artiste.username)

    return (
        <>
        <main className="app-container sm:px-6 lg:px-4 sm:mt-6">
            <div className="grid lg:grid-cols-[1fr_350px] gap-10 w-full mx-auto lg:max-w-7xl">
                <div className="grid">
                    <ArtisteProfileHeader isCurrent={is_current_user} artiste={artiste} />

                    <ArtisteProfileTab slug={artiste.username} />
                    <div className="px-4 sm:px-0 w-full grid">
                        {children}
                    </div>
                </div>
                <div className="">
                    <div className="h-max lg:sticky lg:top-[88px]">
                        <ShowProfileCalendar slug={artiste.username} />
                    </div>
                </div>
            </div>
            {
                is_current_user ?
                    <div className="fixed bottom-16 right-0 lg:right-40">
                         <Button size="square-petite" className={"size-10 rounded-full"}>
                                <ImageDown strokeWidth={1.2} className='size-4' />
                            </Button>
                    </div> : null
            }
        </main>
        </>
    )
}
