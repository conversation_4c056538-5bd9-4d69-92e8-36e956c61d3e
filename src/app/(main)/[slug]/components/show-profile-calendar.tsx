"use client"

import { AppCalendar } from '@/components/app-calendar/Calendar'
import { useState } from 'react'
import { ActionOwner } from './action-owner'
import AvailabilityForm from '@/components/app-calendar/AvailabilityForm'
import { BookNewSlot } from '@/components/app-calendar/book-new-slot'

export const ShowProfileCalendar = ({ slug }: { slug: string }) => {
    const [isOpen, setIsOpen] = useState(false)
    const [selectedDate, setSelectedDate] = useState<{ day: Date, status: string } | null>(null)
    return (
        <>
            <AppCalendar
                gridCellClass={"aspect-square"}
                availabilityData={[
                    {
                        date: '2025-03-15',
                        status: 'available',
                        id: '1',
                        isRecurrent: true,
                        recurrence: {
                            frequency: 'weekly',
                            daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
                            endDate: '2025-06-15'
                        }
                    },
                    {
                        date: '2025-03-16',
                        status: 'booked',
                        id: '2'
                    }
                ]}

                onDateSelect={(Date, status) => {
                    setSelectedDate({ day: Date, status: status! })
                    setIsOpen(true)
                }}
            />

            <ActionOwner notOwner={<></>} orgUser={<>
                <BookNewSlot isOpen={isOpen} onClose={function (): void {
                    setIsOpen(false)
                }} />
            </>} slug={slug}>
                <>
                    {
                        selectedDate?.day ?
                            <AvailabilityForm isOpen={isOpen} onClose={() => setIsOpen(false)} selectedDate={selectedDate?.day!} artistId={slug} />
                            : null
                            }
                            </>
            </ActionOwner>
        </>
    )
}
