
import { notFound } from 'next/navigation'
import { artisteProfiles } from '@/data/artiste-profiles'
import { ProfileWrapperLayout } from './components/profile-wrapper-layout'

import { Mediabox } from '@/components/media-box/media-box'

type Props = {
  params: Promise<{ slug: string }>
}

export default async function ArtisteProfilePage({ params }: Props) {
  const { slug } = await params;
  const artiste = artisteProfiles.find(a => a.username === slug);
  if (!artiste) {
    notFound();
  }
  return (
    <ProfileWrapperLayout artiste={artiste}>
      <>
        <Mediabox gridClass="grid grid-cols-2 md:grid-cols-3 gap-2 lg:gap-3" data={artiste.marketingMedia} />

        {/* <div className="">
          <div className="aspect-[4/5] overflow-hidden rounded-md cursor-pointer">
            <Image
              src={"/avatar_def1.webp"}
              width={800}
              height={1600}
              alt={`${artiste.username} - Image `}
              className="size-full object-cover hover:scale-110 ease-linear duration-200"
            />
          </div>
        </div> */}
      </>
    </ProfileWrapperLayout>
  );
}
