import { ProfileWrapperLayout } from "../components/profile-wrapper-layout";
import { artisteProfiles } from "@/data/artiste-profiles";
import { notFound } from "next/navigation";
import { Mediabox } from "@/components/media-box/media-box";

type Props = {
  params: Promise<{ slug: string }>
}

export default async function ArtisteProfileImagesPage({ params }: Props) {
  const { slug } = await params;
  const artiste = artisteProfiles.find(a => a.username === slug);
  const videos = artiste?.marketingMedia.filter(m => m.type === 'video') || [];
  if (!artiste) {
    notFound();
  }
  return (
    <>
      <ProfileWrapperLayout artiste={artiste}>
        <Mediabox gridClass="grid grid-cols-3 md:grid-cols-4 gap-2 lg:gap-3" data={videos} />
      </ProfileWrapperLayout>
    </>
  );
}
