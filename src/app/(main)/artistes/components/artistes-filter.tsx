"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { Label } from "@/components/ui/field"
import { Modal } from "@/components/ui/modal"
import { Radio, RadioGroup } from "@/components/ui/radio"
import { Select } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { cities } from "@/data/cities"
import { useBreakpoints } from "@/hooks/use-breakpoints"
import { CloudLightning, ListFilter, Shield, Timer } from "lucide-react"
import { useQueryState } from "nuqs"
import { useState } from "react"



export const ArtistesFilterBox = () => {
  const [filterMode, setFilterMode] = useState<"global" | "disponibility">("global")
  const { isMobile } = useBreakpoints()
  const [, setRecherche] = useQueryState("recherche", {})
  const [, setGenre] = useQueryState("genre", {})
  const [localisation, setLocalisation] = useQueryState("localisation", { defaultValue: "" })

  const clearFilter = () => {
    setRecherche("")
    setGenre("")
    setLocalisation("")
  }
  return (
    <div className="relative w-max min-w-max">
      <Modal >
        <Button className={"max-sm:px-0 max-sm:size-9 justify-center"} size="small" intent="outline" >
          <ListFilter strokeWidth={1.2} className="size-4" />
          <span className="hidden sm:flex">Filtre</span>
        </Button>
        <Modal.Content isBlurred size="xl">
          <Modal.Header>
            <Modal.Title>Filtre</Modal.Title>
            <div className="flex items-center gap-2">
              <Button onPress={() => setFilterMode("global")} data-state={filterMode === "global" ? "active" : "inactive"} intent="none" className={"h-max py-0.5 text-sm px-3 rounded-xl hover:bg-muted data-[state=active]:bg-muted border border-transparent data-[state=active]:border-border"}>
                Global
              </Button>
              <Button onPress={() => setFilterMode("disponibility")} data-state={filterMode === "disponibility" ? "active" : "inactive"} intent="none" className="h-max py-0.5 text-sm px-3 rounded-xl hover:bg-muted data-[state=active]:bg-muted border border-transparent data-[state=active]:border-border">
                Disponibilite
              </Button>
            </div>
          </Modal.Header>
          <Modal.Body className="pb-3">
            {
              filterMode === "global" ? (
                <div className="pt-1 space-y-7">
                  <div className="flex flex-col space-y-2">
                    <Label htmlFor="location">
                      Ville
                    </Label>
                    <Select  onSelectionChange={e=>setLocalisation(e?.toString() ?? "")} defaultSelectedKey={localisation} placeholder="Selectionner une ville">
                      <Select.Trigger />
                      <Select.List items={cities}>
                        {(item) => (
                          <Select.Option id={item.slug} key={item.id} textValue={item.name}>
                            {item.name}
                          </Select.Option>
                        )}
                      </Select.List>
                    </Select>
                  </div>
                  <div className="grid sm:grid-cols-2 gap-8">
                    <div>
                      <span className="font-semibold text-fg-title">
                        Type d'artiste
                      </span>
                      <RadioGroup className={"mt-2 gap-4"}>
                        <Radio value="fs">Danceur</Radio>
                        <Radio value="dr">Choreographer</Radio>
                        <Radio value="ss">Animator</Radio>
                        <Radio value="s">Performer</Radio>
                      </RadioGroup>
                    </div>
                    <div>
                      <span className="font-semibold text-fg-title">
                        Competences
                      </span>
                      <RadioGroup className={"mt-2 grid grid-cols-2 gap-4"}>
                        <Radio value="fs">Contemporary</Radio>
                        <Radio value="dr">Ballroom</Radio>
                        <Radio value="ss">Jazz</Radio>
                        <Radio value="s">Hip Hop</Radio>
                      </RadioGroup>
                    </div>
                  </div>
                  <div>
                    <span className="font-semibold text-fg-title">
                      Options
                    </span>
                    <div className="mt-2 flex flex-col gap-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="prefered_location" className="text-sm text-fg flex items-center font-medium select-none">
                          <Timer strokeWidth={1.2} className="size-4 mr-1.5" />
                          Reponse Rapide
                        </Label>
                        <Switch id="prefered_location" />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="available_" className="text-sm text-fg flex items-center font-medium select-none">
                          <CloudLightning strokeWidth={1.2} className="size-4 mr-1.5" />
                          Disponible actuellement
                        </Label>
                        <Switch id="available_" />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="prof_users" className="text-sm text-fg flex items-center font-medium select-none">
                          <Shield strokeWidth={1.2} className="size-4 mr-1.5" />
                          Professionnel
                        </Label>
                        <Switch id="prof_users" />
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  <div className="pt-1 space-y-7">
                    <DateRangePicker visibleDuration={{ months: isMobile ? 1 : 2 }} label="Date disponible" />
                    <Select onSelectionChange={e=>setLocalisation(e?.toString() ?? "")} defaultSelectedKey={localisation} label="Ville" placeholder="Selectionner une ville">
                      <Select.Trigger />
                      <Select.List items={cities}>
                        {(item) => (
                          <Select.Option key={item.id} id={item.slug} textValue={item.name}>
                            {item.name}
                          </Select.Option>
                        )}
                      </Select.List>
                    </Select>
                    <div>
                      <span className="font-semibold text-fg-title">
                        Options
                      </span>
                      <div className="mt-2 flex flex-col gap-4">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="prefered_location" className="text-sm text-fg flex items-center font-medium select-none">
                            <Timer strokeWidth={1.2} className="size-4 mr-1.5" />
                            Reponse Rapide
                          </Label>
                          <Switch id="prefered_location" />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="available_" className="text-sm text-fg flex items-center font-medium select-none">
                            <CloudLightning strokeWidth={1.2} className="size-4 mr-1.5" />
                            Disponible actuellement
                          </Label>
                          <Switch id="available_" />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="prof_users" className="text-sm text-fg flex items-center font-medium select-none">
                            <Shield strokeWidth={1.2} className="size-4 mr-1.5" />
                            Professionnel
                          </Label>
                          <Switch id="prof_users" />
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )
            }
          </Modal.Body>
          <Modal.Footer className="flex">
            <div className="flex justify-between items-center w-full">
              <div className="">
                <Button intent="none" className={"underline cursor-pointer"}>
                  Vider le filtre
                </Button>
              </div>
              <div className="flex">
                <Button onPress={() => { clearFilter() }} intent="white/dark" className="w-full">
                  Filtrer le resultat
                </Button>
              </div>
            </div>
          </Modal.Footer>
        </Modal.Content>
      </Modal>
    </div>
  )
}
