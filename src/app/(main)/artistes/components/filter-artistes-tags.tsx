"use client"

import { useQueryState } from "nuqs"
import { ScrollableUlBox } from "@/components/molecules/scrollable-ul-box"
import { Button, buttonStyles } from "@/components/ui/button"
import { Menu } from "@/components/ui/menu"
import { useBreakpoints } from "@/hooks/use-breakpoints"
import { Layers2, Tag } from "lucide-react"

export const FilterArtistesTags = ({ genres }: {
    genres: {
        id: number;
        name: string;
        slug: string;
        href: string;
    }[]
}) => {
    const { isTablet, isDesktop, isMiddle, isMobile } = useBreakpoints()
    const [genre, setGenre] = useQueryState("genre", { defaultValue: "" })
    return (
        <>
            {
                isMobile || isMiddle ? (
                    <div className="relative overflow-hidden flex w-max pr-2 justify-start sm:flex-1">
                        <Menu>
                            <Menu.Trigger className={buttonStyles({ intent: "outline", size: "extra-small", className: "size-9 justify-center" })}>
                                <Tag strokeWidth={1.2} className="size-4" />
                            </Menu.Trigger>
                            <Menu.Content className="max-md:w-dvw">
                                <Menu.Item onAction={()=>setGenre("")} isSelected={genre === ""} id={"menu-all"}>
                                    <Layers2 strokeWidth={1.2} className="size-4 mr-1" />
                                    <Menu.Label>
                                        Tous les artistes
                                    </Menu.Label>
                                </Menu.Item>
                                {
                                    genres.map(item => (
                                        <Menu.Item onAction={()=>setGenre(item.slug)} isSelected={genre === item.slug} key={item.id} id={item.id}>
                                            <div className="flex items-center justify-center">
                                                <span className="size-1.5 rounded-full bg-fg-muted/50"></span>
                                            </div>
                                            <Menu.Label>
                                                {item.name}
                                            </Menu.Label>
                                        </Menu.Item>
                                    ))
                                }
                            </Menu.Content>
                        </Menu>
                    </div>
                ) : isDesktop || isTablet ? (
                    <>
                        <ScrollableUlBox hideControlOnSmall className="flex-1 pr-5 h-full flex items-center">
                            <li className="flex h-full items-center">
                                <Button intent="none" size="none" onPress={()=>setGenre("")} data-state={genre ==="" ? "active" : "inactive"} className="flex cursor-pointer items-center hover:bg-bg-surface/70 data-[state=active]:bg-bg-surface/70 data-[state=active]:text-fg-title px-3 py-2 rounded-xl">
                                    <Layers2 strokeWidth={1.2} className="size-4 mr-1" />
                                    Tous les artistes
                                </Button>
                            </li>
                            {
                                genres.map(category => <li key={category.id} className="flex h-full items-center">
                                    <Button intent="none" size="none" onPress={()=>setGenre(category.slug)} data-state={genre===category.slug ? "active" : "inactive"} className="flex cursor-pointer items-center hover:bg-bg-surface/70 data-[state=active]:bg-bg-surface/70 data-[state=active]:text-fg-title px-3 py-2 rounded-xl">
                                        <span className="size-1.5 rounded-full bg-fg-muted/50 mr-1.5"></span>
                                        {category.name}
                                    </Button>
                                </li>)
                            }
                        </ScrollableUlBox>
                    </>
                ) : (<>
                    <div className="relative overflow-hidden flex w-max pr-2 justify-start sm:flex-1 md:hidden">
                        <span className={buttonStyles({ intent: "outline", size: "extra-small", className: "size-9 justify-center bg-bg-surface animate-pulse" })}>
                            <Tag strokeWidth={1.2} className="size-4" />
                        </span>
                    </div>
                    <ScrollableUlBox hideControlOnSmall className="flex-1 pr-5 h-full hidden md:flex items-center">
                        <>
                            <li className="flex h-full items-center">
                                <span className="flex items-center animate-pulse bg-bg-surface/70 text-transparent px-3 py-2 rounded-xl">
                                    <Layers2 strokeWidth={1.2} className="size-4 mr-1" />
                                    Tous les artistes
                                </span>
                            </li>
                            {
                                genres.map(category => <li key={category.id} className="flex h-full items-center">
                                    <span className="flex items-center animate-pulse bg-bg-surface/70 text-transparent px-3 py-2 rounded-xl">
                                        <span className="size-1.5 rounded-full bg-fg-muted/20 mr-1.5"></span>
                                        {category.name}
                                    </span>
                                </li>)
                            }
                        </>
                    </ScrollableUlBox>
                </>)
            }
        </>
    )
}
