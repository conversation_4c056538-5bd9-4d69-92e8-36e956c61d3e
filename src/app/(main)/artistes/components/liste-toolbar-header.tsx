"use client"

import { genres } from "@/data/genres"
import { TextField } from "@/components/ui/text-field"
import { FilterArtistesTags } from "./filter-artistes-tags"
import { ArtistesFilterBox } from "./artistes-filter"
import { Search } from "lucide-react"
import { useQueryState } from "nuqs"

export const ListeToolbarHeader = () => {
    const [recherche, setRecherche] = useQueryState("recherche", { defaultValue: "" });
    return (
        <>
            <div className="w-full h-14 border-b bg-bg/40 backdrop-blur-2xl pt-0.5 sticky top-16 flex items-center justify-between z-30">
                <FilterArtistesTags genres={genres} />
                <div className="relative pr-2 flex max-sm:flex-1 sm:min-w-max">
                    <Search className="size-4 absolute left-3 top-2.5 text-fg-muted/60 pointer-events-none" strokeWidth={1.2} />
                    <TextField defaultValue={recherche} onChange={e => setRecherche(e)} className='h-9 [&_input]:ps-9 min-w-[85px] w-full' placeholder="Un artiste, genre, comp...." />
                </div>
                <ArtistesFilterBox />
            </div>
        </>
    )
}
