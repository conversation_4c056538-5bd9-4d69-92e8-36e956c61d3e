"use client"

import { getFilteredArtistes } from "@/actions/operations";
import { GLOBAL_STALE_TIME } from "@/app/const";
import { CardFullArtist } from "@/components/artiste/artiste-full-card";
import { SkeletonCardFullArtiste } from "@/components/skeleton/full-artiste-skeleton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useInfiniteQuery } from "@tanstack/react-query"
import { useQueryState } from "nuqs";
import { useEffect, useState } from "react";


export const LoadClientArtistes = () => {
    const [recherche, setRecherche] = useQueryState("recherche", { defaultValue: "" });
    const [genre, setGenre] = useQueryState("genre", { defaultValue: "" })
    const [localisation, setLocalisation] = useQueryState("localisation", { defaultValue: "" })

    const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery({
        queryKey: ["artistes-with-filter", recherche, genre, localisation],
        queryFn: ({ pageParam = 1 }) => getFilteredArtistes({ search: recherche, localisation, genre, page: pageParam }),
        getNextPageParam: (lastPage, allPages) => {
            return lastPage.hasMore ? allPages.length + 1 : undefined
        },
        initialPageParam: 1,
        staleTime: GLOBAL_STALE_TIME,
    })

    const clearFilter = () => {
        setRecherche("")
        setGenre("")
        setLocalisation("")
    }

    const [totalCount, setTotalCount] = useState(data?.pages.reduce((_, page) => page.total, 0))
    useEffect(() => {
        if (data) {
            const newTotalCount = data.pages.reduce((_, page) => page.total, 0)
            setTotalCount(newTotalCount)
        }
    }, [data])
    return (
        <>
            {
                isLoading ? null : `${totalCount}` === '0' ? (
                    <>
                        <div className="flex flex-col items-center justify-center py-10 sm:py-24 text-center text-gray-500">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth={1.5}
                                stroke="currentColor"
                                className="w-16 h-16 mb-4"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"
                                />
                            </svg>
                            <p className="text-lg sm:text-xl font-semibold mb-2 text-fg-title">Aucun Artiste Trouvé</p>
                            <p className="mb-6 mx-auto max-w-md text-fg-muted text-sm">Il semble qu'aucun artiste ne correspond à vos filtres actuels. Essayez de les effacer pour voir plus d'options.</p>
                            <Button intent="secondary" size="medium" onPress={() => clearFilter()}>
                                Vider les Filtres
                            </Button>
                        </div>
                    </>
                ) : null
            }
            {isLoading ? <div className="flex items-center gap-4 justify-between pt-5">
                <span className="font-semibold text-transparent bg-bg-surface rounded-lg animate-pulse">
                    (02) Artistes
                </span>
                <div>

                </div>
            </div> :
                `${totalCount}` === '0' ? null :
                    <div className="flex items-center gap-4 justify-between pt-5">
                        <span className="font-semibold text-fg-title">
                            ({totalCount?.toString()}) Artiste{`${totalCount}` === '1' || `${totalCount}` === '0' ? '' : 's'}
                        </span>
                        <div>

                        </div>
                    </div>}
            <div className="grid lg:grid-cols-2 gap-6 mt-7 w-full">
                {
                    isLoading ? <>
                        <SkeletonCardFullArtiste />
                        <SkeletonCardFullArtiste />
                        <SkeletonCardFullArtiste />
                        <SkeletonCardFullArtiste />
                        <SkeletonCardFullArtiste />
                        <SkeletonCardFullArtiste />
                        <SkeletonCardFullArtiste />
                        <SkeletonCardFullArtiste />
                    </> : null
                }
                {
                    data?.pages.map(page => page.artistes.map((artiste, index) => (
                        <CardFullArtist key={`artiste-${artiste.id}-${index}`} {...artiste} />
                    )))
                }
            </div>
            <div className="mt-7 pb-4 flex items-center justify-center">
                {hasNextPage ? (
                    <Button intent="white/dark" size="medium" onPress={() => fetchNextPage()} isDisabled={isFetchingNextPage} className="w-max">
                        {isFetchingNextPage ? "Chargement...." : "Lire plus"}
                    </Button>
                ) : `${totalCount}` === '0' ? null : <>
                    <Badge intent="primary" className="w-full justify-center flex text-center py-3 text-sm">
                        Tous les artistes ont été affichés
                    </Badge>
                </>}
            </div>
        </>
    )
}
