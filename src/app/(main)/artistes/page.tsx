import "./../app-ui.css"

import { ListeToolbarHeader } from './components/liste-toolbar-header'
import { SearchParams } from 'nuqs/server'
import { loadSearchParams } from './search-params'
import { getAppQueryClient } from '@/lib/queryClient'
import { getFilteredArtistes } from '@/actions/operations'
import { GLOBAL_STALE_TIME } from '@/app/const'
import { dehydrate, HydrationBoundary } from '@tanstack/react-query'
import { LoadClientArtistes } from './components/load-client-artistes'
type PageProps = {
    searchParams: Promise<SearchParams>
}
export default async function ArtistesPage({ searchParams }: PageProps) {

    const { recherche, genre, localisation } = await loadSearchParams(searchParams)

    const queryClient = getAppQueryClient()

    queryClient.prefetchInfiniteQuery({
        queryKey: ['artistes-with-filter', recherche, genre, localisation],
        queryFn: () => getFilteredArtistes({ search: recherche, localisation, genre }),
        initialPageParam: 1,
        staleTime: GLOBAL_STALE_TIME,
    })


    return (
        <>
            <HydrationBoundary state={dehydrate(queryClient)}>
                <main className="w-full lg:app-container px-4 sm:px-6 lg:px-4">
                    <ListeToolbarHeader />
                    <LoadClientArtistes />
                </main>
            </HydrationBoundary>
        </>
    )
}
