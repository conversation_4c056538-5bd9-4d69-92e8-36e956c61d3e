"use client"

import { VideoPlay } from "@/components/atoms/video-play"
import { Avatar } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { type Short } from "@/data/shorts"
import { EllipsisVertical, Forward, Maximize, Play, Pause, Volume2, VolumeX } from "lucide-react"
import Link from "next/link"
import { useRef, useState } from 'react'



export const CardShortVideo = ({ short }: { short: Short }) => {
    const [isPlaying, setIsPlaying] = useState(false)
    const [isMuted, setIsMuted] = useState(true)
    const [progress, setProgress] = useState(0)
    const videoRef = useRef<HTMLVideoElement>(null)

    const togglePlay = () => {
        if (videoRef.current) {
            if (isPlaying) {
                videoRef.current.pause()
            } else {
                videoRef.current.play()
            }
            setIsPlaying(!isPlaying)
        }
    }

    const toggleMute = () => {
        if (videoRef.current) {
            videoRef.current.muted = !isMuted
            setIsMuted(!isMuted)
        }
    }

    const handleTimeUpdate = () => {
        if (videoRef.current) {
            const progress = (videoRef.current.currentTime / videoRef.current.duration) * 100
            setProgress(progress)
        }
    }

    const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (videoRef.current) {
            const bounds = e.currentTarget.getBoundingClientRect()
            const x = e.clientX - bounds.left
            const width = bounds.width
            const percentage = (x / width) * 100
            const time = (percentage / 100) * videoRef.current.duration
            videoRef.current.currentTime = time
            setProgress(percentage)
        }
    }

    return (
        <>
            <div className="group w-full aspect-[9/16] bg-bg-surface min-[500px]-xl h-full min-[500px]:h-auto lg:max-h-[calc(100%-0.5rem)] relative rounded-xl overflow-hidden">
                <VideoPlay
                    ref={videoRef}
                    src={short.mediaUrl}
                    poster={short.cover}
                    onTimeUpdate={handleTimeUpdate}
                    onEnded={() => setIsPlaying(false)}
                    playsInline
                    loop
                    muted
                    className="size-full object-cover rounded-xl"
                />
                <div className="absolute top-2.5 inset-x-2.5 flex items-center justify-between">
                    <div className='flex items-center gap-2'>
                        <Button
                            intent="none"
                            size="square-petite"
                            className={"bg-gray-800/40 text-white rounded-full size-8 justify-center flex items-center"}
                            onClick={togglePlay}
                        >
                            {isPlaying ? (
                                <Pause className="size-4" strokeWidth={1.2} />
                            ) : (
                                <Play className="size-4" strokeWidth={1.2} />
                            )}
                        </Button>
                        <Button
                            intent="none"
                            size="square-petite"
                            className={"invisible opacity-0 ease-linear duration-300 group-hover:visible group-hover:opacity-100 bg-gray-800/40 text-white rounded-full size-8 justify-center flex items-center"}
                            onClick={toggleMute}
                        >
                            {isMuted ? (
                                <VolumeX className="size-4" strokeWidth={1.2} />
                            ) : (
                                <Volume2 className="size-4" strokeWidth={1.2} />
                            )}
                        </Button>
                    </div>
                    <div className='flex items-center gap-2'>
                        <Button intent="none" size="square-petite" className={"invisible opacity-0 ease-linear duration-300 group-hover:visible group-hover:opacity-100 bg-gray-800/40 text-white rounded-full size-8 justify-center flex items-center"}>
                            <Maximize className="size-4" strokeWidth={1.2} />
                        </Button>
                    </div>
                </div>
                <div className="absolute bottom-0 inset-x-0 px-2.5 py-2 bg-gradient-to-t from-gray-900/60 backdrop-blur-[1px] via-transparent via-70% text-white h-36 flex justify-end flex-col min-[500px]:rounded-b-xl">
                    <div className="flex items-center justify-between ">
                        <Link href={`/${short.username}`} className=' flex items-center gap-2'>
                            <Avatar size="large" src={`${short.avatar}`} alt={`Avatar de ${short.username}`} />
                            <div className="flex flex-1 items-center">
                                <span className="text-sm truncate">@{short.username}</span>
                            </div>
                        </Link>
                    </div>
                    <p className="text-xs sm:text-sm pt-0.5 pb-1 text-gray-100 line-clamp-1">
                        {short.title}
                    </p>
                </div>
                <div className="invisible opacity-0 ease-linear duration-300 group-hover:visible group-hover:opacity-100 absolute inset-x-0 bottom-0 overflow-hidden min-[500px]:rounded-xl h-6 flex items-end">
                    <div
                        className="w-full flex h-1 rounded-xl bg-white/30 relative cursor-pointer"
                        onClick={handleProgressClick}
                    >
                        <div
                            className="absolute inset-y-0 left-0 bg-primary transition-all duration-100"
                            style={{ width: `${progress}%` }}
                        />
                    </div>
                </div>
                <div className="invisible opacity-0 ease-linear duration-300 group-hover:visible group-hover:opacity-100 absolute inset-y-0 right-4 gap-2 flex flex-col justify-center py-20">
                    <Button intent="none" size="square-petite" className={"bg-gray-800/40 text-white sm:bg-muted sm:hover:bg-bg-light border border-border/50 sm:text-fg rounded-full size-10 justify-center flex items-center"}>
                        <Forward className="size-4" strokeWidth={1.8} />
                    </Button>
                    <Button intent="none" size="square-petite" className={"bg-gray-800/40 text-white sm:bg-muted sm:hover:bg-bg-light border border-border/50 sm:text-fg rounded-full size-10 justify-center flex items-center"}>
                        <EllipsisVertical className="size-4" strokeWidth={1.8} />
                    </Button>
                </div>
            </div>
        </>
    )
}
