"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/field"
import { Form } from "@/components/ui/form"
import { Select } from "@/components/ui/select"
import { Search } from "lucide-react"



const selectItems = [
    { id: 'artiste', name: 'Artiste' },
    { id: 'organisation', name: 'Organisation' },
    { id: 'mission', name: 'Mission' }
]

export const ExploreFilterBox = () => {
    return (
        <Form className="w-full max-w-md p-0.5 rounded-xl bg-bg mx-auto flex items-center focus-within:border-primary border shadow-sm focus-within:shadow-primary/20">
            <Input placeholder="Rechercher des Artistes, Organismes ou Missions..." className={"flex-1 flex h-9"}/>
            <Select placeholder="" defaultSelectedKey={'artiste'} className="w-max">
                <Select.Trigger notStylled className="flex items-center max-w-28 text-fg"/>
                <Select.List placement="bottom" items={selectItems}>
                    {(item) => (
                        <Select.Option id={item.id} textValue={item.name}>
                            {item.name}
                        </Select.Option>
                    )}
                </Select.List>
            </Select>
            <Button size="extra-small" className={"size-9 ml-3"}>
                <Search className="size-4"/>
            </Button>
        </Form>
    )
}
