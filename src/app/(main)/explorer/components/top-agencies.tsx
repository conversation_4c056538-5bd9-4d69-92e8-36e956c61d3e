

"use client"

import { CardOrganization } from "@/components/molecules/card-org"
import { organisations } from "@/data/organizations"
import { cn } from "@/lib/utils"
import { Swiper, SwiperSlide } from "swiper/react"

export const TopAgencies = ({ className }: { className?: string }) => {
    return (
        <>
            <Swiper
                slidesPerView={'auto'}
                spaceBetween={0}
                className={cn("mySwiper mt-7 w-full", className)}
            >
                {
                    organisations.slice(0, 8).map(org => <SwiperSlide className="w-[320px] max-sm:max-w-[87%] sm:w-[47%] md:w-[40%] sm:max-w-[47%] md:max-w-[40%] lg:w-1/4 lg:max-w-[24%] mr-3.5" key={org.id}>
                        <CardOrganization className="flex-1 w-full" key={org.id} org={org} />
                    </SwiperSlide>)
                }
            </Swiper>
        </>
    )
}
