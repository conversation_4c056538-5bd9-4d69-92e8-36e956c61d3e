"use client"

import { shorts } from "@/data/shorts"
import { Swiper, SwiperSlide } from "swiper/react"
import { CardShortVideo } from "./cart-short-video"


export const TopTalentVideoSlides = () => {
    return (
        <>
            <Swiper
                slidesPerView={'auto'}
                spaceBetween={0}
                className="mySwiper mt-7 w-full"
            >
                {
                    shorts.slice(0, 8).map(item => <SwiperSlide className="w-[320px] max-sm:max-w-[95%] sm:w-[47%] md:w-[40%] sm:max-w-[47%] md:max-w-[40%] lg:w-1/4 lg:max-w-[24%] mr-4" key={item.id}>
                        <CardShortVideo short={item} />
                    </SwiperSlide>)
                }
            </Swiper>
        </>
    )
}
