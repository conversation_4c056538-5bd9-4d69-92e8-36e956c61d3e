import "./../app-ui.css"

import 'swiper/css';
import { Container } from "@/components/ui/container";

import Link from "next/link";
import { ExploreFilterBox } from "./components/filter-box";
import { ChevronRight, Clapperboard } from "lucide-react";
import { ArtistCard } from "@/components/artiste/artist-show-card";

import { artisteProfiles } from "@/data/artiste-profiles";
import { TopTalentVideoSlides } from "./components/top-talent-video-slides";
import { RecentMissionsSlide } from "../../../components/molecules/recent-missions-slide";
import { TopAgencies } from "./components/top-agencies";
import { drukCondFont } from '@/app/fonts';



export default function ExplorePage() {
    return (
        <main>
            <Container className="relative border-y mt-2 border-border/70 grid">
                <div className="inset-y-0 inset-x-4 md:inset-x-20 absolute border-x border-border/40 isolate">
                    <div className="absolute opacity-30 dark:opacity-70 [--grid-color:var(--color-bg-surface-muted)] dark:[--grid-color:var(--color-bg-surface)] inset-0 -z-10 h-full w-full bg-bg bg-[linear-gradient(to_right,var(--grid-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--grid-color)_1px,transparent_1px)] bg-[size:6rem_4rem]"></div>
                </div>
                <div className="md:max-w-3xl mx-auto py-12 md:py-16 space-y-6 w-full relative text-center">
                    <h1 className={`font-semibold text-fg-title text-4xl sm:text-6xl max-w-4xl mx-auto text-balance ${drukCondFont.className}`}>
                    UNE GALAXIE DE TALENTS
                    </h1>
                    <p className="text-fg-muted mx-auto max-w-md">
                        Découvrez les artistes, missions et organismes disponibles
                    </p>
                    <ExploreFilterBox />
                </div>
            </Container>
            <Container className="mt-10 gap-y-16 grid w-full overflow-hidden">
                <section className="flex flex-col w-full overflow-hidden">
                    <div className="flex justify-between items-center">
                        <span className="font-semibold text-fg-title">
                            Opportunités recentes
                        </span>
                        <Link href="/opportunites" className="flex items-center gap-2 text-sm text-fg-primary">
                            Voir tout
                            <ChevronRight strokeWidth={1.2} className="size-4" />
                        </Link>
                    </div>
                    <RecentMissionsSlide />
                </section>
                <section className="flex flex-col overflow-hidden">
                    <div className="flex justify-between items-center">
                        <span className="font-semibold text-fg-title">
                            Talents exceptionnels
                        </span>
                        <Link href="/opportunites" className="flex items-center gap-2 text-sm text-fg-primary">
                            Voir tout
                            <ChevronRight strokeWidth={1.2} className="size-4" />
                        </Link>
                    </div>
                    <div className="flex overflow-hidden w-full overflow-x-auto lg:overflow-x-hidden lg:grid lg:grid-cols-3 xl:grid-cols-4 gap-2 sm:gap-2.5 mt-7">
                        {
                            artisteProfiles.slice(0, 8).map((artiste, index) =>
                                <ArtistCard className="flex-1 min-w-[88%] sm:min-w-[40%] md:min-w-[35%] lg:max-w-none md:max-w-none" key={`-artiste-${artiste.id} ${index}`}
                                    {...artiste}
                                />)
                        }
                    </div>
                </section>
                <section className="flex flex-col overflow-hidden">
                    <div className="flex justify-between items-center">
                        <span className="font-semibold text-fg-title flex items-center">
                            <Clapperboard strokeWidth={1.2} className="mr-1.5 size-4 tex" />
                            Talents brillants
                        </span>
                        <Link href="/opportunites" className="flex items-center gap-2 text-sm text-fg-primary">
                            Voir tout
                            <ChevronRight strokeWidth={1.2} className="size-4" />
                        </Link>
                    </div>
                    <TopTalentVideoSlides />
                </section>
                <section className="flex flex-col overflow-hidden">
                    <div className="flex justify-between items-center">
                        <span className="font-semibold text-fg-title">
                            Explorez les organismes
                        </span>
                        <Link href="/organismes" className="flex items-center gap-2 text-sm text-fg-primary">
                            Voir tout
                            <ChevronRight strokeWidth={1.2} className="size-4" />
                        </Link>
                    </div>
                    <TopAgencies />
                </section>
            </Container>
        </main>
    )
}