"use client"

import { VideoPlay } from '@/components/atoms/video-play'
import { Avatar } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Short } from '@/data/shorts'
import { cn } from '@/lib/utils'
import { EllipsisVertical, Forward, Maximize, Minimize, Pause, Play, Volume2, VolumeX } from 'lucide-react'
import Link from 'next/link'
import { useState, useEffect, useRef } from 'react'

export const VibeSlideItem = (props: Short & { isCurrent?: boolean, onEnd?: () => void, isMuted: boolean, isFullScreen: boolean, handleMute: () => void, setIsFullScreen: () => void }) => {
    const [isPlaying, setIsPlaying] = useState(props.isCurrent || false)
    const [progress, setProgress] = useState(0)
    const videoRef = useRef<HTMLVideoElement>(null)

    useEffect(() => {
        const videoElement = videoRef.current;
        if (!videoElement) return;

        const handlePlay = async () => {
            try {
                if (props.isCurrent) {
                    videoElement.muted = true;
                    await videoElement.play();
                    setIsPlaying(true);
                    videoElement.muted = props.isMuted
                } else {
                    videoElement.pause();
                    setIsPlaying(false);
                }
            } catch (error) {
                console.error('Error playing video:', error);
                setIsPlaying(false);
            }
        };

        handlePlay();

        return () => {
            if (videoElement) {
                // videoElement.pause();
            }
        };
    }, [props.isCurrent, props.isMuted]);

    useEffect(() => {
        if (videoRef.current) {
            videoRef.current.muted = props.isMuted;
        }
    }, [props.isMuted]);

    // Remove local isMuted state and toggleMute
    const togglePlay = () => {
        if (videoRef.current) {
            if (isPlaying) {
                videoRef.current.pause()
            } else {
                videoRef.current.play()
            }
            setIsPlaying(!isPlaying)
        }
    }

    const handleTimeUpdate = () => {
        if (videoRef.current) {
            const progress = (videoRef.current.currentTime / videoRef.current.duration) * 100
            setProgress(progress)
        }
    }

    const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (videoRef.current) {
            const bounds = e.currentTarget.getBoundingClientRect()
            const x = e.clientX - bounds.left
            const width = bounds.width
            const percentage = (x / width) * 100
            const time = (percentage / 100) * videoRef.current.duration
            videoRef.current.currentTime = time
            setProgress(percentage)
        }
    }
    const onEndPlay = () => {
        setIsPlaying(false)
        if (props.onEnd) {
            props.onEnd()
        }
    }


    return (
        <div className={cn("mx-auto group w-full bg-bg-surface relative flex items-center", {
            "aspect-auto sm:aspect-[9/16] min-[500px]:rounded-xl min-[500px]:max-w-[480px] xl:max-w-[520px] 2xl:max-w-[610px] h-full sm:max-h-[calc(100dvh-(var(--spacing)_*_3))]": props.isFullScreen,
            "h-full min-[500px]:h-auto lg:max-h-[calc(100%-0.5rem)] min-[500px]:rounded-xl min-[500px]:max-w-[420px] 2xl:max-w-[480px] aspect-auto min-[500px]:aspect-[9/16]":!props.isFullScreen
        })}>
            <div onClick={togglePlay} className="size-full z-[5]">
                <VideoPlay
                    ref={videoRef}
                    src={props.mediaUrl}
                    poster={props.cover}
                    onTimeUpdate={handleTimeUpdate}
                    onEnded={() => onEndPlay()}
                    playsInline
                    className="size-full object-cover min-[500px]:rounded-xl"
                />
            </div>
            <div className="md:invisible md:opacity-0 transition-all group-hover:opacity-100 group-hover:visible absolute top-2.5 z-20 inset-x-2.5 flex items-center justify-between">
                <div className='flex items-center gap-2'>
                    <Button
                        intent="none"
                        size="square-petite"
                        className={"bg-gray-800/40 text-white rounded-full size-10 sm:size-12 justify-center flex items-center backdrop-blur-xs cursor-pointer"}
                        onClick={togglePlay}
                    >
                        {isPlaying ? (
                            <Pause className="size-5 md:size-6" fill='currentColor' strokeWidth={1.2} />
                        ) : (
                            <Play className="size-5 md:size-6" fill='currentColor' strokeWidth={1.2} />
                        )}
                    </Button>
                    <Button
                        intent="none"
                        size="square-petite"
                        className={"bg-gray-800/40 text-white rounded-full size-10 sm:size-12 justify-center flex items-center backdrop-blur-xs cursor-pointer"}
                        onPress={props.handleMute}
                    >
                        {props.isMuted ? (
                            <VolumeX className="size-5 md:size-6"  strokeWidth={1.2} />
                        ) : (
                            <Volume2 className="size-5 md:size-6"  strokeWidth={1.2} />
                        )}
                    </Button>

                </div>
                <div className='flex items-center gap-2'>
                    <Button onPress={props.setIsFullScreen} intent="none" size="square-petite" className={"bg-gray-800/40 text-white rounded-full size-10 sm:size-12 justify-center flex items-center backdrop-blur-xs cursor-pointer"}>
                        {
                            props.isFullScreen ? (
                                <Minimize className="size-5 md:size-6" strokeWidth={1.2} />
                            ) : <Maximize className="size-5 md:size-6" strokeWidth={1.2} />
                        }
                    </Button>
                </div>
            </div>
            <div className="absolute bottom-0 inset-x-0 px-2.5 py-2 bg-gradient-to-t from-gray-900/60 backdrop-blur-[1px] via-transparent via-70% text-white h-36 flex justify-end flex-col min-[500px]:rounded-b-xl z-[8]">
                <div className="flex items-center justify-between ">
                    <Link href={`/${props.username}`} className=' flex items-center gap-2'>
                        <Avatar size="large" src={`${props.avatar}`} alt={`Avatar de ${props.username}`} />
                        <div className="flex flex-1 items-center">
                            <span className="text-sm truncate">@{props.username}</span>
                        </div>
                    </Link>
                </div>
                <p className="text-xs sm:text-sm pt-0.5 pb-1 text-gray-100 line-clamp-1">
                    {props.title} -{props.isCurrent ? "Good" : ""}
                </p>
            </div>
            <div className="absolute inset-x-0 bottom-0 overflow-hidden min-[500px]:rounded-xl h-6 flex items-end  z-[8]">
                <div style={{ ['--progress-video' as string]: `${progress}%` }} onClick={handleProgressClick} className="w-full flex h-1 hover:h-2 ease-linear duration-200 rounded-xl bg-white/30 relative before:absolute before:inset-y-0 before:left-0 before:bg-primary before:w-(--progress-video,20%) before:transition-[width] before:ease-linear"></div>
            </div>
            <div className="absolute inset-y-0 right-4 sm:-right-12 gap-2 flex flex-col justify-center sm:justify-end py-20  z-[8]">
                <Button intent="none" size="square-petite" className={"bg-gray-800/40 text-white sm:bg-muted sm:hover:bg-bg-light border border-border/50 sm:text-fg rounded-full size-10 justify-center flex items-center"}>
                    <Forward className="size-4" strokeWidth={1.8} />
                </Button>
                <Button intent="none" size="square-petite" className={"bg-gray-800/40 text-white sm:bg-muted sm:hover:bg-bg-light border border-border/50 sm:text-fg rounded-full size-10 justify-center flex items-center"}>
                    <EllipsisVertical className="size-4" strokeWidth={1.8} />
                </Button>
            </div>
        </div>
    )
}
