"use client"

import { Swiper, SwiperRef, SwiperSlide } from 'swiper/react';
import { Mousewheel, Keyboard } from 'swiper/modules';

import 'swiper/css';
import { Container } from '@/components/ui/container';
import { Button } from '@/components/ui/button';
import { ArrowDown, ArrowUp } from 'lucide-react';
import { shorts } from '@/data/shorts';
import { VibeSlideItem } from './vibe-slide-item';
import { useCallback, useRef, useState } from 'react';
import { cn } from '@/lib/utils';



export const VibeSlides = () => {
    const swiperRef = useRef<SwiperRef>(null);
    const [isBeginning, setIsBeginning] = useState(true);
    const [isEnd, setIsEnd] = useState(false);
    const [isMuted, setIsMuted] = useState(false);
    const [isFullScreen, setIsFullScreen] = useState(false);

    const handleMute = () => {
        setIsMuted(!isMuted);
    }

    const handleIsFullScreen = () => {
        setIsFullScreen(!isFullScreen)
    }

    const handlePrev = useCallback(() => {
        if (swiperRef.current) {
            swiperRef.current.swiper.slidePrev();
        }
    }, []);

    const handleNext = useCallback(() => {
        if (swiperRef.current) {
            swiperRef.current.swiper.slideNext();
        }
    }, []);

    const handleSlideChange = () => {
        if (swiperRef.current) {
            setIsBeginning(swiperRef.current.swiper.isBeginning);
            setIsEnd(swiperRef.current.swiper.isEnd);
        }
    };

    return (
        <Container constrained className={cn("relative flex justify-center items-center sm:py-3 w-full", {
            "fixed inset-0 z-[120] bg-bg-light": isFullScreen,
            "h-[calc(100dvh-8.2rem)] lg:h-[calc(100dvh-4rem)]": !isFullScreen
        })}>
            <Swiper
                ref={swiperRef}
                spaceBetween={4}
                slidesPerView={1}
                keyboard={{
                    enabled: true,
                }}
                mousewheel={true}
                direction={'vertical'}
                modules={[Mousewheel, Keyboard]}
                className="mySwiper size-full"
                onSlideChange={handleSlideChange}
                onAfterInit={handleSlideChange}
            >
                {
                    shorts.map((item, index) => <SwiperSlide className="size-full flex items-center justify-center overflow-hidden" key={`slide-${item.id}-${index}`}>
                        {({ isActive }) => (
                            <div key={`slide-${item.id}-item${index}`} className="flex size-full items-center">
                            <VibeSlideItem
                                isMuted={isMuted}
                                isFullScreen={isFullScreen}
                                handleMute={handleMute}
                                setIsFullScreen={handleIsFullScreen}
                                isCurrent={isActive}
                                onEnd={() => {
                                    if (!isEnd) {
                                        handleNext()
                                    }
                                }} {...item} />
                            </div>
                        )}
                    </SwiperSlide>)
                }
            </Swiper>
            <div className="hidden sm:flex flex-col w-max h-full absolute right-0 top-0 items-center justify-center gap-3">
                <Button
                    onPress={() => handlePrev()}
                    aria-label="Slide precedant"
                    intent="none"
                    size="square-petite"
                    className={"sm:bg-muted sm:hover:bg-bg-light border border-border/50 sm:text-fg rounded-full size-10 justify-center flex items-center disabled:cursor-not-allowed disabled:opacity-40"}
                    isDisabled={isBeginning}
                >
                    <ArrowUp className="size-4" strokeWidth={1.8} />
                </Button>
                <Button
                    onPress={() => handleNext()}
                    aria-label="Slide suivant"
                    intent="none"
                    size="square-petite"
                    className={"sm:bg-muted sm:hover:bg-bg-light border border-border/50 sm:text-fg rounded-full size-10 justify-center flex items-center disabled:cursor-not-allowed disabled:opacity-40"}
                    isDisabled={isEnd}
                >
                    <ArrowDown className="size-4" strokeWidth={1.8} />
                </Button>
            </div>
        </Container>
    )
}
