
import type { ReactNode } from "react";
import { AccessUserWrapper } from "@/components/access-user-wrapper";
import { getCurrentUser } from "@/actions/auth";
import { SingleMessageWrapper } from "./single-message-wrapper";
import { mockChats } from "@/data/messages";




export default async function MessageLayout({
    children,
}: Readonly<{ children: ReactNode }>) {
    const user = await getCurrentUser()
    const chatList = mockChats.filter(message => user?.type === "artiste" ? message.owner === user.artisteData : message.owner === user?.orgData)
    return (
        <>
            <AccessUserWrapper noAccess={<>
                No Access
            </>}>
                <SingleMessageWrapper chatList={chatList} user={user}>
                    {children}
                </SingleMessageWrapper>
            </AccessUserWrapper>
        </>
    );
}
