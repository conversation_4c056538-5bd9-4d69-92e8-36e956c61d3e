
import { getCurrentUser } from '@/actions/auth';
import { ChatDetailBox } from '@/components/chat/chat-detail-box';
import { ChatDetails } from '@/components/chat/chat-details';
import { EmptySelectedMessage } from '@/components/chats/empty-selected-message';
import { mockChats } from '@/data/messages';

type Props = {
  params: Promise<{ chatId: string }>
}


export default async function ChatDetailsPage({ params }: Props) {
  const chatId = (await params).chatId
  const user = await getCurrentUser()
  const chat = mockChats.find(chatM => chatM.slug === chatId)
  if (!chat) return <>
    <EmptySelectedMessage />
  </>

  if ((user?.type === "artiste" && chat.contactType === "organisation" && user.artisteData.username !== chat.owner.username) || (user?.type === "org" && chat.contactType === "artiste" && user.orgData.slug !== chat.owner.slug))
    return <>
      <EmptySelectedMessage />
    </>
  return (
    <>
      {
        chat.contactType === "artiste" && user?.type === "org" ? (
          <ChatDetailBox detailShowIsFor={"artiste"} user={chat.contact}>
            <div>
              <ChatDetails chat={chat} />
            </div>
          </ChatDetailBox>
        ) : chat.contactType === "organisation" ? (
          <ChatDetailBox detailShowIsFor={"org"} user={chat.contact}>
            <div>
              <ChatDetails chat={chat} />
            </div>
          </ChatDetailBox>
        ) : null
      }
    </>
  )
}