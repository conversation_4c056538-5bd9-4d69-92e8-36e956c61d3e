"use client"

import { Chat<PERSON>ideB<PERSON> } from "@/components/chats/chat-side-bar"
import { ViewChatBox } from "@/components/chats/view-chat-box"
import { Loader } from "@/components/ui/loader"
import { Chat } from "@/data/messages"
import { useBreakpoints } from "@/hooks/use-breakpoints"
import type { App_User } from "@/lib/fakeAuthStore"
import type { ReactNode } from "react"


export const SingleMessageWrapper = ({ children, user, chatList }: { children: ReactNode, user: App_User | null, chatList:Chat[] }) => {
    const { isDesktop, isTablet, isMiddle, isMobile } = useBreakpoints()
    return (
        <>
            {
                isDesktop || isTablet ? (
                    <>
                        <div className="grid md:my-3 md:grid-cols-[300px_1fr] h-[calc(100dvh-5.5rem+0.75rem)] md:h-[calc(100dvh-5.5rem)] md:border border-border/60 md:bg-bg rounded-xl lg:app-container w-full">
                            <ChatSideBar list={chatList} forUser={user?.type === "org" ? "org" : "artiste"} />
                            <ViewChatBox>
                                {children}
                            </ViewChatBox>
                        </div>
                    </>
                ) :
                    isMiddle || isMobile ?
                        <>
                            <div className="grid h-[calc(100dvh-5.5rem+0.75rem)] rounded-xl lg:app-container w-full">
                                <ViewChatBox>
                                    {children}
                                </ViewChatBox>
                            </div>
                        </> :
                        <>
                            <div className="relative hidden md:grid md:my-3 md:grid-cols-[300px_1fr] h-[calc(100dvh-5.5rem+0.75rem)] md:h-[calc(100dvh-5.5rem)] md:border border-border/60 md:bg-bg rounded-xl app-container w-full">
                                <ChatSideBar list={chatList} forUser={user?.type === "org" ? "org" : "artiste"} />
                                <div className="grid">
                                    {children}
                                </div>
                                <div className="absolute inset-0 rounded-xl bg-muted/60 backdrop-blur-lg flex items-center justify-center">
                                    <Loader variant="spin" size="extra-large" />
                                </div>
                            </div>
                            <div className="relative grid md:hidden h-[calc(100dvh-5.5rem+0.75rem)] rounded-xl app-container w-full">
                                <div className="grid">
                                    {children}
                                </div>
                                <div className="absolute inset-0 rounded-xl bg-muted/60 backdrop-blur-lg flex items-center justify-center">
                                    <Loader variant="spin" size="extra-large" />
                                </div>
                            </div>
                        </>
            }
        </>
    )
}
