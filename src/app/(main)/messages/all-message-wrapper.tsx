import { ChatSideBar } from "@/components/chats/chat-side-bar";
import type { ReactNode } from "react";
import { ViewChatBox } from "@/components/chats/view-chat-box";
import { AccessUserWrapper } from "@/components/access-user-wrapper";
import { getCurrentUser } from "@/actions/auth";
import { mockChats } from "@/data/messages";




export const AllMessageWrapper = async ({
    children,
}: { children: ReactNode }) => {
    const user = await getCurrentUser()
    const chatList = mockChats.filter(message => user?.type === "artiste" ? message.owner === user.artisteData : message.owner === user?.orgData)
    return (
        <>
            <AccessUserWrapper noAccess={<>
                No Access
            </>}>
                <div className="grid md:my-3 md:grid-cols-[300px_1fr] h-[calc(100dvh-5.5rem+0.75rem)] md:h-[calc(100dvh-5.5rem)] md:border border-border/60 md:bg-bg rounded-xl lg:app-container w-full">
                    <ChatSideBar list={chatList} forUser={user?.type === "org" ? "org" : "artiste"} />
                    <ViewChatBox>
                        {children}
                    </ViewChatBox>
                </div>
            </AccessUserWrapper>
        </>
    );
}
