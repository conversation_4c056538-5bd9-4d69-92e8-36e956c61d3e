import ContainerWrapper from "@/components/artiste/container-wrapper";
import { reviewData, testimonials } from "@/data/reviews";
import { MessageSquare } from "lucide-react";
import { Card } from "@/components/ui/card";
import ReviewSummary from "@/components/review/review-summary";
import { TestimonialCard } from "@/components/review/testimonial-card";

export default function page() {
    return (
        <ContainerWrapper>
            <div className="pb-6 mt-8">
                <div className="flex items-center gap-4">
                    <div className="size-10 rounded-lg bg-bg-surface flex justify-center items-center border border-border/70">
                        <MessageSquare strokeWidth={1.2} className="size-5" />
                    </div>
                    <div className="flex-1 flex flex-col space-y-1">
                        <Card.Title>Avis</Card.Title>
                        <Card.Description>Consultez et gérez les avis de vos clients.</Card.Description>
                    </div>
                </div>
            </div>
            <ReviewSummary
                isOwnerOrArtiste={true}
                totalReviews={reviewData.totalReviews}
                averageRating={reviewData.averageRating}
                ratingDistribution={reviewData.ratingDistribution}
                ratingCategories={reviewData.ratingCategories}
            />
            <div className="mt-8 column-1 sm:columns-2 *:break-inside-avoid space-y-4 gap-4">
                {testimonials.map((testimonial, index) => (
                    <TestimonialCard
                        key={index}
                        note={testimonial.note}
                        quote={testimonial.quote}
                        description={testimonial.description}
                        author={testimonial.author}
                    />
                ))}
            </div>
        </ContainerWrapper>
    )
}
