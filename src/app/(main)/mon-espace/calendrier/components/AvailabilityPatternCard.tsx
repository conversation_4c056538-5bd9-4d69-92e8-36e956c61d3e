import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Edit, Trash } from "lucide-react";

interface AvailabilityPatternCardProps {
  id: number;
  day: string;
  time: string;
}

export function AvailabilityPatternCard({ id, day, time }: AvailabilityPatternCardProps) {
  return (
    <div key={id} className="flex items-center justify-between p-2 rounded-md border">
      <div>
        <Badge intent="info">{day}</Badge>
        <span className="ml-2 text-sm">{time}</span>
      </div>
      <div className="flex space-x-1">
        <Button intent="plain" size="extra-small">
          <Edit className="h-4 w-4" />
        </Button>
        <Button intent="plain" size="extra-small">
          <Trash className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}