import { Button } from "@/components/ui/button";
import { Check, X } from "lucide-react";
import { format } from "date-fns";

interface BookingRequestCardProps {
  id: number;
  organization: string;
  date: Date;
  time: string;
}

export function BookingRequestCard({ id, organization, date, time }: BookingRequestCardProps) {
  return (
    <div key={id} className="p-3 rounded-md border">
      <div className="font-medium">{organization}</div>
      <div className="text-sm text-fg-muted">
        {format(date, "d MMMM yyyy")} • {time}
      </div>
      <div className="flex justify-end space-x-2 mt-2">
        <Button size="small" intent="outline" className="h-8">
          <X className="h-4 w-4 mr-1" /> Refuser
        </Button>
        <Button size="small" className="h-8">
          <Check className="h-4 w-4 mr-1" /> Accepter
        </Button>
      </div>
    </div>
  );
}