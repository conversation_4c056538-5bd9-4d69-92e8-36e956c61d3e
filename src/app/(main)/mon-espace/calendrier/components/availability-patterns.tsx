"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Edit, Trash } from "lucide-react"
import { AvailabilityPatternCard } from "./AvailabilityPatternCard"

// Mock data for demonstration
const patterns = [
    { id: 1, day: "Lundi", time: "14:00 - 18:00" },
    { id: 2, day: "Mercredi", time: "10:00 - 16:00" },
    { id: 3, day: "Vendredi", time: "09:00 - 12:00" },
]

export function AvailabilityPatterns() {
    return (
        <div className="space-y-2">
            {patterns.length === 0 ? (
                <div className="text-center py-4 text-fg-muted">Aucune disponibilité récurrente définie</div>
            ) : (
                patterns.map((pattern) => (
                    <AvailabilityPatternCard
                        key={pattern.id}
                        id={pattern.id}
                        day={pattern.day}
                        time={pattern.time}
                    />
                ))
            )}
        </div>
    )
}
