"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Check, X } from "lucide-react"
import { format } from "date-fns"
import { BookingRequestCard } from "./BookingRequestCard"

// Mock data for demonstration
const requests = [
  {
    id: 1,
    organization: "City Arts Festival",
    date: new Date(2025, 5, 25),
    time: "2:00 PM - 4:00 PM",
  },
  {
    id: 2,
    organization: "Downtown Gallery",
    date: new Date(2025, 5, 28),
    time: "6:00 PM - 8:00 PM",
  },
]

export function BookingRequests() {
  return (
    <div className="space-y-3">
      {requests.length === 0 ? (
        <div className="text-center py-4 text-fg-muted">Aucune demande de réservation en attente</div>
      ) : (
        requests.map((request) => (
          <BookingRequestCard
            key={request.id}
            id={request.id}
            organization={request.organization}
            date={request.date}
            time={request.time}
          />
        ))
      )}
    </div>
  )
}
