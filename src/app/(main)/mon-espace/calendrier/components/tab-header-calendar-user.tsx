import { BookUpIcon, CalendarCheck2, TableCellsMerge } from "lucide-react"
import Link from "next/link"
import { ReactNode } from "react"


export const TabHeaderCalendarUser = ({ current, children }: { current: "index" | "reserv" | "availability", children: ReactNode }) => {
    return (
        <div className="flex w-full sm:items-center flex-col sm:flex-row sm:justify-between gap-4 mt-0.5 mb-6 overflow-hidden">
            <div className="overflow-hidden flex-1 flex">
                <nav className="bg-bg-surface/40 rounded-xl p-1 border border-border/70 max-w-full overflow-hidden overflow-x-auto w-max">
                    <ul className="flex items-center gap-1">
                        <li>
                            <Link href={"/mon-espace/calendrier"} className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm ${current === "index" ? 'bg-bg dark:bg-bg-elevated text-fg shadow-sm' : 'text-muted-fg hover:text-fg'}`}>
                                <CalendarCheck2 strokeWidth={1.2} className="size-4" />
                                Calendrier
                            </Link>
                        </li>
                        <li>
                            <Link href={"/mon-espace/calendrier/reservations"} className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm ${current === "reserv" ? 'bg-bg dark:bg-bg-elevated text-fg shadow-sm' : 'text-muted-fg hover:text-fg'}`}>
                                <BookUpIcon strokeWidth={1.2} className="size-4" />
                                Reservations
                            </Link>
                        </li>
                        <li>
                            <Link href={"/mon-espace/calendrier/disponibilites"} className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm ${current === "availability" ? 'bg-bg dark:bg-bg-elevated text-fg shadow-sm' : 'text-muted-fg hover:text-fg'}`}>
                                <TableCellsMerge strokeWidth={1.2} className="size-4" />
                                Disponibilités
                            </Link>
                        </li>
                    </ul>
                </nav>
            </div>
            <div className="flex min-w-max">
                {children}
            </div>
        </div>
    )
}
