"use client"

import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Tabs } from '@/components/ui/tabs'
import { BookUpIcon, TableCellsMerge } from 'lucide-react'
import React from 'react'
import { AvailabilityPatterns } from './availability-patterns'
import { BookingRequests } from './booking-requests'

export const TabQuickCalender = () => {
    return (
        <>
            <Tabs className={"p-4 rounded-xl bg-bg border border-border/70 overflow-hidden w-full"} defaultSelectedKey="availability">
                <Tabs.List className="grid grid-cols-2 overflow-hidden overflow-x-auto">
                    <Tabs.Tab id="availability" className={"flex items-center justify-center gap-2"}>
                        <BookUpIcon strokeWidth={1.2} className="size-4" />
                        Disponibilité
                    </Tabs.Tab>
                    <Tabs.Tab id="requests" className={"flex items-center justify-center gap-2"}>
                        <TableCellsMerge strokeWidth={1.2} className="size-4" />
                        Réservations
                    </Tabs.Tab>
                </Tabs.List>
                <Tabs.Panel id="availability" className="space-y-4">
                    <>
                        <Card.Header>
                            <Card.Title>Disponibilité Récurrente</Card.Title>
                            <Card.Description>Définissez votre emploi du temps hebdomadaire régulier</Card.Description>
                        </Card.Header>
                        <Card.Content>
                            <AvailabilityPatterns />
                            
                        </Card.Content>
                    </>
                </Tabs.Panel>
                <Tabs.Panel id="requests" className="space-y-4">
                    <>
                        <Card.Header>
                            <Card.Title>Demandes en Attente</Card.Title>
                            <Card.Description>Organisations en attente de votre réponse</Card.Description>
                        </Card.Header>
                        <Card.Content>
                            <BookingRequests />
                        </Card.Content>
                    </>
                </Tabs.Panel>
            </Tabs>
        </>
    )
}
