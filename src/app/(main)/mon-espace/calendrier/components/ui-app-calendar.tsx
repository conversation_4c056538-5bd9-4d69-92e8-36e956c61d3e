"use client"

import AvailabilityForm from '@/components/app-calendar/AvailabilityForm'
import { AppCalendar } from '@/components/app-calendar/Calendar'
import { useState } from 'react'

export const UiAppCalendar = ({slug}: {slug:string}) => {
    const [isOpen, setIsOpen] = useState(false)
    const [selectedDate, setSelectedDate] = useState<{ day: Date, status: string | null } | null>(null)
    return (
        <>
            <AppCalendar
                availabilityData={[
                    {
                        date: '2025-03-15',
                        status: 'available',
                        id: '1',
                        isRecurrent: true,
                        recurrence: {
                            frequency: 'weekly',
                            daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
                            endDate: '2025-06-15'
                        }
                    },
                    {
                        date: '2025-03-16',
                        status: 'booked',
                        id: '2'
                    }
                ]}
                onDateSelect={(Date, status) => {
                    setSelectedDate({ day: Date, status })
                    setIsOpen(true)
                }}
            />
{
            selectedDate?.day ?
                            <AvailabilityForm isOpen={isOpen} onClose={() => setIsOpen(false)} selectedDate={selectedDate?.day!} artistId={slug} />
                            : null
                            }

        </>
    )
}
