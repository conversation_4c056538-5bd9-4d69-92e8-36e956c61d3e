import { Card } from '@/components/ui/card'
import { CalendarCheck2 } from 'lucide-react'
import { ReactNode } from 'react'
import { TabHeaderCalendarUser } from './tab-header-calendar-user'
import ContainerWrapper from '@/components/artiste/container-wrapper'

export const UserCalendarWrapper = ({ current, children, rightChildren }: { current: "index" | "reserv" | "availability", children: ReactNode, rightChildren?: ReactNode }) => {
    return (
        <ContainerWrapper className='w-full'>
            <div className="pb-6 mt-8">
                <div className="flex items-center gap-4">
                    <div className="size-10 rounded-lg bg-bg-surface flex justify-center items-center border border-border/70">
                        <CalendarCheck2 strokeWidth={1.2} className="size-5" />
                    </div>
                    <div className="flex-1 flex flex-col space-y-1">
                        <Card.Title>Avis</Card.Title>
                        <Card.Description>Consultez et gérez les avis de vos clients.</Card.Description>
                    </div>
                </div>
            </div>
            <TabHeaderCalendarUser current={current}>
                {rightChildren}
            </TabHeaderCalendarUser>
            {children}
        </ContainerWrapper>
    )
}
