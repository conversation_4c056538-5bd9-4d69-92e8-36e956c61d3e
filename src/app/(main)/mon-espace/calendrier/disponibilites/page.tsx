import { Card } from "@/components/ui/card";
import { UserCalendarWrapper } from "../components/user-calendar-wrapper";
import { Button } from "@/components/ui/button";


const availabilityData = [
    { id: 1, date: "2023-10-01", status: "libre" },
    { id: 2, date: "2023-10-02", status: "occupe" },
    { id: 3, date: "2023-10-03", status: "indisponible" },
];

export default function MyCalendarManagementDispoPage() {
    return (
        <UserCalendarWrapper current="availability" rightChildren={<>
            <Button intent="primary" size="small">
                Ajouter une disponibilité
            </Button>
        </>}>
            <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                {availabilityData.map(({ id, date, status }) => (
                    <div key={id} className="p-4 bg-bg border border-border/70 rounded-xl">
                        <Card.Header>
                            <Card.Title>ID: {id}</Card.Title>
                            <Card.Description>Date: {date}</Card.Description>
                            <Card.Description>Status: {status}</Card.Description>
                        </Card.Header>
                        <div className="flex justify-end gap-2 mt-4">
                            <Button size="small" intent="primary">Edit</Button>
                            <Button size="small" intent="danger">Delete</Button>
                        </div>
                    </div>
                ))}
            </div>
        </UserCalendarWrapper>
    );
}
