import { TabQuickCalender } from './components/tab-quick-calender'
import { UiAppCalendar } from './components/ui-app-calendar'
import { UserCalendarWrapper } from './components/user-calendar-wrapper'

export default function MyCalendarManagementPage() {
    return (
        <UserCalendarWrapper current='index'>
            <div className="grid lg:grid-cols-[1fr_420px] gap-10 overflow-hidden w-full">
                <UiAppCalendar slug=""/>
                <div className="grid overflow-hidden w-full">
                    <TabQuickCalender/>
                </div>
            </div>
        </UserCalendarWrapper>
    )
}