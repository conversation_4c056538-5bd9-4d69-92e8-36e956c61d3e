
import { UserCalendarWrapper } from "../components/user-calendar-wrapper";
import { BookingRequestCard } from "../components/BookingRequestCard";

const bookingData = [
    { id: 1, organization: "Org A", date: new Date(), time: "10:00 AM" },
    { id: 2, organization: "Org B", date: new Date(), time: "11:00 AM" },
    { id: 3, organization: "Org C", date: new Date(), time: "12:00 PM" },
    { id: 4, organization: "Org D", date: new Date(), time: "01:00 PM" },
    { id: 5, organization: "Org E", date: new Date(), time: "02:00 PM" },
    { id: 6, organization: "Org F", date: new Date(), time: "03:00 PM" },
    { id: 7, organization: "Org G", date: new Date(), time: "04:00 PM" },
    { id: 8, organization: "Org H", date: new Date(), time: "05:00 PM" },
    { id: 9, organization: "Org I", date: new Date(), time: "06:00 PM" },
    { id: 10, organization: "Org J", date: new Date(), time: "07:00 PM" }
];

export default function MyCalendarManagementDispoPage() {
    return (
        <UserCalendarWrapper current="reserv" rightChildren={<></>}>
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {bookingData.map(({ id, organization, date, time }) => (
                    <BookingRequestCard key={id} id={id} organization={organization} date={date} time={time} />
                ))}
            </div>
        </UserCalendarWrapper>
    );
}
