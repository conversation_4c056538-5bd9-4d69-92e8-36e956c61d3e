"use client"

import { Card } from "@/components/ui/card"
import { Candidature, CandidatureStatus } from "../../../../../data/candidature-mock"
import { motion } from "framer-motion"

interface KanbanColumnProps {
    title: string
    status: CandidatureStatus
    items: Candidature[]
}

const KanbanColumn = ({ title, items }: KanbanColumnProps) => {
    return (
        <div className="flex-1 min-w-[280px] bg-bg dark:bg-bg-surface/50 border border-border/70 rounded-lg p-4">
            <h3 className="font-medium mb-4">{title}</h3>
            <div className="space-y-3">
                {items.map((item) => (
                    <motion.div
                        key={item.id}
                        layout
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.2 }}
                    >
                        <Card className="p-4 transition-shadow">
                            <div className="space-y-2">
                                <div className="font-medium">{item.mission}</div>
                                <div className="text-sm text-muted-fg">{item.company}</div>
                                <div className="text-xs text-muted-fg">
                                    Postuté le {new Date(item.applicationDate).toLocaleDateString('fr-FR')}
                                </div>
                            </div>
                        </Card>
                    </motion.div>
                ))}
            </div>
        </div>
    )
}

export const CandidaturesKanban = ({ candidatures }: { candidatures: Candidature[] }) => {
    const columns: { title: string; status: CandidatureStatus }[] = [
        { title: "En attente", status: "en_attente" },
        { title: "En cours", status: "en_cours" },
        { title: "Accepté", status: "accepté" },
        { title: "Rejeté", status: "rejeté" },
    ]

    return (
        <div className="mt-6">
            <div className="flex gap-6 overflow-x-auto pb-6">
                {columns.map((column) => (
                    <KanbanColumn
                        key={column.status}
                        title={column.title}
                        status={column.status}
                        items={candidatures.filter((c) => c.status === column.status)}
                    />
                ))}
            </div>
        </div>
    )
}