"use client"

import { Card } from "@/components/ui/card"
import { HandCoins } from "lucide-react"
import { AnimatePresence } from "framer-motion"
import { mockCandidatures } from "@/data/candidature-mock"
import { CandidaturesTable } from "./candidatures-table"

export const CandidaturesList = () => {

    return (
        <>
            <div className="flex items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                    <div className="size-10 rounded-lg bg-bg-surface flex justify-center items-center border border-border/70">
                        <HandCoins strokeWidth={1.2} className="size-5" />
                    </div>
                    <div className="flex-1 flex flex-col space-y-1">
                        <Card.Title>Candidatures</Card.Title>
                        <Card.Description>Suivez l'état de vos candidatures aux missions.</Card.Description>
                    </div>
                </div>
            </div>

            <div className="grid mt-6">
                <AnimatePresence mode="wait">
                    <div className="p-5 rounded-lg bg-bg border border-border/70 overflow-x-auto">
                        <CandidaturesTable candidatures={mockCandidatures} />
                    </div>
                </AnimatePresence>
            </div>
        </>
    )
}
