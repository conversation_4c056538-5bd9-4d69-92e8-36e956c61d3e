"use client"

import { Table } from "@/components/ui/table"
import { Menu } from "@/components/ui/menu"
import { IconDotsVertical, IconEye } from "@intentui/icons"
import { Candidature } from "../../../../../data/candidature-mock"
import { motion } from "framer-motion"

interface CandidaturesTableProps {
    candidatures: Candidature[]
}

const getStatusColor = (status: string) => {
    switch (status) {
        case 'rejeté':
            return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
        case 'accepté':
            return 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400'
        case 'en_cours':
            return 'bg-primary text-fg-primary-btn'
        case 'en_attente':
            return 'bg-fg-title text-bg'
        default:
            return 'bg-bg-surface text-fg'
    }
}

const formatStatus = (status: string) => {
    const statusMap: Record<string, string> = {
        'rejeté': 'Rejeté',
        'accepté': 'Accepté',
        'en_cours': 'En cours',
        'en_attente': 'En attente'
    }
    return statusMap[status] || status
}

export const CandidaturesTable = ({ candidatures }: CandidaturesTableProps) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
        >
            <Table aria-label="Candidatures">
                <Table.Header>
                    <Table.Column className="w-0">#</Table.Column>
                    <Table.Column isRowHeader>Mission</Table.Column>
                    <Table.Column>Entreprise</Table.Column>
                    <Table.Column>Date de candidature</Table.Column>
                    <Table.Column>Statut</Table.Column>
                    <Table.Column />
                </Table.Header>
                <Table.Body items={candidatures}>
                    {(item) => (
                        <Table.Row key={item.id}>
                            <Table.Cell>{item.id}</Table.Cell>
                            <Table.Cell>{item.mission}</Table.Cell>
                            <Table.Cell>{item.company}</Table.Cell>
                            <Table.Cell>
                                {new Date(item.applicationDate).toLocaleDateString('fr-FR')}
                            </Table.Cell>
                            <Table.Cell>
                                <span className={`inline-flex items-center rounded-lg px-2 py-1 text-xs font-medium ${getStatusColor(item.status)}`}>
                                    {formatStatus(item.status)}
                                </span>
                            </Table.Cell>
                            <Table.Cell className="text-end last:pr-2.5">
                                <Menu>
                                    <Menu.Trigger>
                                        <IconDotsVertical />
                                    </Menu.Trigger>
                                    <Menu.Content placement="left top">
                                        <Menu.Item>
                                            <IconEye /> Voir
                                        </Menu.Item>
                                    </Menu.Content>
                                </Menu>
                            </Table.Cell>
                        </Table.Row>
                    )}
                </Table.Body>
            </Table>
        </motion.div>
    )
}