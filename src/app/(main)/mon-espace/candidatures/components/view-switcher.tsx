"use client"

import { IconGrid4 as IconLayoutGrid, IconBulletList as IconList } from "@intentui/icons"
import { motion } from "framer-motion"
import { ViewState } from "../../../../../data/candidature-mock"

interface ViewSwitcherProps {
    currentView: ViewState['currentView']
    onViewChange: (view: ViewState['currentView']) => void
}

export const ViewSwitcher = ({ currentView, onViewChange }: ViewSwitcherProps) => {
    return (
        <div className="flex items-center gap-2 bg-bg-surface rounded-lg p-1 border border-border/70">
            <motion.button
                whileTap={{ scale: 0.95 }}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm ${currentView === 'list' ? 'bg-bg text-fg shadow-sm' : 'text-muted-fg hover:text-fg'}`}
                onClick={() => onViewChange('list')}
            >
                <IconList className="size-4" />
                Liste
            </motion.button>
            <motion.button
                whileTap={{ scale: 0.95 }}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm ${currentView === 'kanban' ? 'bg-bg text-fg shadow-sm' : 'text-muted-fg hover:text-fg'}`}
                onClick={() => onViewChange('kanban')}
            >
                <IconLayoutGrid className="size-4" />
                Kanban
            </motion.button>
        </div>
    )
}