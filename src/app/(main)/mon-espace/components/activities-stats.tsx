"use client"

import { Chart, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import type { ChartConfig } from "@/components/ui/chart"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"

// Generate last 30 days data
const generateDailyData = (min: number, max: number) => {
    return Array.from({ length: 30 }, (_, index) => {
        const date = new Date()
        date.setDate(date.getDate() - (29 - index))
        return {
            date: date.toLocaleDateString("fr-FR", { day: "numeric", month: "short" }),
            value: Math.floor(Math.random() * (max - min) + min)
        }
    })
}

// Dummy data for each chart
const visitsData = generateDailyData(10, 90)
const clicksData = generateDailyData(5, 50)
const contractsData = generateDailyData(6, 50)

const visitsConfig: ChartConfig = {
    value: {
        label: "Visites du profil",
        color: "var(--chart-1)"
    }
}

const clicksConfig: ChartConfig = {
    value: {
        label: "Clics sur le profil",
        color: "var(--chart-2)"
    }
}

const contractsConfig: ChartConfig = {
    value: {
        label: "Contrats",
        color: "var(--chart-3)"
    }
}

export function ProfileVisits() {
    return (
        <Chart className="max-h-[250px] w-full" config={visitsConfig}>
            <AreaChart
                data={visitsData}
                margin={{ left: 12, right: 12 }}
                accessibilityLayer
            >
                <CartesianGrid vertical={false} />
                <XAxis
                    dataKey="date"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                />
                <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="line" />} />
                <Area
                    dataKey="value"
                    type="natural"
                    fill="var(--chart-1)"
                    fillOpacity={0.4}
                    stroke="var(--chart-1)"
                />
            </AreaChart>
        </Chart>
    )
}

export function ProfileClicks() {
    return (
        <Chart className="max-h-[250px] w-full" config={clicksConfig}>
            <AreaChart
                data={clicksData}
                margin={{ left: 12, right: 12 }}
                accessibilityLayer
            >
                <CartesianGrid vertical={false} />
                <XAxis
                    dataKey="date"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                />
                <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="line" />} />
                <Area
                    dataKey="value"
                    type="natural"
                    fill="var(--chart-2)"
                    fillOpacity={0.4}
                    stroke="var(--chart-2)"
                />
            </AreaChart>
        </Chart>
    )
}

export function ContractsHome() {
    return (
        <Chart className="max-h-[350px] size-full" config={contractsConfig}>
            <AreaChart
                data={contractsData}
                margin={{ left: 12, right: 12 }}
                accessibilityLayer
            >
                <CartesianGrid vertical={false} />
                <XAxis
                    dataKey="date"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                />
                <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="line" />} />
                <Area
                    dataKey="value"
                    type="natural"
                    fill="var(--chart-3)"
                    fillOpacity={0.4}
                    stroke="var(--chart-3)"
                />
            </AreaChart>
        </Chart>
    )
}
