"use client"

import { cn } from "@/lib/utils"
import Link from "next/link"
import { ReactNode } from "react"

export const TabHeaderSwitcher = ({ current, className = '', items }: { current: "index" | "reserv" | "availability", className?: string, items: { id: number | string, name: string, href: string, text: string, icon: ReactNode }[] }) => {
    return (
        <nav className={cn("flex items-center gap-2 bg-bg-surface/40 rounded-xl p-1 border border-border/70 max-w-full overflow-hidden overflow-x-auto w-max mt-0.5 mb-6", className)}>
            <ul className="flex items-center gap-1">
                {
                    items.map(item => <li key={`item-dash-${item.id}`}>
                        <Link href={item.href} className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm ${current === item.name ? 'bg-bg dark:bg-bg-elevated text-fg shadow-sm' : 'text-muted-fg hover:text-fg'}`}>
                            {item.icon}
                            {item.text}
                        </Link>
                    </li>)
                }
            </ul>
        </nav>
    )
}
