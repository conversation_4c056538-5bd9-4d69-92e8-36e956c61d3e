"use client"

import { ContratPreview } from "@/components/contrat/contrat-preview"
import { Card } from "@/components/ui/card"
import { Menu } from "@/components/ui/menu"
import { Pagination } from "@/components/ui/pagination"
import { Table } from "@/components/ui/table"
import { initialArtisteContrats } from "@/data/contrats"
import { organisations } from "@/data/organizations"
import { Contrat } from "@/types/contrat"
import { IconDotsVertical, IconEye, IconHighlight } from "@intentui/icons"
import { ReceiptText } from "lucide-react"
import { useState } from "react"

export const ContractsList = () => {
    const [contrats] = useState<Contrat[]>(initialArtisteContrats)
    const [action, setAction] = useState<"none" | "preview-contrat" | "print-contrat" | "add-signature">("none")

    const [selectedContrat, setSelectedContrat] = useState<Contrat | null>(null)

    const closeAction = () => {
        setAction("none")
        setSelectedContrat(null)
    }
    return (
        <>
            <div className="flex items-center gap-4">
                <div className="size-10 rounded-lg bg-bg-surface flex justify-center items-center border border-border/70">
                    <ReceiptText strokeWidth={1.2} className="size-5" />
                </div>
                <div className="flex-1 flex flex-col space-y-1">
                    <Card.Title>Contrats</Card.Title>
                    <Card.Description>Gérez vos contrats et suivez leur statut.</Card.Description>
                </div>
            </div>
            <div className="bg-bg rounded-lg p-5 border border-border/70 mt-6 w-full overflow-hidden overflow-x-auto">
                <Table aria-label="Contrats">
                    <Table.Header>
                        <Table.Column className="w-0">#</Table.Column>
                        <Table.Column isRowHeader>Organisation</Table.Column>
                        <Table.Column>Période</Table.Column>
                        <Table.Column>Statut</Table.Column>
                        <Table.Column>Note</Table.Column>
                        <Table.Column />
                    </Table.Header>
                    <Table.Body items={contrats}>
                        {(item) => (
                            <Table.Row id={item.id}>
                                <Table.Cell>{item.id}</Table.Cell>
                                <Table.Cell>{
                                    organisations[0].name
                                }</Table.Cell>
                                <Table.Cell>{`${formatDate(item.dateDebut)} - ${formatDate(item.dateFin)}`}</Table.Cell>
                                <Table.Cell>
                                    <span className={`inline-flex items-center rounded-lg px-2 py-1 text-xs font-medium ${getStatusColor(item.statut)}`}>
                                        {item.statut}
                                    </span>
                                </Table.Cell>
                                <Table.Cell>3</Table.Cell>
                                <Table.Cell className="text-end last:pr-2.5">
                                    <Menu>
                                        <Menu.Trigger>
                                            <IconDotsVertical />
                                        </Menu.Trigger>
                                        <Menu.Content placement="left top">
                                            <Menu.Item onAction={()=>{
                                                setAction("preview-contrat")
                                                setSelectedContrat(item)
                                            }}>
                                                <IconEye /> Voir le contrat
                                            </Menu.Item>
                                            <Menu.Item isDisabled={item.statut === "draft"}>
                                                <IconHighlight /> Imprimer
                                            </Menu.Item>
                                        </Menu.Content>
                                    </Menu>
                                </Table.Cell>
                            </Table.Row>
                        )}
                    </Table.Body>
                </Table>
            </div>

            {
                selectedContrat && action === "preview-contrat" ? <ContratPreview
                    open={true}
                    onOpenChange={closeAction}
                    contratData={{
                        ...selectedContrat,
                        isArtiste: true,
                        showAddSignature: !selectedContrat.estSigne,
                        description: selectedContrat?.description || ''
                    }}
                    onDownload={() => {
                        console.log("Téléchargement du contrat...")
                    }}
                    onSend={() => {
                        console.log("Envoi pour signature...")
                    }}
                /> : null
            }
        </>
    )
}

const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'annulé':
            return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
        case 'clôturé':
            return 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400'
        case 'en cours':
            return 'bg-primary text-fg-primary-btn'
        case 'à venir':
            return 'bg-fg-title text-bg'
        default:
            return 'bg-bg-surface text-fg'
    }
}

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    })
}

const contracts = [
    {
        id: 1,
        organization: "Tech Solutions SA",
        period: {
            start: "2024-03-15",
            end: "2024-04-15"
        },
        status: "En cours",
        rating: 4.8,
    },
    {
        id: 2,
        organization: "Marketing Pro SARL",
        period: {
            start: "2024-02-28",
            end: null
        },
        status: "Clôturé",
        rating: 4.5,
    },
    {
        id: 3,
        organization: "Consulting Group",
        period: {
            start: "2024-04-01",
            end: "2024-05-01"
        },
        status: "À venir",
        rating: 0,
    },
    {
        id: 4,
        organization: "Design Studio",
        period: {
            start: "2024-01-15",
            end: null
        },
        status: "Annulé",
        rating: 3.2,
    },
    {
        id: 5,
        organization: "Innovation Labs",
        period: {
            start: "2024-03-20",
            end: "2024-04-20"
        },
        status: "En cours",
        rating: 4.9,
    },
]

const pages = Array.from({ length: contracts.length / 2 }, (_, i) => ({ value: i + 1 }))
