import "./../app-ui.css"

import { ArtUserWrapper } from "@/components/art-user-wrapper";
import { GlobalAppDashNav } from "@/components/organisms/global-app-dash-nav";

import { CalendarCheck2, HandCoins, LayoutDashboard, ReceiptText, WandSparkles } from "lucide-react"


const dashItems = [
    {
        id: 1,
        text: "Dashboard",
        href: "/mon-espace",
        icon: <LayoutDashboard strokeWidth={1.2} className="size-5" />
    }, {
        id: 2,
        text: "Contrats",
        href: "/mon-espace/contrats",
        icon: <ReceiptText strokeWidth={1.2} className="size-5" />
    }, {
        id: 3,
        text: "Calendrier",
        href: "/mon-espace/calendrier",
        icon: <CalendarCheck2 strokeWidth={1.2} className="size-5" />
    }, {
        id: 4,
        text: "Solicitations",
        href: "/mon-espace/sollicitations",
        icon: <WandSparkles strokeWidth={1.2} className="size-5" />
    }, {
        id: 5,
        text: "Candidatures",
        href: "/mon-espace/candidatures",
        icon: <HandCoins strokeWidth={1.2} className="size-5" />
    },
]

export default function DashLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <>
            <ArtUserWrapper noAccess={<>
                No Access
            </>}>
                <main className="w-full">
                    <GlobalAppDashNav items={dashItems} />
                    {children}
                </main>
            </ArtUserWrapper>
        </>
    );
}
