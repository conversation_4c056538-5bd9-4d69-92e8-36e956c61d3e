

import ContainerWrapper from '@/components/artiste/container-wrapper'
import React from 'react'
import {  CalendarCheck2, ReceiptText, WandSparkles, HandCoins } from 'lucide-react'
import Link from 'next/link'
import { StatHeader } from '../../../components/molecules/stat-header'
import { ContractsHome, ProfileClicks, ProfileVisits } from './components/activities-stats'

const stats = [
  {
    id: 1,
    value: "23",
    label: "Contrats",
    icon: <ReceiptText className="size-5" strokeWidth={1.2} />,
    href:"/contrats"
  },
  {
    id: 2,
    value: "45",
    label: "Candidatures",
    icon: <HandCoins className="size-5" strokeWidth={1.2} />,
    href:"/candidatures"
  },
  {
    id: 3,
    value: "67",
    label: "Sollicitations",
    icon: <WandSparkles className="size-5" strokeWidth={1.2} />,
    href:"/sollicitations"
  },
  {
    id: 4,
    value: "4",
    label: "Reservations",
    icon: <CalendarCheck2 className="size-5" strokeWidth={1.2} />,
    href:"/calendrier"
  },
]

export default function page() {
  return (
    <ContainerWrapper>
      <div className="mt-6">
        <div className="flex flex-col gap-2 text-center sm:flex-row sm:items-center sm:justify-between sm:text-start rounded-xl bg-bg border border-border/60 p-5">
          <div className="grow ">
            <h1 className="mb-1 text-lg font-semibold text-fg-title">Aperçu Rapide</h1>
            <h2 className="text-sm font-medium text-fg-muted">
              Bienvenue sur votre tableau de bord d'artiste.
            </h2>
          </div>
          <div className="flex flex-none items-center justify-center gap-2 rounded-sm px-2 sm:justify-end sm:bg-transparent sm:px-0">
          </div>
        </div>
        <hr className="mt-6 border-border" />
      </div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-4 pt-5">
        {stats.map((stat) => (
          <Link
            key={stat.id}
            href={`/mon-espace${stat.href}`}
            className="flex flex-col rounded-xl bg-bg dark:bg-bg-surface/60 border border-border/60"
          >
            <div className="flex grow items-center justify-between p-5">
              <dl>
                <dt className="text-2xl font-semibold text-fg-title">{stat.value}</dt>
                <dd className="text-sm font-medium text-fg-muted">
                  {stat.label}
                </dd>
              </dl>
              <div className="flex size-10 items-center justify-center rounded-lg bg-bg-surface/70 dark:bg-bg-surface border border-transparent dark:border-border text-fg">
                {stat.icon}
              </div>
            </div>
            <div className="border-t border-border px-5 py-3 text-xs font-medium text-fg-muted">
              <p>Vue d'ensemble</p>
            </div>
          </Link>
        ))}
      </div>
      <div className="grid sm:grid-cols-2 gap-4 mt-6 z-[5]">
        <div className="rounded-xl bg-bg border border-border/60 p-5">
          <StatHeader title="Clicks sur le profile" description='Clicks durant les 30 derniers jours' />
          <div className="">
            <ProfileClicks />
          </div>
        </div>
        <div className="rounded-xl bg-bg border border-border/60 p-5">
          <StatHeader title="Vue" description='Vue du profile durant les 30 derniers jours' />
          <div className="">
            <ProfileVisits/>
          </div>
        </div>
        <div className="rounded-xl bg-bg border border-border/60 p-5 col-span-full">
          <StatHeader title="Contrats signes" description='Contrats durant ces 30 derniers jours' />
          <div className="">
            <ContractsHome />
          </div>
        </div>
      </div>
    </ContainerWrapper>
  )
}
