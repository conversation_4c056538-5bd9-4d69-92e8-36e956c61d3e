"use client"

import { useState } from "react"

import { Avatar } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Menu } from "@/components/ui/menu"
import { Sheet } from "@/components/ui/sheet"

import {
    IconBook,
    IconBrandCopilot,
    IconGear,
    IconHeart,
    IconLogout,
    IconMessageDots,
    IconPerson,
    IconStar,
} from "@intentui/icons"
import { Menu as MenuPrimitive } from "react-aria-components"

export const SheetDetailSollicitation = ({ solicitation }:{solicitation:any}) => {
    const [isOpen, setIsOpen] = useState(false)
    const closeModal = () => setIsOpen(false)
    return (
        <>
            <Sheet>
                <Button size="small" intent="outline" className={"h-8 text-sm before:flex"}>
                    Details
                </Button>
                <Sheet.Content classNames={{content:"max-w-3xl"}} isFloat={false}>
                    <Sheet.Header className="flex flex-row gap-x-3.5 border-b sm:gap-x-3 sm:px-4 sm:pt-3 sm:pb-2">
                        <Avatar src={solicitation.avatarUrl} shape="square" alt="Corp" />
                        <div>
                            <Sheet.Title className="text-base/4 sm:text-base/4">{solicitation.title}</Sheet.Title>
                            <Sheet.Description>{solicitation.description}</Sheet.Description>
                        </div>
                    </Sheet.Header>
                    <Sheet.Body className="px-0 sm:px-0">
                        <div className="p-4">
                            <p>{solicitation.details}</p>
                        </div>
                    </Sheet.Body>
                    <Sheet.Footer className="border-t bg-muted/20 sm:p-4 flex items-center justify-end">
                        <Button size="small" className="justify-center" intent="danger">
                            <span>Rejetter</span>
                        </Button>
                        <Button size="small" className="justify-center" intent="primary">
                            <span>Accepter</span>
                        </Button>
                    </Sheet.Footer>
                </Sheet.Content>
            </Sheet>
        </>
    )
}
