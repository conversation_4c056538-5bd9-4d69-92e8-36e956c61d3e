"use client"

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Building2, Calendar, Euro, FileText, MapPin, Timer } from 'lucide-react'
import Link from 'next/link'
import { SheetDetailSollicitation } from './sheet-detail-sollicitation'



const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'canceled':
            return 'bborder border-red-200 dark:border-red-800 g-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
        case 'confirmed':
            return 'border border-primary-300 dark:border-primary-800 bg-primary-50 text-primary-900 dark:bg-primary-900/30 dark:text-primary-400'
        case 'pending':
            return 'border border-orange-200 dark:border-orange-800 bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400'
        default:
            return 'bg-bg-surface text-fg'
    }
}

export const SollicitationCard = ({ title, organization, description, date, location, contractType, salary, status }: { title: string, organization: string, description: string, date: string, location: string, contractType: string, salary: number, status: string }) => {
    return (
        <div className="rounded-xl p-5 border border-border/70 bg-bg flex flex-col relative">
            <div className="flex justify-between items-center pointer-events-none">
                <h2 className="font-semibold md:text-lg text-fg-title flex-1">
                    {title}
                </h2>
                <span className="flex items-center text-xs sm:text-sm text-fg-muted">
                    <Timer strokeWidth={1.2} className="size-3.5 mr-0.5" />
                    2 Jours
                </span>
            </div>
            <div className="flex items-center text-sm text-fg-muted pointer-events-none">
                <Building2 strokeWidth={1.2} className="size-4 mr-2" />
                {organization}
            </div>
            <p className='line-clamp-2 text-sm text-fg-muted mt-5 pointer-events-none'>
                {description}
            </p>
            <div className="mt-auto pt-4 flex flex-col justify-between gap-3">
                <div className="flex items-center flex-wrap gap-x-4 gap-y-2 text-sm text-fg-muted pointer-events-none">
                    <div className="flex items-center">
                        <Calendar strokeWidth={1.2} className="size-4 text-fg-muted mr-1" />
                        <span>
                            {date}
                        </span>
                    </div>
                    <div className="flex items-center">
                        <MapPin strokeWidth={1.2} className="size-4 text-fg-muted mr-1" />
                        <span>
                            {location}
                        </span>
                    </div>
                    <div className="flex items-center">
                        <FileText strokeWidth={1.2} className="size-4 text-fg-muted mr-1" />
                        <span>{contractType}</span>
                    </div>
                    <div className="flex items-center">
                        <Euro strokeWidth={1.2} className="size-4 text-fg-muted mr-1" />
                        <span>{salary}</span>
                    </div>
                </div>
                <div className="flex justify-between items-center gap-2 pt-2.5 border-t border-border">
                    <div className="flex items-center pointer-events-none">
                        <Badge intent="none" className={`flex rounded-lg ${getStatusColor(status)}`}>
                            {status}
                        </Badge>
                    </div>

                    <div className="flex items-center gap-2">
                        <SheetDetailSollicitation solicitation={{ title, description, details: 'Additional details here', avatarUrl: 'path/to/avatar' }} />
                        {status.toLowerCase() === 'pending' && (
                            <Button size="small" className={"h-8 text-sm"}>
                                Accepter
                            </Button>
                        )}
                    </div>

                </div>
            </div>
        </div>
    )
}
