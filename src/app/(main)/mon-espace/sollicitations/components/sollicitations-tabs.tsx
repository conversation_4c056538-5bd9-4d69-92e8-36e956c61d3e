"use client"

import ContainerWrapper from "@/components/artiste/container-wrapper"
import { Card } from "@/components/ui/card"
import { Tabs } from "@/components/ui/tabs"
import { CircleDotDashed, CircleOff, PackageCheck, WandSparkles } from "lucide-react"
import React, { ReactNode } from "react"


const tabItems = [
    {
        id: 1,
        name: "all-s",
        text: "Toutes les sollicitations",
        icon: <WandSparkles strokeWidth={1.2} className="size-4" />
    },
    {
        id: 2,
        name: "accepted",
        text: "Acceptees",
        icon: <PackageCheck strokeWidth={1.2} className="size-4" />
    },
    {
        id: 3,
        name: "pending",
        text: "En attente",
        icon: <CircleDotDashed strokeWidth={1.2} className="size-4" />
    },
    {
        id: 4,
        name: "rejected",
        text: "Rejettees",
        icon: <CircleOff strokeWidth={1.2} className="size-4" />
    }
]

export const SollicitationsTabs = ({ all, accepted, rejected, pending }: { all: ReactNode, accepted: ReactNode, rejected: ReactNode, pending: ReactNode }) => {
    return (
        <>
            <ContainerWrapper>
                <div className="pb-6 mt-8">
                    <div className="flex items-center gap-4">
                        <div className="size-10 rounded-lg bg-bg-surface flex justify-center items-center border border-border/70">
                            <WandSparkles strokeWidth={1.2} className="size-5" />
                        </div>
                        <div className="flex-1 flex flex-col space-y-1">
                            <Card.Title>Sollicitations</Card.Title>
                            <Card.Description>Consultez et gérez les avis de vos clients.</Card.Description>
                        </div>
                    </div>
                </div>
                <div className="grid">
                    <Tabs aria-label="Recipe App">
                        <Tabs.List>
                            {
                                tabItems.map(item =>
                                    <Tabs.Tab key={item.id} className={"flex h-max pb-1.5 items-center gap-x-1"} id={item.name}>
                                        {item.icon}
                                        {item.text}
                                    </Tabs.Tab>
                                )
                            }
                        </Tabs.List>
                        <Tabs.Panel id="all-s">
                            {all}
                        </Tabs.Panel>
                        <Tabs.Panel id="accepted">
                            {accepted}
                        </Tabs.Panel>
                        <Tabs.Panel id="pending">
                            {pending}
                        </Tabs.Panel>
                        <Tabs.Panel id="rejected">
                            {rejected}
                        </Tabs.Panel>
                    </Tabs>
                </div>
            </ContainerWrapper>
        </>
    )
}
