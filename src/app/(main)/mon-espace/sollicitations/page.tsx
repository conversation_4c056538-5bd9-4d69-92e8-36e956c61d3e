
import { SollicitationCard } from './components/sollicitation-card'
import { SollicitationsTabs } from './components/sollicitations-tabs'

export default function MySollicitationsPage() {
  return (
    <>
      <SollicitationsTabs all={<>
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <SollicitationCard title="Mission 1" organization="Org 1" description="Description 1" date="01-04 Janvier" location="Berlin" contractType="CDI" salary={1300} status="pending" />
          <SollicitationCard title="Mission 2" organization="Org 2" description="Description 2" date="05-08 Janvier" location="Paris" contractType="CDD" salary={1500} status="confirmed" />
          <SollicitationCard title="Mission 3" organization="Org 3" description="Description 3" date="09-12 Janvier" location="London" contractType="Freelance" salary={2000} status="canceled" />
  
        </div>
        </>}
        accepted={<>
       
        </>}
        pending={<>
        </>}
        rejected={<>
        </>}
        />
    </>
  )
}
