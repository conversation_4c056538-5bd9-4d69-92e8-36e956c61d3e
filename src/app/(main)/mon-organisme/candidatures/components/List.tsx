"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table } from "@/components/ui/table"

const candidatures = [
    {
        id: 1,
        candidat: "<PERSON>",
        poste: "Danseur(se) Contemporain(e)",
        company: "Compagnie de Danse XYZ",
        status: "en_cours",
        date_candidature: "2024-03-25",
        experience: "5 ans"
    },
    {
        id: 2,
        candidat: "<PERSON>",
        poste: "Professeur de Danse Jazz",
        company: "École de Danse ABC",
        status: "entretien",
        date_candidature: "2024-03-24",
        experience: "8 ans"
    },
    {
        id: 3,
        candidat: "<PERSON>",
        poste: "Chorégraphe Assistant(e)",
        company: "Studio de Création",
        status: "rejetée",
        date_candidature: "2024-03-23",
        experience: "3 ans"
    }
]

export const CandidaturesListDash = () => {
    return (
        <>
            <Table aria-label="Candidatures">
                <Table.Header>
                    <Table.Column>CANDIDAT</Table.Column>
                    <Table.Column>POSTE</Table.Column>
                    <Table.Column>ENTREPRISE</Table.Column>
                    <Table.Column>EXPÉRIENCE</Table.Column>
                    <Table.Column>STATUT</Table.Column>
                    <Table.Column>ACTIONS</Table.Column>
                </Table.Header>
                <Table.Body items={candidatures}>
                    {(item) => (
                        <Table.Row key={item.id}>
                            <Table.Cell className="py-4">
                                <div className="flex flex-col">
                                    <span className="text-sm font-medium text-fg-title">{item.candidat}</span>
                                    <span className="text-xs text-fg-muted">Postulé le: {new Date(item.date_candidature).toLocaleDateString()}</span>
                                </div>
                            </Table.Cell>
                            <Table.Cell>{item.poste}</Table.Cell>
                            <Table.Cell>{item.company}</Table.Cell>
                            <Table.Cell>{item.experience}</Table.Cell>
                            <Table.Cell>
                                <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${item.status === 'en_cours' ? 'bg-yellow-50 text-yellow-800 dark:bg-yellow-400/10 dark:text-yellow-500' : item.status === 'entretien' ? 'bg-green-50 text-green-800 dark:bg-green-400/10 dark:text-green-500' : 'bg-red-50 text-red-800 dark:bg-red-400/10 dark:text-red-500'}`}>
                                    {item.status}
                                </span>
                            </Table.Cell>
                            <Table.Cell>
                                <div className="flex items-center gap-2">
                                    <Button intent="outline" size="small">Voir profil</Button>
                                    {item.status === 'en_cours' && (
                                        <>
                                            <Button intent="success" size="small">Planifier entretien</Button>
                                            <Button intent="danger" size="small">Rejeter</Button>
                                        </>
                                    )}
                                </div>
                            </Table.Cell>
                        </Table.Row>
                    )}
                </Table.Body>
            </Table>
        </>
    )
}