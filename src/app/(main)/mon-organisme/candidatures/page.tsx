import ContainerWrapper from '@/components/artiste/container-wrapper'
import { CandidaturesListDash } from './components/List'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { HandCoins } from 'lucide-react'

export default function OrgCandidaturesList() {
  return (
    <ContainerWrapper className="grid">
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between pt-6">
        <div className="flex items-center gap-4">
          <div className="size-10 rounded-lg bg-bg-surface flex justify-center items-center border border-border/70">
            <HandCoins strokeWidth={1.2} className="size-5" />
          </div>
          <div className="flex-1 flex flex-col space-y-1">
            <Card.Title>Candidatures</Card.Title>
            <Card.Description>Gérez les candidatures reçues pour vos offres.</Card.Description>
          </div>
        </div>
        <Button intent="primary" size='small'>Nouvelle candidature</Button>
      </div>

      <div className="w-full mt-6 rounded-xl bg-bg border border-border/70 p-4 sm:p-6 overflow-hidden overflow-x-auto">
        <CandidaturesListDash />
      </div>

    </ContainerWrapper>
  )
}
