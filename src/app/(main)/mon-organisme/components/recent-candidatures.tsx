"use client"

import { Avatar } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { mockCandidatures } from "@/data/candidature-mock"

export const RecentCandidatures = () => {
    return (
        <div className="divide-y divide-border">
            {mockCandidatures.slice(0, 4).map((candidature) => (
                <div key={candidature.id} className="flex flex-col gap-3 justify-start py-3 px-4">
                    <div className="flex items-center gap-3">
                        <Avatar src={""} initials={candidature.company[0]} className="size-9 text-xl flex items-center justify-center"/>
                        <div className="flex flex-col">
                            <p className="text-sm font-medium text-fg-title">{candidature.mission}</p>
                            <p className="text-xs text-fg-muted">{candidature.company}</p>
                        </div>
                    </div>
                    <Badge intent={candidature.status === 'en_attente' ? "white/dark" : candidature.status === 'accepté' ? "success" : candidature.status === 'rejeté' ? "danger" : "primary"} className="w-max rounded-lg ml-12">
                        {candidature.status}
                    </Badge>
                </div>
            ))}
        </div>
    )
}