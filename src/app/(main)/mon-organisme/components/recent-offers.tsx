"use client"

import { Avatar } from "@/components/ui/avatar"
import { CalendarCheck2 } from "lucide-react"

const recentOffers = [
    {
        id: 1,
        title: "Danseur Principal",
        company: "Compagnie de Danse XYZ",
        location: "Paris, France",
        date: "2024-03-25"
    },
    {
        id: 2,
        title: "Chorégraphe",
        company: "Studio de Danse ABC",
        location: "Lyon, France",
        date: "2024-03-24"
    },
    {
        id: 3,
        title: "Professeur de Danse",
        company: "École de Danse 123",
        location: "Marseille, France",
        date: "2024-03-23"
    },
    {
        id: 4,
        title: "Danseur Contemporain",
        company: "Théâtre National",
        location: "Bordeaux, France",
        date: "2024-03-22"
    }
]

export const RecentOffers = () => {
    return (
        <div className="divide-y divide-border">
            {recentOffers.map((offer) => (
                <div key={offer.id} className="flex flex-col items-start py-3 px-4">
                    <div className="flex items-center gap-3">
                        <Avatar src={""} initials={offer.company[0]} className="size-9 text-xl flex items-center justify-center"/>
                        <div className="flex flex-col">
                            <p className="text-sm font-medium text-fg-title">{offer.title}</p>
                            <p className="text-xs sm:text-sm text-fg-muted">{offer.company}</p>
                        </div>
                    </div>
                    <div className="flex items-center gap-1 text-xs sm:text-sm text-fg-muted pl-12">
                        <CalendarCheck2 className="size-3" strokeWidth={1.5} />
                        <span>{new Date(offer.date).toLocaleDateString()}</span>
                    </div>
                </div>
            ))}
        </div>
    )
}