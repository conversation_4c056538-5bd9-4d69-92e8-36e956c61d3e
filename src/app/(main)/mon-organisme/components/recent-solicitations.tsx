"use client"

import { Avatar } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

const recentSolicitations = [
    {
        id: 1,
        title: "Performance Spéciale",
        company: "Festival d'Été",
        status: "en_attente",
        date: "2024-03-25"
    },
    {
        id: 2,
        title: "Spectacle de Danse",
        company: "Théâtre Municipal",
        status: "accepté",
        date: "2024-03-24"
    },
    {
        id: 3,
        title: "Workshop de Danse",
        company: "Studio de Danse Pro",
        status: "en_cours",
        date: "2024-03-23"
    },
    {
        id: 4,
        title: "Événement Privé",
        company: "Entreprise ABC",
        status: "rejeté",
        date: "2024-03-22"
    }
]

export const RecentSolicitations = () => {
    return (
        <div className="divide-y divide-border">
            {recentSolicitations.map((solicitation) => (
                <div key={solicitation.id} className="flex items-start flex-col gap-3 py-3 px-4">
                    <div className="flex items-center gap-3">
                        <Avatar src={""} initials={solicitation.company[0]} className="size-9 text-xl flex items-center justify-center"/>
                        <div className="flex flex-col">
                            <p className="text-sm font-medium text-fg-title">{solicitation.title}</p>
                            <p className="text-xs text-fg-muted">{solicitation.company}</p>
                        </div>
                    </div>
                    <Badge intent={solicitation.status === 'en_attente' ? "white/dark" : solicitation.status === 'accepté' ? "success" : solicitation.status === 'rejeté' ? "danger" : "primary"} className="text-sm w-max rounded-lg ml-12">
                        {solicitation.status}
                    </Badge>
                </div>
            ))}
        </div>
    )
}