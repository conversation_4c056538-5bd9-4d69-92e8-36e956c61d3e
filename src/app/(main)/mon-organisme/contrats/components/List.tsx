"use client"

import { useState } from "react"
import { buttonStyles } from "@/components/ui/button"

import { Table } from "@/components/ui/table"
import { Download, Eye, FileText, MessageSquare, MoreHorizontal, PenLine, Receipt, Trash2 } from "lucide-react"
import { type Contrat } from "@/types/contrat"
import { Badge } from "@/components/ui/badge"
import { Menu } from "@/components/ui/menu"
import { FactureDialog } from "./view-facture"
import { initialContrats } from "@/data/contrats"
import { ContratPreview } from "@/components/contrat/contrat-preview"

export const ContractsListDash = () => {
    const [contrats, setContrats] = useState<Contrat[]>(initialContrats)
    const [selectedContrat, setSelectedContrat] = useState<Contrat | null>(null)

    const [action, setAction] = useState<"details-contrat" | "preview-contrat" | "edit-contrat" | "facture-contrat" | "delete" | "none">("none")


    const getStatusLabel = (statut: string) => {
        switch (statut) {
            case "active":
                return "Actif"
            case "pending":
                return "En attente"
            case "draft":
                return "Brouillon"
            case "completed":
                return "Terminé"
            default:
                return statut
        }
    }

    const getFactureBadge = (contrat: Contrat) => {
        if (!contrat.hasFacture) return null

        return (
            <Badge intent={contrat.factureStatut === "paid" ? "primary" : "secondary"} className="ml-2">
                {contrat.factureStatut === "paid" ? "Facture payée" : "Facture en attente"}
            </Badge>
        )
    }

    const closeAction = () => {
        setSelectedContrat(null)
        setAction("none")
    }

    return (
        <>
            <Table>
                <Table.Header>
                    <Table.Column>Artiste</Table.Column>
                    <Table.Column isRowHeader>Projet</Table.Column>
                    <Table.Column>Début</Table.Column>
                    <Table.Column>Fin</Table.Column>
                    <Table.Column>Type</Table.Column>
                    <Table.Column>Rémunération</Table.Column>
                    <Table.Column>Statut</Table.Column>
                    <Table.Column className="text-right">Actions</Table.Column>
                </Table.Header>
                <Table.Body items={contrats}>
                    {
                        (contrat) => <Table.Row key={contrat.id}>
                            <Table.Cell className="font-medium">{contrat.artiste}</Table.Cell>
                            <Table.Cell>{contrat.projet}</Table.Cell>
                            <Table.Cell>{new Date(contrat.dateDebut).toLocaleDateString("fr-FR")}</Table.Cell>
                            <Table.Cell>{new Date(contrat.dateFin).toLocaleDateString("fr-FR")}</Table.Cell>
                            <Table.Cell>{contrat.typeContrat}</Table.Cell>
                            <Table.Cell>
                                <div className="flex items-center">
                                    {contrat.remuneration}
                                    {/* {getFactureBadge(contrat)} */}
                                </div>
                            </Table.Cell>
                            <Table.Cell>
                                <Badge intent={contrat.statut === "active" ? "primary" : contrat.statut === "completed" ? "secondary" : contrat.statut === "pending" ? "info" : "none"}>{getStatusLabel(contrat.statut)}</Badge>
                            </Table.Cell>
                            <Table.Cell className="text-right">
                                <Menu>
                                    <Menu.Trigger className={buttonStyles({ size: "square-petite", intent: "plain" })}>
                                        <MoreHorizontal className="size-4" />
                                        <span className="sr-only">Menu</span>
                                    </Menu.Trigger>
                                    <Menu.Content className="w-56">
                                        <Menu.Header>
                                            <Menu.Label>Actions</Menu.Label>
                                            <Menu.Separator />
                                        </Menu.Header>
                                        <Menu.Item onAction={() => {
                                            setSelectedContrat(contrat)
                                            setAction("details-contrat")
                                        }}>
                                            <Eye className="size-4 mr-2" strokeWidth={1.2} />
                                            <Menu.Label>Voir les détails</Menu.Label>
                                        </Menu.Item>
                                        <Menu.Item onAction={() => {
                                            setSelectedContrat(contrat)
                                            setAction("preview-contrat")
                                        }}>
                                            <FileText className="size-4 mr-2" strokeWidth={1.2} />
                                            <Menu.Label>Aperçu du contrat</Menu.Label>
                                        </Menu.Item>
                                        {/* {contrat.hasFacture && (
                                            <Menu.Item onAction={() => {
                                                setSelectedContrat(contrat)
                                                setAction("facture-contrat")
                                            }}>
                                                <Receipt className="size-4 mr-2" strokeWidth={1.2} />
                                                <Menu.Label>Voir la facture</Menu.Label>
                                            </Menu.Item>
                                        )} */}
                                        <Menu.Item onAction={() => {
                                            setSelectedContrat(contrat)
                                            setAction("edit-contrat")
                                        }}>
                                            <PenLine className="size-4 mr-2" strokeWidth={1.2} />
                                            <Menu.Label>Modifier</Menu.Label>
                                        </Menu.Item>
                                        <Menu.Item>
                                            <Download className="size-4 mr-2" strokeWidth={1.2} />
                                            <Menu.Label>Télécharger PDF</Menu.Label>
                                        </Menu.Item>
                                        <Menu.Item>
                                            <MessageSquare className="size-4 mr-2" strokeWidth={1.2} />
                                            <Menu.Label>Contacter l&apos;artiste</Menu.Label>
                                        </Menu.Item>
                                        <Menu.Separator />
                                        <Menu.Item isDanger>
                                            <Trash2 className="size-4 mr-2" strokeWidth={1.2} />
                                            <Menu.Label>Supprimer</Menu.Label>
                                        </Menu.Item>
                                    </Menu.Content>
                                </Menu>
                            </Table.Cell>
                        </Table.Row>
                    }
                </Table.Body>
            </Table>
            {
                selectedContrat && action === "facture-contrat" ? <FactureDialog open={true} onDownload={() => { }} factureData={selectedContrat} onPay={() => { }} onOpenChange={closeAction} contratData={selectedContrat}/>:null
            }

            {
                selectedContrat && action === "preview-contrat" ? <ContratPreview
                    open={true}
                    onOpenChange={closeAction}
                    contratData={{
                        ...selectedContrat,
                        description: selectedContrat?.description || ''
                    }}
                    onDownload={() => {
                        console.log("Téléchargement du contrat...")
                    }}
                    onSend={() => {
                        console.log("Envoi pour signature...")
                    }}
                /> : null
            }

        </>
    )
}