"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Receipt, Download, CreditCard, Calendar, Euro } from "lucide-react"
import { Contrat } from "@/types/contrat"
import { Modal } from "@/components/ui/modal"

interface FactureDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    contratData: Contrat
    factureData: any
    onPay: () => void
    onDownload: () => void
}

export function FactureDialog({ open, onOpenChange, contratData, factureData, onPay, onDownload }: FactureDialogProps) {
    const [isPaying, setIsPaying] = useState(false)

    const handlePay = () => {
        setIsPaying(true)
        setTimeout(() => {
            onPay()
            setIsPaying(false)
        }, 2000)
    }

    const formatDate = (dateString: string | undefined) => {
        if (!dateString) return "Non définie"
        return new Date(dateString).toLocaleDateString("fr-FR")
    }

    const calculateTTC = (montant: string) => {
        const amount = Number.parseFloat(montant.replace(/[^\d.]/g, ""))
        const tva = amount * 0.2 // 20% TVA
        return {
            ht: amount,
            tva: tva,
            ttc: amount + tva,
        }
    }

    const amounts = calculateTTC(contratData.remuneration)

    return (
        <Modal.Content size="4xl" isOpen={open} onOpenChange={onOpenChange} classNames={{ content: "max-w-6xl w-full max-h-[90vh] overflow-y-auto" }}>
            <Modal.Header>
                <Modal.Title className="flex items-center gap-2">
                    <Receipt className="h-5 w-5" />
                    Facture {factureData?.numero ? `- ${factureData.numero}` : ""}
                </Modal.Title>
            </Modal.Header>

            <Modal.Body className="space-y-6">
                {/* En-tête facture */}
                <Card className="shadow-none border-border/80 rounded-xl">
                    <CardHeader>
                        <div className="flex justify-between items-start">
                            <div>
                                <CardTitle className="text-lg">FACTURE</CardTitle>
                                <p className="text-sm text-fg-muted">Numéro: {factureData.numero}</p>
                            </div>
                            <Badge intent={factureData.statut === "pending" ? "secondary" : "primary"}>
                                {factureData.statut === "pending" ? "En attente" : "Payée"}
                            </Badge>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="grid md:grid-cols-2 gap-6">
                            <div>
                                <h4 className="font-semibold mb-2">Facturé à :</h4>
                                <div className="text-sm space-y-1">
                                    <p className="font-medium">Théâtre National de Paris</p>
                                    <p>123 Avenue des Arts</p>
                                    <p>75001 Paris, France</p>
                                    <p>SIRET: 12345678901234</p>
                                </div>
                            </div>
                            <div>
                                <h4 className="font-semibold mb-2">Facturé par :</h4>
                                <div className="text-sm space-y-1">
                                    <p className="font-medium">{contratData.artiste}</p>
                                    <p>45 Rue de la Danse</p>
                                    <p>75011 Paris, France</p>
                                    <p>SIRET: 98765432109876</p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Détails du contrat */}
                <Card className="shadow-none border-border/80 rounded-xl">
                    <CardHeader>
                        <CardTitle className="text-base">Détails de la prestation</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="grid md:grid-cols-2 gap-4 text-sm">
                                <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 text-fg-muted" />
                                    <span className="font-medium">Projet:</span> {contratData.projet}
                                </div>
                                <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 text-fg-muted" />
                                    <span className="font-medium">Dates:</span> {formatDate(contratData.dateDebut)} -{" "}
                                    {formatDate(contratData.dateFin)}
                                </div>
                            </div>
                            <div className="text-sm">
                                <span className="font-medium">Description:</span> {contratData.description}
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Détail des montants */}
                <Card className="shadow-none border-border/80 rounded-xl">
                    <CardHeader>
                        <CardTitle className="text-base">Détail des montants</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            <div className="flex justify-between text-sm">
                                <span>Prestation artistique (HT)</span>
                                <span>{amounts.ht.toFixed(2)} €</span>
                            </div>
                            <div className="flex justify-between text-sm">
                                <span>TVA (20%)</span>
                                <span>{amounts.tva.toFixed(2)} €</span>
                            </div>
                            <hr />
                            <div className="flex justify-between font-semibold text-lg">
                                <span>Total TTC</span>
                                <span className="flex items-center gap-1">
                                    <Euro strokeWidth={1.2} className="h-4 w-4" />
                                    {amounts.ttc.toFixed(2)} €
                                </span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Informations de paiement */}
                <Card className="shadow-none border-border/80 rounded-xl">
                    <CardHeader>
                        <CardTitle className="text-base">Informations de paiement</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span className="font-medium">Date d'émission:</span>{" "}
                                {factureData?.dateEmission ? formatDate(factureData.dateEmission) : "Non définie"}
                            </div>
                            <div>
                                <span className="font-medium">Date d'échéance:</span>{" "}
                                {factureData?.dateEcheance ? formatDate(factureData.dateEcheance) : "Non définie"}
                            </div>
                            <div>
                                <span className="font-medium">Modalités:</span> Virement bancaire ou carte
                            </div>
                            <div>
                                <span className="font-medium">Délai:</span> 30 jours
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Statut de paiement */}
                {factureData && factureData.statut === "pending" && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-orange-50 border border-orange-200 rounded-lg p-4"
                    >
                        <div className="flex items-center gap-2 text-orange-800">
                            <CreditCard className="h-5 w-5" />
                            <span className="font-medium">Paiement requis</span>
                        </div>
                        <p className="text-sm text-orange-700 mt-1">
                            Cette facture est en attente de paiement. Veuillez procéder au règlement pour finaliser le contrat.
                        </p>
                    </motion.div>
                )}
            </Modal.Body>

            <Modal.Footer className="gap-2">
                <Button intent="outline" onClick={onDownload} className="gap-2">
                    <Download strokeWidth={1.2} className="h-4 w-4" />
                    Télécharger PDF
                </Button>
                {factureData && factureData.factureStatut === "pending" && (
                    <Button onClick={handlePay} isDisabled={isPaying} className="gap-2">
                        <CreditCard strokeWidth={1.2} className="h-4 w-4" />
                        {isPaying ? "Paiement en cours..." : "Payer maintenant"}
                    </Button>
                )}
            </Modal.Footer>
        </Modal.Content>

    )
}
