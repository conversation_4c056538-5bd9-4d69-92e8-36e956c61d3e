import { Card } from "@/components/ui/card"
import { FileText, Plus } from "lucide-react"
import ContainerWrapper from "@/components/artiste/container-wrapper"
import { But<PERSON> } from "@/components/ui/button"
import { ContractsListDash } from "./components/List"

export default function ContratsPage() {
    return (
        <ContainerWrapper className="grid">
            <div className="flex items-center gap-4 flex-1 pt-6">
                <div className="size-10 rounded-lg bg-bg-surface flex justify-center items-center border border-border/70">
                    <FileText strokeWidth={1.2} className="size-5" />
                </div>
                <div className="flex-1 flex flex-col space-y-1">
                    <Card.Title>Gestion des Contrats</Card.Title>
                    <Card.Description>Gérez vos contrats, factures et documents contractuels.</Card.Description>
                </div>
            </div>

            <div className="w-full flex flex-col mt-6 rounded-xl bg-bg border border-border/70 p-4 sm:p-6 overflow-hidden overflow-x-auto">
                <ContractsListDash />
            </div>
        </ContainerWrapper>
    )
}