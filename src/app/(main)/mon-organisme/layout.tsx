import "./../app-ui.css"
import { OrgUserWrapper } from "@/components/org-user-wrapper";
import { GlobalAppDashNav } from "@/components/organisms/global-app-dash-nav";
import { CalendarCheck2, HandCoins, LayoutDashboard, ReceiptText, WandSparkles } from "lucide-react"


const dashItems = [
    {
        id: 1,
        text: "Dashboard",
        href: "/mon-organisme",
        icon: <LayoutDashboard strokeWidth={1.2} className="size-5" />
    }, {
        id: 2,
        text: "Contrats",
        href: "/mon-organisme/contrats",
        icon: <ReceiptText strokeWidth={1.2} className="size-5" />
    }, {
        id: 3,
        text: "Projets",
        href: "/mon-organisme/projets",
        icon: <WandSparkles strokeWidth={1.2} className="size-5" />
    }, {
        id: 4,
        text: "Opportunites",
        href: "/mon-organisme/opportunites",
        icon: <CalendarCheck2 strokeWidth={1.2} className="size-5" />
    }, {
        id: 5,
        text: "Solicitations",
        href: "/mon-organisme/sollicitations",
        icon: <WandSparkles strokeWidth={1.2} className="size-5" />
    }, {
        id: 6,
        text: "Candidatures",
        href: "/mon-organisme/candidatures",
        icon: <HandCoins strokeWidth={1.2} className="size-5" />
    }
]


export default function OrgDashLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <>
            <OrgUserWrapper noAccess={<>No Access</>}>
                <main className="w-full">
                    <GlobalAppDashNav items={dashItems} />
                    {children}
                </main>
            </OrgUserWrapper>
        </>
    );
}
