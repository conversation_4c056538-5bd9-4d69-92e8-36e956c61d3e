"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Table } from "@/components/ui/table"

const offers = [
    {
        id: 1,
        title: "Danseur(se) Contemporain(e)",
        company: "Compagnie de Danse XYZ",
        status: "active",
        date_limite: "2024-04-25",
        lieu: "Paris",
        type_contrat: "CDI",
        candidatures: 12
    },
    {
        id: 2,
        title: "Professeur de Danse Jazz",
        company: "École de Danse ABC",
        status: "en_pause",
        date_limite: "2024-04-15",
        lieu: "Lyon",
        type_contrat: "CDD",
        candidatures: 8
    },
    {
        id: 3,
        title: "Chorégraphe Assistant(e)",
        company: "Studio de Création",
        status: "terminée",
        date_limite: "2024-03-30",
        lieu: "Marseille",
        type_contrat: "Freelance",
        candidatures: 15
    }
]

export const OffersListDash = () => {
    return (
        <>
            <Table aria-label="Offres">
                <Table.Header>
                    <Table.Column>TITRE</Table.Column>
                    <Table.Column>ENTREPRISE</Table.Column>
                    <Table.Column>LIEU</Table.Column>
                    <Table.Column>TYPE</Table.Column>
                    <Table.Column>CANDIDATURES</Table.Column>
                    <Table.Column>STATUT</Table.Column>
                    <Table.Column>ACTIONS</Table.Column>
                </Table.Header>
                <Table.Body items={offers}>
                    {(item) => (
                        <Table.Row key={item.id}>
                            <Table.Cell className="py-4">
                                <div className="flex flex-col">
                                    <span className="text-sm font-medium text-fg-title">{item.title}</span>
                                    <span className="text-xs text-fg-muted">Date limite: {new Date(item.date_limite).toLocaleDateString()}</span>
                                </div>
                            </Table.Cell>
                            <Table.Cell>{item.company}</Table.Cell>
                            <Table.Cell>{item.lieu}</Table.Cell>
                            <Table.Cell>{item.type_contrat}</Table.Cell>
                            <Table.Cell>{item.candidatures}</Table.Cell>
                            <Table.Cell>
                                <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${item.status === 'active' ? 'bg-green-50 text-green-800 dark:bg-green-400/10 dark:text-green-500' : item.status === 'en_pause' ? 'bg-yellow-50 text-yellow-800 dark:bg-yellow-400/10 dark:text-yellow-500' : 'bg-blue-50 text-blue-800 dark:bg-blue-400/10 dark:text-blue-500'}`}>
                                    {item.status}
                                </span>
                            </Table.Cell>
                            <Table.Cell>
                                <div className="flex items-center gap-2">
                                    <Button intent="outline" size="small">Voir détails</Button>
                                    {item.status === 'active' && (
                                        <Button intent="success" size="small">Modifier</Button>
                                    )}
                                </div>
                            </Table.Cell>
                        </Table.Row>
                    )}
                </Table.Body>
            </Table>
        </>
    )
}