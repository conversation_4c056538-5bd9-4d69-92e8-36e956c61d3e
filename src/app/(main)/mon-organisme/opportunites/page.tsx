import ContainerWrapper from "@/components/artiste/container-wrapper";
import { OffersListDash } from './components/List';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { CalendarCheck2, Plus } from "lucide-react";

export default function OrgOffresPage() {
    return (
        <ContainerWrapper>
            <div className="flex items-center justify-between gap-4 pt-6">
                <div className="flex items-center gap-4">
                    <div className="size-10 rounded-lg bg-bg-surface flex justify-center items-center border border-border/70">
                        <CalendarCheck2 strokeWidth={1.2} className="size-5" />
                    </div>
                    <div className="flex-1 flex flex-col space-y-1">
                        <Card.Title>Opportunité d'emploi</Card.Title>
                        <Card.Description>Gérez vos Opportunités d'emploi publiées.</Card.Description>
                    </div>
                </div>
                <Button intent="primary" size="small">
                    <Plus strokeWidth={1.2} className="size-4 mr-2"/>
                    Nouvelle Opportunite
                </Button>
            </div>

            <div className="mt-6 rounded-xl bg-bg border border-border/70 p-4 sm:p-6 w-full overflow-hidden overflow-x-auto">
                <OffersListDash />
            </div>
        </ContainerWrapper>
    )
}
