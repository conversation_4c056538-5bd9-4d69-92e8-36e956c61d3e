import ContainerWrapper from '@/components/artiste/container-wrapper'
import { ReceiptText, WandSparkles, HandCoins, Briefcase } from 'lucide-react'
import Link from 'next/link'
import { RecentCandidatures } from './components/recent-candidatures'
import { RecentOffers } from './components/recent-offers'
import { RecentSolicitations } from './components/recent-solicitations'
import { CardStatActivities } from '@/components/molecules/card-stat-activities'

const stats = [
    {
        id: 1,
        value: "4",
        label: "Offres actives",
        icon: <Briefcase className="size-5" strokeWidth={1.2} />,
        href: "/offres-actives"
    },
    {
        id: 2,
        value: "45",
        label: "Candidatures en attente",
        icon: <HandCoins className="size-5" strokeWidth={1.2} />,
        href: "/candidatures"
    },
    {
        id: 3,
        value: "67",
        label: "Sollicitations",
        icon: <WandSparkles className="size-5" strokeWidth={1.2} />,
        href: "/sollicitations"
    },
    {
        id: 4,
        value: "23",
        label: "Contrats actifs",
        icon: <ReceiptText className="size-5" strokeWidth={1.2} />,
        href: "/contrats"
    },
]

export default function OrgDashboardPage() {
    return (
        <ContainerWrapper>
            <div className=" grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-4 pt-5">
                {stats.map((stat) => (
                    <Link
                        key={stat.id}
                        href={`/mon-organisme${stat.href}`}
                        className="flex flex-col rounded-xl bg-bg dark:bg-bg-surface/60 border border-border/60"
                    >
                        <div className="flex grow items-center justify-between p-5">
                            <dl>
                                <dt className="text-2xl font-semibold text-fg-title">{stat.value}</dt>
                                <dd className="text-sm font-medium text-fg-muted">
                                    {stat.label}
                                </dd>
                            </dl>
                            <div className="flex size-10 items-center justify-center rounded-lg bg-bg-surface/70 dark:bg-bg-surface border border-transparent dark:border-border text-fg">
                                {stat.icon}
                            </div>
                        </div>
                        <div className="border-t border-border px-5 py-3 text-xs font-medium text-fg-muted">
                            <p>Vue d'ensemble</p>
                        </div>
                    </Link>
                ))}
            </div>
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 pt-6">
                <CardStatActivities href="/mon-espace/dashboard/candidatures" title="Candidatures recentes">
                    <RecentCandidatures />
                </CardStatActivities>
                <CardStatActivities href="/mon-espace/dashboard/offres" title="Offres récentes">
                    <RecentOffers />
                </CardStatActivities>
                <CardStatActivities href="/mon-espace/dashboard/sollicitations" title="Sollicitations récentes">
                    <RecentSolicitations />
                </CardStatActivities>
            </div>
        </ContainerWrapper>
    )
}
