"use client"

import type React from "react"

import { useState } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"

import { Textarea } from "@/components/ui/textarea"
import { Select } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"
import { TextField } from "@/components/ui/text-field"
import { DateField } from "@/components/ui/date-field"
import { Label } from "@/components/ui/field"
import { Sheet } from "@/components/ui/sheet"

interface CreateAnnouncementModalProps {
    open: boolean
    onOpenChange: (open: boolean) => void
}

export function CreateAnnouncementModal({ open, onOpenChange }: CreateAnnouncementModalProps) {
    const [formData, setFormData] = useState({
        title: "",
        description: "",
        type: "",
        location: "",
        startDate: "",
        endDate: "",
        remuneration: "",
        isPublic: true,
        skills: [] as string[],
    })

    const [newSkill, setNewSkill] = useState("")

    const handleAddSkill = () => {
        if (newSkill.trim() && !formData.skills.includes(newSkill.trim())) {
            setFormData((prev) => ({
                ...prev,
                skills: [...prev.skills, newSkill.trim()],
            }))
            setNewSkill("")
        }
    }

    const handleRemoveSkill = (skillToRemove: string) => {
        setFormData((prev) => ({
            ...prev,
            skills: prev.skills.filter((skill) => skill !== skillToRemove),
        }))
    }

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        // Ici, vous ajouteriez la logique pour soumettre l'annonce
        console.log("Nouvelle annonce:", formData)
        onOpenChange(false)
        // Reset form
        setFormData({
            title: "",
            description: "",
            type: "",
            location: "",
            startDate: "",
            endDate: "",
            remuneration: "",
            isPublic: true,
            skills: [],
        })
    }

    return (
        <Sheet.Content isFloat={false} isBlurred isOpen={open} onOpenChange={onOpenChange} classNames={{ content: "sm:max-w-xl rounded-l-3xl" }}>

            <Sheet.Header>
                <Sheet.Title>Créer une nouvelle annonce</Sheet.Title>
                <Sheet.Description>
                    Publiez une opportunité pour attirer les talents qui correspondent à votre projet.
                </Sheet.Description>
            </Sheet.Header>

            <>
                <Sheet.Body className="space-y-6">

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <TextField label="Titre du rôle *"
                                id="title"
                                value={formData.title}
                                onChange={(e) => setFormData((prev) => ({ ...prev, title: e }))}
                                placeholder="ex: Danseur principal, Chorégraphe..."
                                isRequired />
                        </div>

                        <div className="space-y-2">
                            <Select
                                label="Type de performance *"
                                placeholder="Sélectionner le type"
                                selectedKey={formData.type}
                                onSelectionChange={(value) => setFormData((prev) => ({ ...prev, type: value?.toString() ?? "" }))}
                            >
                                <Select.Trigger />
                                <Select.List>
                                    <Select.Option id="solo" textValue="Solo">Solo</Select.Option>
                                    <Select.Option id="duo" textValue="Duo">Duo</Select.Option>
                                    <Select.Option id="groupe" textValue="Groupe">Groupe</Select.Option>
                                    <Select.Option id="corps_de_ballet" textValue="Corps de ballet">Corps de ballet</Select.Option>
                                    <Select.Option id="choregraphe" textValue="Chorégraphe">Chorégraphe</Select.Option>
                                </Select.List>
                            </Select>
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Textarea
                            label="Description de l'opportunité *"
                            id="description"
                            value={formData.description}
                            onChange={(e) => setFormData((prev) => ({ ...prev, description: e }))}
                            placeholder="Décrivez le rôle, les responsabilités, le contexte du spectacle..."
                            isRequired
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <TextField label="Titre du rôle *"
                            id="location"
                            value={formData.location}
                            onChange={(e) => setFormData((prev) => ({ ...prev, location: e }))}
                            placeholder="ex: Paris, Théâtre National..."
                            isRequired />

                        <TextField label="Titre du rôle *"
                            id="remuneration"
                            value={formData.remuneration}
                            onChange={(e) => setFormData((prev) => ({ ...prev, remuneration: e }))}
                            placeholder="ex: 2500€, 150€/jour..."
                            isRequired />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <DateField id="startDate"
                            label="Date de début"
                        // onChange={(e) => setFormData((prev) => ({ ...prev, startDate: e.target.value }))}
                        />
                        <DateField id="endDate"
                            label="Date de fin"
                        // onChange={(e) => setFormData((prev) => ({ ...prev, startDate: e.target.value }))}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label>Compétences requises</Label>
                        <div className="flex gap-2">
                            <TextField
                                value={newSkill}
                                onChange={(e) => setNewSkill(e)}
                                placeholder="ex: Danse contemporaine, Ballet..."
                                onKeyUp={(e) => e.key === "Enter" && (e.preventDefault(), handleAddSkill())}
                            />
                            <Button type="button" onClick={handleAddSkill} intent="outline">
                                Ajouter
                            </Button>
                        </div>
                        {formData.skills.length > 0 && (
                            <div className="flex flex-wrap gap-2 mt-2">
                                {formData.skills.map((skill, index) => (
                                    <Badge key={index} intent="secondary" className="flex items-center gap-1">
                                        {skill}
                                        <X className="w-3 h-3 cursor-pointer" onClick={() => handleRemoveSkill(skill)} />
                                    </Badge>
                                ))}
                            </div>
                        )}
                    </div>

                    <div className="flex items-center space-x-2">
                        <Switch
                            id="visibility"
                            isSelected={formData.isPublic}
                            onChange={(checked) => setFormData((prev) => ({ ...prev, isPublic: checked }))}
                        />
                        <Label htmlFor="visibility">Annonce publique (visible par tous les artistes)</Label>
                    </div>
                </Sheet.Body>

                <Sheet.Footer>
                    <Button type="button" intent="outline" onClick={() => onOpenChange(false)}>
                        Annuler
                    </Button>
                    <Button type="submit" className="">
                        Publier l'annonce
                    </Button>
                </Sheet.Footer>
            </>
        </Sheet.Content>
    )
}
