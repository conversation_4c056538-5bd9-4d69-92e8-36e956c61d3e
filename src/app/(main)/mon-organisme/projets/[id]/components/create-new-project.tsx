"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"

import { Modal } from "@/components/ui/modal"
import { Select } from "@/components/ui/select"
import { Sheet } from "@/components/ui/sheet"
import { TextField } from "@/components/ui/text-field"
import { Textarea } from "@/components/ui/textarea"
import { Plus } from "lucide-react"
import { useState } from "react"
import React from 'react'

export const CreateNewProject = () => {
    const [open, setOpen] = useState(false)
    const [formData, setFormData] = useState({
        titre: "",
        description: "",
        lieu: "",
        dateDebut: "",
        dateFin: "",
        typeProjet: "",
        rolesTotal: "",
        budget: "",
    })

    const handleChange = (field: string, value: string) => {
        setFormData({
            ...formData,
            [field]: value,
        })
    }

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        // onSubmit(formData)
        resetForm()
        // onOpenChange(false)

    }

    const resetForm = () => {
        setFormData({
            titre: "",
            description: "",
            lieu: "",
            dateDebut: "",
            dateFin: "",
            typeProjet: "",
            rolesTotal: "",
            budget: "",
        })
    }

    

    return (
        <>
            <Button onPress={()=>{
                setOpen(true)
            }} size="small" className={""}>
                <Plus strokeWidth={1.2} className="size-4 mr-1.5" />
                Nouveau projet
            </Button>
            <Sheet.Content isFloat={false} isBlurred classNames={{content:"sm:max-w-lg rounded-l-3xl"}} isOpen={open} onOpenChange={()=>setOpen(false)}>
                <Sheet.Header>
                    <Sheet.Title>Créer un nouveau projet</Sheet.Title>
                    <Sheet.Description>
                        Remplissez les informations pour créer un nouveau projet artistique.
                    </Sheet.Description>
                </Sheet.Header>

                <Sheet.Body>
                    <div className="grid gap-6 py-4">
                        <TextField
                            label="Titre du projet *"
                            id="titre"
                            value={formData.titre}
                            onChange={(e) => setFormData((prev) => ({ ...prev, titre: e }))}
                            placeholder="Ex: Spectacle de Noël 2025"
                            isRequired
                        />

                        <Textarea
                            label="Description *"
                            id="description"
                            value={formData.description}
                            onChange={(e) => setFormData((prev) => ({ ...prev, description: e }))}
                            placeholder="Décrivez votre projet..."
                            isRequired
                        />

                        <TextField
                            label="Lieu *"
                            id="lieu"
                            value={formData.lieu}
                            onChange={(e) => setFormData((prev) => ({ ...prev, lieu: e }))}
                            placeholder="Ex: Théâtre National, Paris"
                            isRequired
                        />

                        <div className="grid grid-cols-2 gap-4">
                            <TextField
                                label="Date de début *"
                                id="dateDebut"
                                type="date"
                                value={formData.dateDebut}
                                onChange={(e) => setFormData((prev) => ({ ...prev, dateDebut: e }))}
                                isRequired
                            />

                            <TextField
                                label="Date de fin *"
                                id="dateFin"
                                type="date"
                                value={formData.dateFin}
                                onChange={(e) => setFormData((prev) => ({ ...prev, dateFin: e }))}
                                isRequired
                            />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <Select
                                label="Type de projet *"
                                placeholder="Sélectionner"
                                selectedKey={formData.typeProjet}
                                onSelectionChange={(value) => setFormData((prev) => ({ ...prev, typeProjet: value?.toString() ?? "" }))}
                            >
                                <Select.Trigger />
                                <Select.List>
                                    <Select.Option id="spectacle" textValue="Spectacle">Spectacle</Select.Option>
                                    <Select.Option id="festival" textValue="Festival">Festival</Select.Option>
                                    <Select.Option id="tournee" textValue="Tournée">Tournée</Select.Option>
                                    <Select.Option id="gala" textValue="Gala">Gala</Select.Option>
                                    <Select.Option id="autre" textValue="Autre">Autre</Select.Option>
                                </Select.List>
                            </Select>

                            <TextField
                                label="Nombre de rôles *"
                                id="rolesTotal"
                                type="number"
                                value={formData.rolesTotal}
                                onChange={(e) => setFormData((prev) => ({ ...prev, rolesTotal: e }))}
                                placeholder="Ex: 10"
                                isRequired
                            />
                        </div>

                        <TextField
                            label="Budget estimé"
                            id="budget"
                            value={formData.budget}
                            onChange={(e) => setFormData((prev) => ({ ...prev, budget: e }))}
                            placeholder="Ex: 50000€"
                        />
                    </div>
                </Sheet.Body>
                <Sheet.Footer>
                    <Button type="button" intent="outline" onClick={() => {
                        setOpen(false)
                     }}>
                        Annuler
                    </Button>
                    <Button type="submit">Créer le projet</Button>
                </Sheet.Footer>
            </Sheet.Content>
        </>
    )
}
