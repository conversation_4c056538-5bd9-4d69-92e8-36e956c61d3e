"use client"

import type React from "react"

import { useState } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"

import { Textarea } from "@/components/ui/textarea"
import { Select } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Search, MapPin, Star } from "lucide-react"
import { Input, Label } from "@/components/ui/field"
import { artisteProfiles } from "@/data/artiste-profiles"
import { Sheet } from "@/components/ui/sheet"
import { TextField } from "@/components/ui/text-field"

interface InviteArtistModalProps {
    open: boolean
    onOpenChange: (open: boolean) => void
}


export function InviteArtistModal({ open, onOpenChange }: InviteArtistModalProps) {
    const [searchTerm, setSearchTerm] = useState("")
    const [selectedLocation, setSelectedLocation] = useState("all")
    const [selectedSpecialty, setSelectedSpecialty] = useState("all")
    const [selectedArtists, setSelectedArtists] = useState<number[]>([])
    const [role, setRole] = useState("")
    const [message, setMessage] = useState("")

    const filteredArtists = artisteProfiles.filter((artist) => {
        const matchesSearch =
            artist.about.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            artist.skills.some((s) => s.toLowerCase().includes(searchTerm.toLowerCase()))
        const matchesLocation = selectedLocation === "all" || artist.about.city === selectedLocation
        const matchesSpecialty = selectedSpecialty === "all" || artist.skills.includes(selectedSpecialty)

        return matchesSearch && matchesLocation && matchesSpecialty
    })

    const handleArtistSelect = (artistId: number) => {
        setSelectedArtists((prev) => (prev.includes(artistId) ? prev.filter((id) => id !== artistId) : [...prev, artistId]))
    }

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        if (selectedArtists.length === 0) {
            alert("Veuillez sélectionner au moins un artiste")
            return
        }

        // Ici, vous ajouteriez la logique pour envoyer les invitations
        console.log("Invitations envoyées:", {
            artists: selectedArtists,
            role,
            message,
        })

        onOpenChange(false)
        // Reset form
        setSelectedArtists([])
        setRole("")
        setMessage("")
        setSearchTerm("")
        setSelectedLocation("all")
        setSelectedSpecialty("all")
    }

    const allSpecialties = Array.from(new Set(artisteProfiles.flatMap((artist) => artist.skills)))
    const allLocations = Array.from(new Set(artisteProfiles.map((artist) => artist.about.city)))

    return (
        <Sheet.Content isFloat={false} isBlurred classNames={{ content: "max-w-4xl rounded-l-3xl" }} isOpen={open} onOpenChange={onOpenChange}>

            <Sheet.Header>
                <Sheet.Title>Solliciter des artistes</Sheet.Title>
                <Sheet.Description>Recherchez et invitez directement des artistes pour votre projet.</Sheet.Description>
            </Sheet.Header>

            <Sheet.Body className="space-y-6 overfl">
                {/* Filtres de recherche */}
                <div className="space-y-4 p-4 bg-muted rounded-lg">
                    <h3 className="font-semibold">Rechercher des artistes</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                            <Label>Recherche</Label>
                            <div className="relative pt-1.5">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-fg-muted/70 w-4 h-4" />
                                <TextField
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e)}
                                    placeholder="Nom ou spécialité..."
                                    className="[&_input]:pl-10"
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Select
                                label="Lieu"
                                placeholder="Tous les lieux"
                                selectedKey={selectedLocation}
                                onSelectionChange={(value) => setSelectedLocation(value?.toString() ?? 'all')}
                            >
                                <Select.Trigger />
                                <Select.List>
                                    <Select.Option id="all" textValue="Tous les lieux">Tous les lieux</Select.Option>
                                    {allLocations.map((location) => (
                                        <Select.Option key={location} id={location} textValue={location}>
                                            {location}
                                        </Select.Option>
                                    ))}
                                </Select.List>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Select
                                label="Spécialité"
                                placeholder="Toutes les spécialités"
                                selectedKey={selectedSpecialty}
                                onSelectionChange={(value) => setSelectedSpecialty(value?.toString() ?? 'all')}
                            >
                                <Select.Trigger />
                                <Select.List>
                                    <Select.Option id="all" textValue="Toutes les spécialités">Toutes les spécialités</Select.Option>
                                    {allSpecialties.map((specialty) => (
                                        <Select.Option key={specialty} id={specialty} textValue={specialty}>
                                            {specialty}
                                        </Select.Option>
                                    ))}
                                </Select.List>
                            </Select>
                        </div>
                    </div>
                </div>

                {/* Liste des artistes */}
                <div className="space-y-4">
                    <h3 className="font-semibold">
                        Artistes disponibles ({filteredArtists.length})
                        {selectedArtists.length > 0 && (
                            <span className="text-fg-primary ml-2">- {selectedArtists.length} sélectionné(s)</span>
                        )}
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                        {filteredArtists.map((artist) => (
                            <Card
                                key={artist.id}
                                className={`cursor-pointer transition-all 
                                    has-checked:border-primary-500 relative`} >
                                <CardContent className="flex-1 min-w-0">
                                    <div className="flex items-start">


                                        <div className="flex flex-1 items-start justify-between">
                                            <div className="flex items-center flex-1">
                                                <img
                                                    src={artist.avatar || "/placeholder.svg"}
                                                    alt={artist.about.firstName}
                                                    className="size-12 mr-3.5 rounded-full object-cover"
                                                />
                                                <div className="flex flex-col flex-1">
                                                    <h4 className="font-semibold text-fg-title text-sm">{artist.about.firstName} {artist.about.lastName}</h4>
                                                    <div className="flex items-center gap-4 mt-1">
                                                        <div className="flex items-center text-xs text-fg-muted">
                                                            <MapPin className="w-3 h-3" />
                                                            {artist.about.city}
                                                        </div>
                                                        <div className="flex items-center text-fg-muted">
                                                            <Star className="size-3.5 text-yellow-400 fill-current" />
                                                            <span className="text-sm ml-1">{artist.rating}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex items-center mt-0.5">


                                                <Checkbox
                                                    id={`selected-${artist.id}`}
                                                    isSelected={selectedArtists.includes(artist.id)}
                                                    onChange={() => handleArtistSelect(artist.id)}
                                                    className={'ml-3'}
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex flex-wrap gap-1 mt-2">
                                        {artist.skills.map((skill, index) => (
                                            <Badge key={index} intent="secondary" className="text-xs">
                                                {skill}
                                            </Badge>
                                        ))}
                                        {artist.skills.length > 2 && (
                                            <Badge intent="white/dark" className="text-xs">
                                                +{artist.skills.length - 2}
                                            </Badge>
                                        )}
                                    </div>
                                    <div className="text-xs text-fg-muted mt-1">
                                        {artist?.experience ?? ""} • {artist.isActive ? "Disponible" : "Non disponible"}
                                    </div>

                                </CardContent>
                                <label htmlFor={`selected-${artist.id}`} className="absolute inset-0"></label>
                            </Card>
                        ))}
                    </div>
                </div>

                {/* Détails de l'invitation */}
                <div className="space-y-4 p-4 bg-muted rounded-lg">
                    <h3 className="font-semibold">Détails de l'invitation</h3>
                    <div className="space-y-4">
                        <TextField id="role"
                            label="Rôle proposé *"
                            value={role}
                            isRequired
                            onChange={(e) => setRole(e)}
                            placeholder="ex: Danseur principal, Chorégraphe assistant..."
                        />
                        <Textarea
                            label="Message personnalisé (optionnel)"
                            id="message"
                            value={message}
                            onChange={(e) => setMessage(e)}
                            placeholder="Ajoutez un message personnel pour expliquer pourquoi vous souhaitez travailler avec cet artiste..."

                        />
                    </div>
                </div>
            </Sheet.Body>

            <Sheet.Footer>
                <Button type="button" intent="outline" onClick={() => onOpenChange(false)}>
                    Annuler
                </Button>
                <Button type="submit" className="" isDisabled={selectedArtists.length === 0}>
                    Envoyer {selectedArtists.length} invitation(s)
                </Button>
            </Sheet.Footer>

        </Sheet.Content>
    )
}
