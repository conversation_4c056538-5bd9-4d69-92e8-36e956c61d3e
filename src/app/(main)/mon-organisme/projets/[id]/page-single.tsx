"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs } from "@/components/ui/tabs"
import { Table } from "@/components/ui/table"
import { ProgressBar as Progress } from "@/components/ui/progress-bar"
import { ArrowLeft, Plus, UserPlus, MapPin, Calendar, Users, Eye, Edit, Trash2, Mail, Phone } from "lucide-react"
import Link from "next/link"
import { InviteArtistModal } from "./components/invite-artist-modal"
import { CreateAnnouncementModal } from "./components/create-announcement-modal"
import { Menu } from "@/components/ui/menu"
import { IconDotsVertical } from "@intentui/icons"
import { artisteProfiles } from "@/data/artiste-profiles"


// Données fictives pour un projet spécifique
const projectData = {
  id: 1,
  title: "Spectacle de Danse Contemporaine",
  description:
    "Un spectacle innovant mêlant danse contemporaine et nouvelles technologies, explorant les thèmes de l'identité et de la transformation dans le monde moderne.",
  location: "Théâtre National de Paris",
  startDate: "2024-03-01",
  endDate: "2024-03-15",
  status: "en_cours",
  createdAt: "2024-01-15",
  totalRoles: 12,
  rolesFilledCount: 8,
}

const announcements = [
  {
    id: 1,
    title: "Danseur Principal - Rôle Masculin",
    description: "Recherche danseur expérimenté en danse contemporaine pour rôle principal",
    type: "Solo",
    remuneration: "2500€",
    skills: ["Danse contemporaine", "Improvisation", "Acrobatie"],
    visibility: "public",
    applicants: 12,
    status: "active",
    createdAt: "2024-01-20",
  },
  {
    id: 2,
    title: "Corps de Ballet - 6 positions",
    description: "Danseurs pour le corps de ballet, niveau intermédiaire à avancé",
    type: "Groupe",
    remuneration: "1200€",
    skills: ["Ballet classique", "Danse contemporaine"],
    visibility: "public",
    applicants: 28,
    status: "active",
    createdAt: "2024-01-22",
  },
]

const invitedArtists = [
  artisteProfiles[3],
  artisteProfiles[1],
  artisteProfiles[5]
]

const confirmedArtists = [
  artisteProfiles[0],
  artisteProfiles[2],
  artisteProfiles[1]
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge className="bg-green-100 text-green-800">Active</Badge>
    case "en_attente":
      return <Badge intent="white/dark">En attente</Badge>
    case "accepte":
      return <Badge className="bg-green-100 text-green-800">Accepté</Badge>
    case "refuse":
      return <Badge intent="danger">Refusé</Badge>
    default:
      return <Badge intent="secondary">Inconnu</Badge>
  }
}

export const ProjectDetail = ()=> {
  const [showCreateAnnouncement, setShowCreateAnnouncement] = useState(false)
  const [showInviteArtist, setShowInviteArtist] = useState(false)

  const progressPercentage = Math.round((projectData.rolesFilledCount / projectData.totalRoles) * 100)

  return (
    <div className="">
      {/* Header */}
      <header className="border-b border-border">
        <div className="max-w-7xl mx-auto">
          <div className="py-6">
            <div className="flex items-center mb-4">
              <Link href="/">
                <Button intent="plain" size="small">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Retour aux projets
                </Button>
              </Link>
            </div>
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-3xl font-bold text-fg-title">{projectData.title}</h1>
                <div className="flex items-center gap-4 mt-2 text-fg-muted">
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {projectData.location}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {new Date(projectData.startDate).toLocaleDateString("fr-FR")} -{" "}
                    {new Date(projectData.endDate).toLocaleDateString("fr-FR")}
                  </div>
                  <div className="flex items-center">
                    <Users className="w-4 h-4 mr-1" />
                    {projectData.rolesFilledCount}/{projectData.totalRoles} rôles pourvus
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button size="small" onClick={() => setShowInviteArtist(true)} intent="outline">
                  <UserPlus className="w-4 h-4 mr-2" />
                  Solliciter un artiste
                </Button>
                <Button size="small" onClick={() => setShowCreateAnnouncement(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Ajouter une annonce
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-8">
        {/* Project Info Card */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Description du projet</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-fg mb-4">{projectData.description}</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h4 className="font-semibold text-sm text-fg-muted mb-1">Statut</h4>
                <Badge intent="info">En cours</Badge>
              </div>
              <div>
                <h4 className="font-semibold text-sm text-fg-muted mb-1">Progression</h4>
                <div className="flex items-center gap-2">
                  <Progress value={progressPercentage} className="flex-1" />
                  <span className="text-sm font-medium">{progressPercentage}%</span>
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-sm text-fg-muted mb-1">Créé le</h4>
                <span className="text-sm">{new Date(projectData.createdAt).toLocaleDateString("fr-FR")}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs defaultSelectedKey="announcements" className="space-y-6">
          <Tabs.List className="grid w-full grid-cols-4">
            <Tabs.Tab className={"justify-center text-center"} id="announcements">Annonces publiées</Tabs.Tab>
            <Tabs.Tab className={"justify-center text-center"} id="invited">Artistes sollicités</Tabs.Tab>
            <Tabs.Tab className={"justify-center text-center"} id="confirmed">Artistes confirmés</Tabs.Tab>
            <Tabs.Tab className={"justify-center text-center"} id="progress">Suivi</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel id="announcements">
            <Card>
              <CardHeader>
                <CardTitle>Annonces publiées</CardTitle>
                <CardDescription>Opportunités publiques auxquelles les artistes peuvent postuler</CardDescription>
              </CardHeader>
              <CardContent>
                <Table aria-label="Annonces publiées">
                  <Table.Header>
                    <Table.Column className="w-0">#</Table.Column>
                    <Table.Column isRowHeader>Titre</Table.Column>
                    <Table.Column>Type</Table.Column>
                    <Table.Column>Rémunération</Table.Column>
                    <Table.Column>Candidatures</Table.Column>
                    <Table.Column>Statut</Table.Column>
                    <Table.Column />
                  </Table.Header>
                  <Table.Body items={announcements}>
                    {(announcement) => (
                      <Table.Row key={announcement.id}>
                        <Table.Cell>{announcement.id}</Table.Cell>
                        <Table.Cell>
                          <div>
                            <div className="font-medium">{announcement.title}</div>
                            <div className="text-sm text-fg-muted">{announcement.description}</div>
                          </div>
                        </Table.Cell>
                        <Table.Cell>{announcement.type}</Table.Cell>
                        <Table.Cell>{announcement.remuneration}</Table.Cell>
                        <Table.Cell>
                          <Badge intent="white/dark">{announcement.applicants} candidatures</Badge>
                        </Table.Cell>
                        <Table.Cell>{getStatusBadge(announcement.status)}</Table.Cell>
                        <Table.Cell className="text-end last:pr-2.5">
                          <Menu>
                            <Menu.Trigger>
                              <IconDotsVertical />
                            </Menu.Trigger>
                            <Menu.Content placement="left top">
                              <Menu.Item>
                                <Eye /> Voir
                              </Menu.Item>
                              <Menu.Item>
                                <Edit /> Modifier
                              </Menu.Item>
                              <Menu.Item isDanger>
                                <Trash2 /> Supprimer
                              </Menu.Item>
                            </Menu.Content>
                          </Menu>
                        </Table.Cell>
                      </Table.Row>
                    )}
                  </Table.Body>
                </Table>
              </CardContent>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel id="invited">
            <Card>
              <CardHeader>
                <CardTitle>Artistes sollicités</CardTitle>
                <CardDescription>Invitations directes envoyées à des artistes spécifiques</CardDescription>
              </CardHeader>
              <CardContent>
                <Table aria-label="Artistes sollicités">
                  <Table.Header>
                    <Table.Column isRowHeader>Artiste</Table.Column>
                    <Table.Column>Rôle proposé</Table.Column>
                    <Table.Column>Spécialités</Table.Column>
                    <Table.Column>Statut</Table.Column>
                    <Table.Column>Invité le</Table.Column>
                    <Table.Column />
                  </Table.Header>
                  <Table.Body items={invitedArtists}>
                    {(artist) => (
                      <Table.Row key={artist.id}>
                        <Table.Cell>
                          <div>
                            <div className="font-medium">{artist.about.firstName} {artist.about.lastName}</div>
                            
                          </div>
                        </Table.Cell>
                        <Table.Cell>{artist.about.title}</Table.Cell>
                        <Table.Cell>
                          <div className="flex flex-wrap gap-1">
                            {artist.skills.map((specialty, index) => (
                              <Badge key={index} intent="secondary" className="text-xs">
                                {specialty}
                              </Badge>
                            ))}
                          </div>
                        </Table.Cell>
                        <Table.Cell>{getStatusBadge(['en_attente', 'accepte', 'active', 'refuse'][Math.floor(Math.random() * 4)])}</Table.Cell>
                        <Table.Cell>{new Date(new Date('2025-04-10').getTime() + Math.random() * (new Date('2025-06-15').getTime() - new Date('2025-04-10').getTime())).toLocaleDateString("fr-FR")}</Table.Cell>
                        <Table.Cell className="text-end last:pr-2.5">
                          <Menu>
                            <Menu.Trigger>
                              <IconDotsVertical />
                            </Menu.Trigger>
                            <Menu.Content placement="left top">
                              <Menu.Item>
                                <Mail /> Relancer
                              </Menu.Item>
                              <Menu.Item isDanger>
                                <Trash2 /> Annuler
                              </Menu.Item>
                            </Menu.Content>
                          </Menu>
                        </Table.Cell>
                      </Table.Row>
                    )}
                  </Table.Body>
                </Table>
              </CardContent>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel id="confirmed">
            <Card>
              <CardHeader>
                <CardTitle>Artistes confirmés</CardTitle>
                <CardDescription>Participants confirmés pour ce projet</CardDescription>
              </CardHeader>
              <CardContent>
                <Table aria-label="Artistes confirmés">
                  <Table.Header>
                    <Table.Column isRowHeader>Artiste</Table.Column>
                    <Table.Column>Rôle</Table.Column>
                    <Table.Column>Rémunération</Table.Column>
                    <Table.Column>Confirmé le</Table.Column>
                    <Table.Column />
                  </Table.Header>
                  <Table.Body items={confirmedArtists}>
                    {(artist) => (
                      <Table.Row key={artist.id}>
                        <Table.Cell>
                          <div>
                            <div className="font-medium">{artist.about.firstName} {artist.about.lastName}</div>
                            
                          </div>
                        </Table.Cell>
                        <Table.Cell>{artist.about.title}</Table.Cell>
                        <Table.Cell>{`${Math.floor(Math.random() * (3400 - 500 + 1) + 500)}€`}</Table.Cell>
                        <Table.Cell>{new Date(new Date('2025-04-10').getTime() + Math.random() * (new Date('2025-06-15').getTime() - new Date('2025-04-10').getTime())).toLocaleDateString("fr-FR")}</Table.Cell>
                        <Table.Cell className="text-end last:pr-2.5">
                          <Menu>
                            <Menu.Trigger>
                              <IconDotsVertical />
                            </Menu.Trigger>
                            <Menu.Content placement="left top">
                              <Menu.Item>
                                <Mail /> Contacter
                              </Menu.Item>
                              <Menu.Item>
                                <Edit /> Modifier
                              </Menu.Item>
                            </Menu.Content>
                          </Menu>
                        </Table.Cell>
                      </Table.Row>
                    )}
                  </Table.Body>
                </Table>
              </CardContent>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel id="progress">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Progression générale</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Rôles pourvus</span>
                      <span className="font-semibold">
                        {projectData.rolesFilledCount}/{projectData.totalRoles}
                      </span>
                    </div>
                    <Progress value={progressPercentage} className="h-3" />
                    <div className="text-center text-2xl font-bold text-indigo-600">{progressPercentage}% complété</div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Statut du projet</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Annonces actives</span>
                      <Badge className="bg-green-100 text-green-800">{announcements.length}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Invitations en attente</span>
                      <Badge intent="white/dark">3</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Artistes confirmés</span>
                      <Badge className="bg-green-100 text-green-800">{confirmedArtists.length}</Badge>
                    </div>
                    <div className="pt-4 border-t">
                      <div className="text-center">
                        {progressPercentage === 100 ? (
                          <Badge className="bg-green-100 text-green-800 text-lg px-4 py-2">Complet</Badge>
                        ) : progressPercentage >= 75 ? (
                          <Badge className="bg-yellow-100 text-yellow-800 text-lg px-4 py-2">Presque complet</Badge>
                        ) : (
                          <Badge className="bg-blue-100 text-blue-800 text-lg px-4 py-2">À compléter</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </Tabs.Panel>
        </Tabs>
      </main>

      {/* Modals */}
      <CreateAnnouncementModal open={showCreateAnnouncement} onOpenChange={setShowCreateAnnouncement} />
      <InviteArtistModal open={showInviteArtist} onOpenChange={setShowInviteArtist} />
    </div>
  )
}
