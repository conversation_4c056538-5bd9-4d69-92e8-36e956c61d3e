"use client"

import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"

import Link from "next/link"
import { Button, buttonStyles } from "@/components/ui/button"
import { Calendar, Copy, MapPin, PenTool, Trash2 } from "lucide-react"
import { Menu } from "@/components/ui/menu"
import { IconDotsVertical } from "@intentui/icons"
import { org_projects } from "@/data"


const getStatusBadge = (status: string) => {
  switch (status) {
    case "en_cours":
      return (
        <Badge intent="primary" className="bg-blue-100 text-blue-800">
          En cours
        </Badge>
      )
    case "complet":
      return (
        <Badge intent="primary" className="bg-green-100 text-green-800">
          Complet
        </Badge>
      )
    case "termine":
      return <Badge intent="secondary">Terminé</Badge>
    default:
      return <Badge intent="warning">Inconnu</Badge>
  }
}

const getProgressPercentage = (filled: number, total: number) => {
  return Math.round((filled / total) * 100)
}

export const AllOrgProjects = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {org_projects.map((project) => (
            <Card key={project.id} className="hover:shadow-lg transition-shadow">
              <Card.Header>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <Card.Title className="text-lg mb-2">{project.title}</Card.Title>
                    <div className="flex items-center text-sm text-gray-600 mb-2">
                      <MapPin className="w-4 h-4 mr-1" />
                      {project.location}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(project.startDate).toLocaleDateString("fr-FR")} -{" "}
                      {new Date(project.endDate).toLocaleDateString("fr-FR")}
                    </div>
                  </div>
                  <Menu>
                    <Menu.Trigger>
                      <IconDotsVertical />
                    </Menu.Trigger>
                    <Menu.Content placement="left top">
                      <Menu.Item>
                        <PenTool strokeWidth={1.2} className="size-4 mr-2"/> Modifier
                      </Menu.Item>
                      <Menu.Item>
                        <Copy strokeWidth={1.2} className="size-4 mr-2"/> Dupliquer
                      </Menu.Item>
                      <Menu.Item isDanger>
                        <Trash2 strokeWidth={1.2} className="size-4 mr-2"/> Supprimer
                      </Menu.Item>
                    </Menu.Content>
                  </Menu>
                </div>
              </Card.Header>
              <Card.Content>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    {getStatusBadge(project.status)}
                    <span className="text-sm text-gray-600">
                      Créé le {new Date(project.createdAt).toLocaleDateString("fr-FR")}
                    </span>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Rôles pourvus</span>
                      <span>
                        {project.rolesFilledCount}/{project.totalRoles}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-indigo-600 h-2 rounded-full transition-all"
                        style={{ width: `${getProgressPercentage(project.rolesFilledCount, project.totalRoles)}%` }}
                      />
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {getProgressPercentage(project.rolesFilledCount, project.totalRoles)}% complété
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Link href={`/mon-organisme/projets/${project.id}`} className={buttonStyles({className:"flex-1 justify-center", intent:"outline", size:"small"})}>
                        Voir détails
                    </Link>
                    <Button size="small" >
                      Gérer
                    </Button>
                  </div>
                </div>
              </Card.Content>
            </Card>
          ))}
    </div>
  )
}
