import ContainerWrapper from "@/components/artiste/container-wrapper";

import { Card } from "@/components/ui/card";
import { Badge, Calendar, Users } from "lucide-react";
import { AllOrgProjects } from "./components/all-org-projects";
import { org_projects } from "@/data";
import { CreateNewProject } from "./[id]/components/create-new-project";



export default function OrgProjectsPage() {
    return (
        <ContainerWrapper>
            {/* Header */}
            <header className="border-b border-border">
                <div className="flex justify-between items-center py-6">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Gestion des Projets</h1>
                        <p className="text-gray-600 mt-1">Plateforme de mise en relation pour danseurs et artistes</p>
                    </div>
                    <div className="">
                       <CreateNewProject/>
                    </div>
                </div>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 my-8">
                <Card>
                    <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <Card.Title className="text-sm font-medium">Total Projets</Card.Title>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </Card.Header>
                    <Card.Content>
                        <div className="text-2xl font-bold">{org_projects.length}</div>
                    </Card.Content>
                </Card>
                <Card>
                    <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <Card.Title className="text-sm font-medium">En Cours</Card.Title>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                    </Card.Header>
                    <Card.Content>
                        <div className="text-2xl font-bold">{org_projects.filter((p) => p.status === "en_cours").length}</div>
                    </Card.Content>
                </Card>
                <Card>
                    <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <Card.Title className="text-sm font-medium">Terminés</Card.Title>
                        <Badge className="h-4 w-4 text-muted-foreground" />
                    </Card.Header>
                    <Card.Content>
                        <div className="text-2xl font-bold">{org_projects.filter((p) => p.status === "termine").length}</div>
                    </Card.Content>
                </Card>
                <Card>
                    <Card.Header className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <Card.Title className="text-sm font-medium">Taux de Remplissage</Card.Title>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </Card.Header>
                    <Card.Content>
                        <div className="text-2xl font-bold">
                            {Math.round(
                                (org_projects.reduce((acc, p) => acc + p.rolesFilledCount, 0) /
                                    org_projects.reduce((acc, p) => acc + p.totalRoles, 0)) *
                                100,
                            )}
                            %
                        </div>
                    </Card.Content>
                </Card>
            </div>
            <AllOrgProjects/>
        </ContainerWrapper>
    )
}
