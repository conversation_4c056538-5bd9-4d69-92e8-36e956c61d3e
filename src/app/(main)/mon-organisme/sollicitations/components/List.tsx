"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Table } from "@/components/ui/table"



const solicitations = [
    {
        id: 1,
        title: "Performance Spéciale",
        company: "Festival d'Été",
        status: "en_attente",
        date: "2024-03-25",
        description: "Performance de danse contemporaine pour l'ouverture du festival"
    },
    {
        id: 2,
        title: "Spectacle de Danse",
        company: "Théâtre Municipal",
        status: "accepté",
        date: "2024-03-24",
        description: "Spectacle de danse classique pour la saison culturelle"
    },
    {
        id: 3,
        title: "Workshop de Danse",
        company: "Studio de Danse Pro",
        status: "en_cours",
        date: "2024-03-23",
        description: "Animation d'un workshop de danse contemporaine"
    },
    {
        id: 4,
        title: "Événement Privé",
        company: "Entreprise ABC",
        status: "rejeté",
        date: "2024-03-22",
        description: "Performance pour un événement d'entreprise"
    }
]
export const SollicationsListDash = () => {
    return (
        <>
            <Table aria-label="Sollicitations">
                <Table.Header>
                    <Table.Column>TITRE</Table.Column>
                    <Table.Column>ENTREPRISE</Table.Column>
                    <Table.Column>DATE</Table.Column>
                    <Table.Column>STATUT</Table.Column>
                    <Table.Column>ACTIONS</Table.Column>
                </Table.Header>
                <Table.Body items={solicitations}>
                    {(item) => (
                        <Table.Row key={item.id}>
                            <Table.Cell className="py-4">
                                <div className="flex flex-col">
                                    <span className="text-sm font-medium text-fg-title">{item.title}</span>
                                    <span className="text-xs text-fg-muted">{item.description}</span>
                                </div>
                            </Table.Cell>
                            <Table.Cell>{item.company}</Table.Cell>
                            <Table.Cell>{new Date(item.date).toLocaleDateString()}</Table.Cell>
                            <Table.Cell>
                                <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${item.status === 'en_attente' ? 'bg-yellow-50 text-yellow-800 dark:bg-yellow-400/10 dark:text-yellow-500' : item.status === 'accepté' ? 'bg-green-50 text-green-800 dark:bg-green-400/10 dark:text-green-500' : item.status === 'rejeté' ? 'bg-red-50 text-red-800 dark:bg-red-400/10 dark:text-red-500' : 'bg-blue-50 text-blue-800 dark:bg-blue-400/10 dark:text-blue-500'}`}>
                                    {item.status}
                                </span>
                            </Table.Cell>
                            <Table.Cell>
                                <div className="flex items-center gap-2">
                                    <Button intent="outline" size="small">Voir détails</Button>
                                    {item.status === 'en_attente' && (
                                        <>
                                            <Button intent="success" size="small">Accepter</Button>
                                            <Button intent="danger" size="small">Rejeter</Button>
                                        </>
                                    )}
                                </div>
                            </Table.Cell>
                        </Table.Row>
                    )}
                </Table.Body>
            </Table>
        </>
    )
}
