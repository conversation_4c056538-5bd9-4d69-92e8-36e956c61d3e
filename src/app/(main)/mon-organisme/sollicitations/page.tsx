

import { Card } from "@/components/ui/card"
import { WandSparkles } from "lucide-react"
import ContainerWrapper from "@/components/artiste/container-wrapper"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SollicationsListDash } from "./components/List"


export default function SollicitationsPage() {
    return (
        <ContainerWrapper className="grid">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between pt-6">
                <div className="flex items-center gap-4">
                    <div className="size-10 rounded-lg bg-bg-surface flex justify-center items-center border border-border/70">
                        <WandSparkles strokeWidth={1.2} className="size-5" />
                    </div>
                    <div className="flex-1 flex flex-col space-y-1">
                        <Card.Title>Sollicitations</Card.Title>
                        <Card.Description>Gérez les sollicitations reçues pour vos services.</Card.Description>
                    </div>
                </div>
                <Button intent="primary" size="small">Nouvelle sollicitation</Button>
            </div>

            <div className="mt-6 rounded-xl bg-bg border border-border/70 p-4 sm:p-6 overflow-hidden overflow-x-auto">
                <SollicationsListDash />
            </div>
        </ContainerWrapper>
    )
}