

import { getOpportunity } from '@/actions/operations'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>ontent, CardHeader } from '@/components/ui/card'
import { ArrowLeft, Building, Calendar, ChevronLeft, MapPin, Tag, Timer } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

type Props = {
    params: Promise<{ slug: string }>
}

export default async function MissionDetailPage({ params }: Props) {
    const slug = (await params).slug
    const mission = await getOpportunity(slug)

    if (!mission) {
        return (
            <main className="app-container px-4 sm:px-6 lg:px-4 py-8">
                <div className="flex">
                    <Button intent='outline' as-child>
                        <Link href={"/opportunites"} className="">
                            <ArrowLeft className='size-4 mr-1' />
                            Toutes les missions
                        </Link>
                    </Button>
                </div>
                <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
                    <div className="w-16 h-16 mb-4 text-fg-muted/30">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"
                            />
                        </svg>
                    </div>
                    <h1 className="text-2xl font-semibold text-fg-title mb-2">Mission not found</h1>
                    <p className="text-fg-muted max-w-md">
                        The mission you are looking for does not exist or has been removed.
                    </p>
                </div>
            </main>
        )
    }

    return (
        <main className="container mx-auto px-4 py-8">
            <Link href="/opportunites" className="inline-flex items-center text-fg-muted hover:text-fg-title mb-6">
                <ChevronLeft className="h-4 w-4 mr-1" />
                <span>Retour aux missions</span>
            </Link>

            <div className="flex flex-col md:flex-row gap-8">
                {/* Main Content */}
                <div className="w-full md:w-2/3">
                    <div className="bg-bg rounded-xl border border-border/70 overflow-hidden">
                        <div className="w-full aspect-video relative">
                            <Image
                                src={mission.coverImage}
                                alt={mission.jobTitle}
                                width={1200}
                                height={400}
                                className="size-full object-cover"
                            />
                        </div>

                        <div className="p-5">
                            <h1 className="text-2xl font-semibold text-fg-title mb-2">{mission.jobTitle}</h1>
                            <div className="flex items-center text-sm text-fg-muted">
                                <Timer strokeWidth={1.2} className="size-4 mr-2" />
                                {mission.periode.fin ? 'Du' : 'Pour'} {mission.periode.debut} {mission.periode.fin ? 'Au' : ''} {mission.periode.fin ?? ''}
                            </div>
                            <div className="flex flex-wrap gap-4 py-4 border-y border-border my-4 text-sm text-fg-muted">
                                <div className="flex items-center">
                                    <MapPin strokeWidth={1.2} className="mr-1 size-4" />
                                    <span>{mission.location.city}, {mission.location.country}</span>
                                </div>

                                <div className="flex items-center">
                                    <Calendar strokeWidth={1.2} className="mr-1 size-4" />
                                    <span>Publié le {mission.datePublished}</span>
                                </div>

                                <div className="flex items-center">
                                    <Tag strokeWidth={1.2} className="mr-1 size-4" />
                                    <span>{mission.genre}</span>
                                </div>
                            </div>

                            <div className="max-w-none">
                                <h2 className="text-xl font-semibold mb-4">Description de la mission</h2>
                                <p className="whitespace-pre-line">{mission.description}</p>

                                <h2 className="text-xl font-semibold mt-6 mb-4">Compétences requises</h2>
                                <div className="flex flex-wrap gap-2">
                                    {mission.skillsRequired.map((skill) => (
                                        <Badge key={skill} intent="white/dark" className="rounded-lg">
                                            {skill}
                                        </Badge>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="mt-6 bg-accent/10 rounded-xl p-5 border border-accent/20">
                        <h2 className="text-lg font-semibold mb-3">Cette mission vous intéresse?</h2>
                        <p className="text-neutral-600 dark:text-neutral-300 mb-4">
                            Si vous avez les compétences recherchées, n'hésitez pas à postuler pour cette mission.
                        </p>
                        <Button intent="white/dark" size="extra-small" as-child>
                            <Link href={`/opportunites/${mission.slug}/postuler`}>
                                Postuler à cette mission
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Sidebar */}
                <div className="w-full md:w-1/3">
                    <div className="sticky top-24">
                        <div className="bg-bg border border-border/70 rounded-xl p-5">
                            <CardHeader className='flex justify-center'>
                                <span className="flex items-center text-center text-sm text-fg-muted">
                                    <Building strokeWidth={1.2} className="mr-1 size-4" />
                                    <span>À propos de l'organisation</span>
                                </span>
                            </CardHeader>
                            <CardContent>
                                <div className="text-center">
                                    <div className="w-16 h-16 mx-auto rounded-full overflow-hidden">
                                        <Image
                                            src="/avatar_def1.webp"
                                            alt={mission.organization.name}
                                            width={200}
                                            height={200}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                    <h3 className="text-lg font-semibold mt-2">{mission.organization.name}</h3>
                                    <p className="text-sm text-fg-muted">{mission.location.city}</p>
                                    <div className="mt-4 flex justify-center">
                                        <Button intent="outline" className="w-max" as-child>
                                            <Link href={`/organizations/${mission.organization.slug}`}>
                                                Voir le profil de l'organisation
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </div>

                        <Button intent="white/dark" className={"mt-5 w-full justify-center"} as-child>
                            <Link href={`/opportunites/${mission.slug}/postuler`} className='w-full flex justify-center rounded-xl'>
                                Postuler maintenant
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </main>
    )
}