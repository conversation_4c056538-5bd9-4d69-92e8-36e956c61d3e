"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Select } from "@/components/ui/select"
import { TextField } from "@/components/ui/text-field"
import { Textarea } from "@/components/ui/textarea"
import Link from "next/link"

export const FormApplyMission = () => {
    return (
        <>
            <form className="space-y-6 mt-6">
                <div className="grid md:grid-cols-2 gap-6">
                    <TextField label='Nom commplet' placeholder="<PERSON>" name="fullName" />
                    <TextField label='Email' type="email" placeholder="<EMAIL>" name="email" />
                    <TextField label='Téléphone' placeholder="+33 6 12 34 56 78" name="phone" />
                    <Select label="Niveau d'expérience" name="experienceLevel">
                        <Select.Trigger />
                        <Select.List>
                            <Select.Option textValue="Sélectionnez votre niveau" id="">Sélectionnez votre niveau</Select.Option>
                            <Select.Option textValue="Junior (0-2 ans)" id="junior">Junior (0-2 ans)</Select.Option>
                            <Select.Option textValue="Intermédiaire (3-5 ans)" id="intermediate">Intermédiaire (3-5 ans)</Select.Option>
                            <Select.Option textValue="Sénior (6-9 ans)" id="senior">Sénior (6-9 ans)</Select.Option>
                            <Select.Option textValue="Expert (10+ ans)" id="expert">Expert (10+ ans)</Select.Option>
                        </Select.List>
                    </Select>
                </div>



                <Select label='Disponibilité' name="availability">
                    <Select.Trigger />
                    <Select.List>
                        <Select.Option textValue="Quand êtes-vous disponible?" id="">Quand êtes-vous disponible?</Select.Option>
                        <Select.Option textValue="Immédiatement" id="immediately">Immédiatement</Select.Option>
                        <Select.Option textValue="Dans 1 semaine" id="one_week">Dans 1 semaine</Select.Option>
                        <Select.Option textValue="Dans 2 semaines" id="two_weeks">Dans 2 semaines</Select.Option>
                        <Select.Option textValue="Dans 1 mois" id="one_month">Dans 1 mois</Select.Option>
                        <Select.Option textValue="Dates spécifiques" id="custom">Dates spécifiques</Select.Option>
                    </Select.List>
                </Select>

                <Textarea label='Lettre de motivation'
                    name="coverLetter"
                    placeholder="Expliquez pourquoi vous êtes intéressé par cette mission et pourquoi vous êtes qualifié..."
                    className="min-h-32"
                />

                <div className="flex items-start space-x-3 mt-6">
                    <Checkbox name="acceptTerms" >
                        <span>
                            J'accepte les <Link href="#" className="text-accent hover:underline">conditions générales</Link> et la <Link href="#" className="text-accent hover:underline">politique de confidentialité</Link>.
                        </span>
                    </Checkbox>
                </div>

                <div className="pt-4 border-t border-neutral-200 dark:border-neutral-800 flex justify-end">
                    <Button type="submit" size="large" className="w-full md:w-auto">
                        Soumettre ma candidature
                    </Button>
                </div>
            </form>
        </>
    )
}
