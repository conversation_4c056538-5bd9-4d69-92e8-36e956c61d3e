

import { But<PERSON> } from '@/components/ui/button'
import { opportunites } from '@/data/opportunites'
import { ArrowLeft, Calendar, ChevronLeft, MapPin, Tag } from 'lucide-react'

import Link from 'next/link'

import { Note } from '@/components/ui/note'
import { FormApplyMission } from './form-apply-mission'

type Props = {
    params: Promise<{ slug: string }>
}

export default async function MissionDetailApplyPage({ params }: Props) {
    const slug = (await params).slug
    const mission = opportunites.find((m) => m.slug === slug)

    if (!mission) {
        return (
            <main className="app-container px-4 sm:px-6 lg:px-4 py-8">
                <div className="flex">
                    <Button intent='outline' as-child>
                        <Link href={"/opportunites"} className="">
                            <ArrowLeft className='size-4 mr-1' />
                            Toutes les missions
                        </Link>
                    </Button>
                </div>
                <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
                    <div className="w-16 h-16 mb-4 text-fg-muted/30">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"
                            />
                        </svg>
                    </div>
                    <h1 className="text-2xl font-semibold text-fg-title mb-2">Mission not found</h1>
                    <p className="text-fg-muted max-w-md">
                        The mission you are looking for does not exist or has been removed.
                    </p>
                </div>
            </main>
        )
    }

    return (
        <main className="container mx-auto px-4 py-8">
            <Link href={`/opportunites/${mission.slug}`} className="inline-flex items-center text-fg-muted hover:text-fg-title mb-6">
                <ChevronLeft strokeWidth={1.2} className="size-4 mr-1" />
                <span>Retour aux détails de la mission</span>
            </Link>

            <div className="flex-1">
                <div className="max-w-3xl mx-auto">
                    <div className="bg-bg rounded-xl border border-border/70 overflow-hidden mb-8">
                        <div className="p-6">
                            <h1 className="text-2xl font-semibold text-fg-title mb-2">Postuler à la mission</h1>
                            <h2 className="text-xl font-medium text-fg mb-6">{mission.jobTitle}</h2>

                            <div className="flex flex-wrap gap-4 py-4 border-y border-border my-4 text-sm text-fg-muted">
                                <div className="flex items-center">
                                    <MapPin strokeWidth={1.2} className="mr-1 size-4" />
                                    <span>{mission.location.city}, {mission.location.country}</span>
                                </div>

                                <div className="flex items-center">
                                    <Calendar strokeWidth={1.2} className="mr-1 size-4" />
                                    <span>Publié le {mission.datePublished}</span>
                                </div>

                                <div className="flex items-center">
                                    <Tag strokeWidth={1.2} className="mr-1 size-4" />
                                    <span>{mission.genre}</span>
                                </div>
                            </div>
                            <Note intent="info">
                                <p className="text-sm text-blue-800 dark:text-blue-300">
                                    Complétez le formulaire ci-dessous pour postuler à cette mission. Assurez-vous de fournir toutes les informations demandées pour maximiser vos chances d'être sélectionné.
                                </p>
                            </Note>
                            <FormApplyMission />
                        </div>
                    </div>
                </div>
            </div>
        </main>
    )
}