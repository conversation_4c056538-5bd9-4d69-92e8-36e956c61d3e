"use client"

import { Input } from '@/components/ui/field'
import { Form } from '@/components/ui/form'
import { Search } from 'lucide-react'
import React from 'react'
import { HeaderFilter } from './header-filter'
import { useQueryState } from 'nuqs'

export const HeadBarSearchOpportunities = () => {
    const [recherche, setRecherche] = useQueryState("recherche", { defaultValue: "" });
    return (
        <>
            <div className="w-full h-14 border-b bg-bg/40 backdrop-blur-2xl pt-0.5 sticky top-16 flex items-center justify-between z-30 gap-4">
                <div className="flex-1">
                    <Form className="w-full max-w-md p-0.5 rounded-xl bg-bg flex items-center focus-within:border-primary border shadow-sm focus-within:shadow-primary/20">
                        <Input defaultValue={recherche} onChange={e => setRecherche(e.target.value)} placeholder="Rechercher des missions, mots cles..." className={"flex-1 flex h-9"} />
                        <span className={"size-9 flex items-center justify-center ml-3 text-fg-muted/70"}>
                            <Search className="size-4" />
                        </span>
                    </Form>
                </div>
                <HeaderFilter />
            </div>
        </>
    )
}
