"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/field"
import { Modal } from "@/components/ui/modal"
import { Radio, RadioGroup } from "@/components/ui/radio"
import { Select } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { ListFilter, MapIcon } from "lucide-react"
import { useQueryState } from "nuqs"


const cities = [
  {
    id: 1,
    name: "Brussels"
  },
  {
    id: 2,
    name: "Antwerp"
  },
  {
    id: 3,
    name: "Ghent"
  },
  {
    id: 4,
    name: "Charleroi"
  },
  {
    id: 5,
    name: "Liège"
  },
  {
    id: 6,
    name: "Bruges"
  },
  {
    id: 7,
    name: "<PERSON><PERSON>"
  },
  {
    id: 8,
    name: "Leuven"
  },
  {
    id: 9,
    name: "<PERSON><PERSON>"
  },
  {
    id: 10,
    name: "<PERSON>als<PERSON>"
  },
  {
    id: 11,
    name: "Mechelen"
  },
  {
    id: 12,
    name: "<PERSON><PERSON><PERSON>"
  },
  {
    id: 13,
    name: "<PERSON><PERSON><PERSON><PERSON>"
  },
  {
    id: 14,
    name: "Ostend"
  }
]
export const HeaderFilter = () => {
  const [localisation, setLocalisation] = useQueryState("localisation", { defaultValue: "" })

  return (
    <div className="relative w-max min-w-max">
      <Modal >
        <Button className={""} size="small" intent="outline">
          <ListFilter strokeWidth={1.2} className="size-4" />
          Filtre
        </Button>
        <Modal.Content isBlurred size="xl">
          <Modal.Header>
            <Modal.Title>Filtre</Modal.Title>
          </Modal.Header>
          <Modal.Body className="py-3 space-y-7">
            <div className="flex flex-col space-y-2">
              <Label htmlFor="location">
                Ville
              </Label>
              <Select defaultSelectedKey={localisation} onSelectionChange={e => { setLocalisation(e?.toString() ?? '') }} placeholder="Selectionner une ville">
                <Select.Trigger />
                <Select.List items={cities}>
                  {(item) => (
                    <Select.Option id={item.id} textValue={item.name}>
                      {item.name}
                    </Select.Option>
                  )}
                </Select.List>
              </Select>
            </div>
            <div className="grid sm:grid-cols-2 gap-8">
              <div>
                <span className="font-semibold text-fg-title">
                  Type d'artiste
                </span>
                <RadioGroup className={"mt-2 gap-4"}>
                  <Radio value="fs">Danceur</Radio>
                  <Radio value="dr">Choreographer</Radio>
                  <Radio value="ss">Animator</Radio>
                  <Radio value="s">Performer</Radio>
                </RadioGroup>
              </div>
              <div>
                <span className="font-semibold text-fg-title">
                  Competences
                </span>
                <RadioGroup className={"mt-2 grid grid-cols-2 gap-4"}>
                  <Radio value="fs">Contemporary</Radio>
                  <Radio value="dr">Ballroom</Radio>
                  <Radio value="ss">Jazz</Radio>
                  <Radio value="s">Hip Hop</Radio>
                </RadioGroup>
              </div>
            </div>
            <div>
              <span className="font-semibold text-fg-title">
                Options
              </span>
              <div className="mt-2 flex flex-col gap-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="prefered_location" className="text-sm text-fg flex items-center font-medium select-none">
                    <MapIcon strokeWidth={1.2} className="size-4 mr-1.5" />
                    Uniquement dans ma region
                  </Label>
                  <Switch id="prefered_location" />
                </div>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer className="flex">
            <div className="flex justify-between items-center w-full">
              <div className="">
                <Button intent="none" className={"underline cursor-pointer"}>
                  Vider le filtre
                </Button>
              </div>
              <div className="flex">
                <Button intent="white/dark" className="w-full">
                  Filtrer le resultat
                </Button>
              </div>
            </div>
          </Modal.Footer>
        </Modal.Content>
      </Modal>
    </div>
  )
}