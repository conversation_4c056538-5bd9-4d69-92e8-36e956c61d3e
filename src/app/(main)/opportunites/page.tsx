import "./../app-ui.css"

import { Search } from "lucide-react"
import { HeaderFilter } from "./components/header-filter"
import { Form } from "@/components/ui/form"
import { Input } from "@/components/ui/field"
import { OpportunityCard } from "@/components/molecules/opportunite-card"
import { opportunites } from "@/data/opportunites"
import { Button } from "@/components/ui/button"
import { SearchParams } from "nuqs"
import { loadSearchParams } from "./search-params"
import { getAppQueryClient } from "@/lib/queryClient"
import { getFilteredOpportunities } from "@/actions/operations"
import { GLOBAL_STALE_TIME } from "@/app/const"
import { dehydrate, HydrationBoundary } from "@tanstack/react-query"
import { ClientOpportunities } from "./components/load-client-opportunities"
import { HeadBarSearchOpportunities } from "./components/head-bar-search"



type PageProps = {
    searchParams: Promise<SearchParams>
}
export default async function MissionsPage({ searchParams }: PageProps) {
    const { recherche, localisation } = await loadSearchParams(searchParams)
    const queryClient = getAppQueryClient()

    queryClient.prefetchInfiniteQuery({
        queryKey: ['opportunities-with-filter', recherche, localisation],
        queryFn: () => getFilteredOpportunities({ search: recherche, limit:8, location: localisation }),
        initialPageParam: 1,
        staleTime: GLOBAL_STALE_TIME,
    })
    return (
        <HydrationBoundary state={dehydrate(queryClient)}>
            <main className="app-container px-4 sm:px-6 lg:px-4">
               <HeadBarSearchOpportunities/>
                <ClientOpportunities/>
            </main>
        </HydrationBoundary>
    )
}
