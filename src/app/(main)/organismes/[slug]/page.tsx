import { getOrganisme } from '@/actions/operations'
import { ArtistCard } from '@/components/artiste/artist-show-card'
import { artisteProfiles } from '@/data/artiste-profiles'
import { Briefcase, Building, ChevronLeft, MapPin } from 'lucide-react'
import Link from 'next/link'
import React from 'react'


type Props = {
  params: Promise<{ slug: string }>
}

export default async function PageSingleOrganisme({ params }: Props) {
  const slug = (await params).slug
  const agency = await getOrganisme(slug)

  if (!agency) {
    return <div>Agency not found</div>
  }

  return (
    <>
      <main className="mx-auto w-full max-w-6xl py-8">
        <Link href="/organismes" className="inline-flex items-center text-fg-muted hover:text-fg-title mb-6">
          <ChevronLeft className="h-4 w-4 mr-1" />
          <span>Tous les organismes</span>
        </Link>

        <div className="flex flex-col md:flex-row gap-8">
          <div className="md:w-1/3">
            <div className="sticky top-24">
              <div className="bg-bg border  rounded-xl overflow-hidden">
                <div className="h-48 overflow-hidden">
                  <img
                    src={agency.profileImage}
                    alt={agency.name}
                    className="w-full h-full object-cover"
                  />
                </div>

                <div className="p-6">
                  <h1 className="text-2xl font-semibold mb-2 text-fg-title">{agency.name}</h1>

                  <div className="flex items-center text-fg-muted text-sm mb-4">
                    <MapPin className="mr-2 h-4 w-4" />
                    <span>{agency.location}</span>
                  </div>


                  <div className="mt-6">
                    <h3 className="text-sm font-semibold text-fg-muted uppercase tracking-wide mb-3">
                      À propos
                    </h3>
                    <p className="text-sm text-fg">
                      {agency.description}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="md:w-2/3 space-y-6">
            <div className="bg-bg border rounded-xl p-5 sm:p-6">
              <h2 className="text-xl font-semibold text-fg-title mb-6 flex items-center">
                <Briefcase strokeWidth={1.2} className="mr-2 size-4 text-fg-muted" />
                <span>Prestations à venir de l'organisme</span>
              </h2>

              <div className="text-center py-12 border border-dashed border-neutral-200 dark:border-neutral-800 rounded-lg">
                <Building className="mx-auto h-12 w-12 text-neutral-400 dark:text-neutral-600 mb-3" />
                <h3 className="text-lg font-medium mb-1">Aucune prestation à venir </h3>
                <p className="text-neutral-500 dark:text-neutral-400">
                  L'organisme n'a pas de prestations en cours pour le moment.
                </p>
              </div>
            </div>
            <div className="bg-bg border rounded-xl p-5 sm:p-6">
              <h2 className="text-xl font-semibold text-fg-title mb-6 flex items-center">
                <Briefcase strokeWidth={1.2} className="mr-2 size-4 text-fg-muted" />
                <span>Artistes prestataires</span>
              </h2>
              <div className="grid sm:grid-cols-2 gap-4">
                <ArtistCard {...artisteProfiles[4]}/>
                <ArtistCard {...artisteProfiles[7]}/>
                <ArtistCard {...artisteProfiles[5]}/>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}
