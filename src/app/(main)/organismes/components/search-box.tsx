"use client"

import { Input } from "@/components/ui/field"
import { Search } from "lucide-react"
import { useQueryState } from "nuqs";

export const OrganismSearchBox = () => {
    const [recherche, setRecherche] = useQueryState("recherche", { defaultValue: "" });

    return (
        <>
            <div className="w-full max-w-md mx-auto relative p-0.5 pr-2 rounded-xl bg-bg items-center focus-within:border-primary border shadow-sm focus-within:shadow-primary/20 hidden md:flex">
                <Input defaultValue={recherche} onChange={e => setRecherche(e.target.value)} placeholder="Rechercher..." className={"flex-1 flex h-9 ps-9"} />
                <span className="mx-3 w-0.5 h-3/5 bg-bg-surface"></span>
                <Search className="size-4 absolute left-3 top-3" />
            </div>
        </>
    )
}
