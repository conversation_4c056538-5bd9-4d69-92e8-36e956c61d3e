import "./../app-ui.css"

import { Container } from "@/components/ui/container";
import { OrganismSearchBox } from "./components/search-box";
import { CtaOrg } from "@/components/organisms/cta-org";
import { drukCondFont } from "@/app/fonts";
import { SearchParams } from "nuqs";
import { loadSearchParams } from "./search-params";
import { LoadClientOrganisms } from "./components/load-client-organisms";
import { getFilteredOrganismes } from "@/actions/operations";
import { GLOBAL_STALE_TIME } from "@/app/const";
import { getAppQueryClient } from "@/lib/queryClient";
import { dehydrate, HydrationBoundary } from "@tanstack/react-query";


type PageProps = {
  searchParams: Promise<SearchParams>
}

export default async function PageAllOrganismes({ searchParams }: PageProps) {
  const { recherche } = await loadSearchParams(searchParams)
  const queryClient = getAppQueryClient()

  queryClient.prefetchInfiniteQuery({
    queryKey: ['organismes-with-filter', recherche],
    queryFn: () => getFilteredOrganismes({ search: recherche }),
    initialPageParam: 1,
    staleTime: GLOBAL_STALE_TIME,
  })
  return (
    <>
      <HydrationBoundary state={dehydrate(queryClient)}>
        <main>
          <Container className="relative mt-2">
            <div className="relative border-y border-border/70 grid">
              <div className="inset-y-0 inset-x-4 md:inset-x-20 absolute border-x border-border/40 isolate">
                <div className="absolute opacity-30 dark:opacity-70 [--grid-color:var(--color-bg-surface-muted)] dark:[--grid-color:var(--color-bg-surface)] inset-0 -z-10 h-full w-full bg-bg bg-[linear-gradient(to_right,var(--grid-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--grid-color)_1px,transparent_1px)] bg-[size:6rem_4rem]"></div>
              </div>
              <div className="md:max-w-3xl mx-auto py-12 md:py-16 space-y-6 w-full relative text-center">
                <h1 className={`font-semibold uppercase text-fg-title text-4xl sm:text-6xl max-w-4xl mx-auto text-balance ${drukCondFont.className}`}>
                  Decouvrez, Explorez les organismes
                </h1>
                <OrganismSearchBox />
              </div>
            </div>
            <article>
              <LoadClientOrganisms />
            </article>
            <CtaOrg />
          </Container>
        </main>
      </HydrationBoundary>
    </>
  )
}
