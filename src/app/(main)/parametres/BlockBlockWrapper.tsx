import { type ReactNode } from 'react';

export const BlockBlockWrapper = ({ title, description, children }: { title: string; description: string; children: ReactNode; }) => {

  return <div className="flex flex-col md:flex-row items-start gap-8 md:gap-10 lg:gap-16 z-[3] relative">
    <div className="w-full max-w-md flex flex-col md:sticky md:top-36">
      <h1 className="font-semibold text-lg text-fg-title">{title}</h1>
      <p className="text-sm text-fg-muted mt-2">{description}</p>
    </div>
    <div className="flex-1 bg-bg dark:bg-bg-surface/70 border border-border/70 p-5 lg:p-8 rounded-xl z-[2]">
      {children}
    </div>
  </div>;
};
