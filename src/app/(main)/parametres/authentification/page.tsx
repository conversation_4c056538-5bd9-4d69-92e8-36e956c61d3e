import ContainerWrapper from '@/components/artiste/container-wrapper'
import { BlockBlockWrapper } from '../BlockBlockWrapper'
import { Form } from '@/components/ui/form'
import { ReactNode } from 'react'
import { Settings2 } from 'lucide-react'
import { TextField } from '@/components/ui/text-field'
import { Button } from '@/components/ui/button'


const HeaderForm = ({ title, description, icon }: { title: string, description: string, icon: ReactNode }) => {
    return (
        <div className="flex items-center gap-4 pb-4 border-b border-border mb-5">
            <span className="size-12 flex items-center justify-center bg-bg-surface border border-border rounded-xl">
                {icon}
            </span>
            <div className="flex-1">
                <h2 className="text-xl font-semibold text-fg-title">
                    {title}
                </h2>
                <p className="text-fg-muted">
                    {description}
                </p>
            </div>
        </div>
    )
}

export default function AuthSettingProfile() {
    return (
        <ContainerWrapper className="pt-8 space-y-10 max-w-7xl">
            <BlockBlockWrapper title="Informations Personnelles" description='Gérez vos informations de compte et coordonnées'>
                <Form>
                    <HeaderForm title="Détails du Compte" description="Informations essentielles de votre compte" icon={<Settings2 strokeWidth={1.2} className="size-5"/>}/>
                    <div className="grid gap-6">
                        <TextField label="Nom d'utilisateur"/>
                        <TextField className='' label="Adresse e-mail" type="email"/>
                        <TextField label="Numéro de téléphone" type="number"/>
                    </div>
                    <div className="mt-5">
                        <Button>
                            Sauvegarder les modifications
                        </Button>
                    </div>
                </Form>
            </BlockBlockWrapper>
            <BlockBlockWrapper title="Sécurité du Compte" description='Modifiez votre mot de passe pour sécuriser votre compte'>
            <Form>
                    <HeaderForm title="Modification du Mot de Passe" description="Mettez à jour vos informations de connexion" icon={<Settings2 strokeWidth={1.2} className="size-5"/>}/>
                    <div className="grid gap-6">
                        <TextField label="Mot de passe actuel" type="password"/>
                        <TextField className='' label="Nouveau mot de passe" type="password"/>
                        <TextField label="Confirmation du mot de passe" type="password"/>
                    </div>
                    <div className="mt-5">
                        <Button>
                            Mettre à jour le mot de passe
                        </Button>
                    </div>
                </Form>
            </BlockBlockWrapper>
        </ContainerWrapper>
    )
}
