"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Modal } from "@/components/ui/modal"
import { UserCog, X } from "lucide-react"
import { TextField } from '@/components/ui/text-field'




export const EditProfileSkill = () => {

    return (
        <div className="relative w-max min-w-max">
            <Modal>
                <Button intent="outline" size="small" className="h-8">
                    Modifier
                </Button>
                <Modal.Content classNames={{ content: "w-full" }} isBlurred size="2xl">
                    <Modal.Header className="bg-bg-surface/50">
                        <div className="flex items-center gap-4">
                            <span className="size-12 flex items-center justify-center bg-bg-surface border border-border rounded-xl">
                                <UserCog strokeWidth={1.2} className="size-5" />
                            </span>
                            <div className="flex-1">
                                <Modal.Title className="text-xl font-semibold">
                                    Nouvelle experience
                                </Modal.Title>
                                <Modal.Description className="text-fg-muted">
                                    Partagez votre expérience
                                </Modal.Description>
                            </div>
                        </div>
                    </Modal.Header>
                    <Modal.Body className="py-3 space-y-7 w-full">
                        <div className="flex flex-col w-full">
                            <div className="grid gap-6">
                                <TextField label="Experience" />
                                <div className="flex flex-wrap gap-2">
                                    <div className="bg-bg-elevated text-fg pl-2 pr-3 py-0.5 rounded-xl flex items-center gap-2">
                                        <span className="text-sm">
                                            Skill 1
                                        </span>
                                        <Button className={"size-4 px-0 py-0 text-danger"} intent="none" size="extra-small">
                                            <X strokeWidth={1.2} className="size-4" />
                                        </Button>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </Modal.Body>
                    <Modal.Footer className="flex">
                        <Button>
                            Enregistrer
                        </Button>
                    </Modal.Footer>
                </Modal.Content>
            </Modal>
        </div>
    )
}
