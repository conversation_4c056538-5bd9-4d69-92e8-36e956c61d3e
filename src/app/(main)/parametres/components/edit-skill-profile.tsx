"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Modal } from "@/components/ui/modal"
import { ListFilter } from "lucide-react"


export const EditpSkillsProfile = () => {
    return (
        <div className="relative w-max min-w-max">
            <Modal >
                <Button className={""} size="small" intent="outline">
                    <ListFilter strokeWidth={1.2} className="size-4" />
                    Filtre
                </Button>
                <Modal.Content isBlurred size="xl">
                    <Modal.Header>
                        <Modal.Title>Filtre</Modal.Title>
                    </Modal.Header>
                    <Modal.Body className="py-3 space-y-7">

                    </Modal.Body>
                    <Modal.Footer className="flex">
                        <div className="flex justify-between items-center w-full">
                            <div className="">

                            </div>
                            <div className="flex">
                                <Button className="w-full">
                                    Enregistrer
                                </Button>
                            </div>
                        </div>
                    </Modal.Footer>
                </Modal.Content>
            </Modal>
        </div>
    )
}
