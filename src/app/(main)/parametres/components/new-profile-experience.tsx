"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePicker } from "@/components/ui/date-picker"

import { Modal } from "@/components/ui/modal"
import { Select } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { UserCog } from "lucide-react"
import { TextField } from '@/components/ui/text-field'
import { TagField } from "@/components/ui/tag-field"
import { useListData } from "react-stately"



const cities = [
    {
        id: 1,
        name: "Brussels"
    },
    {
        id: 2,
        name: "Antwerp"
    },
    {
        id: 3,
        name: "Ghent"
    },
    {
        id: 4,
        name: "Charleroi"
    },
    {
        id: 5,
        name: "Liège"
    },
    {
        id: 6,
        name: "Bruges"
    },
    {
        id: 7,
        name: "<PERSON><PERSON>"
    },
    {
        id: 8,
        name: "Leuven"
    },
    {
        id: 9,
        name: "<PERSON><PERSON>"
    },
    {
        id: 10,
        name: "<PERSON><PERSON><PERSON>"
    },
    {
        id: 11,
        name: "Mechelen"
    },
    {
        id: 12,
        name: "<PERSON><PERSON><PERSON>"
    },
    {
        id: 13,
        name: "<PERSON><PERSON><PERSON><PERSON>"
    },
    {
        id: 14,
        name: "Ostend"
    }
]

export const NewProfileExperience= () => {
    const selectedItems = useListData({
        initialItems: [
          {
            id: 1,
            name: "Danse",
          },
          {
            id: 2,
            name: "Animation",
          },
        ],
      })
    return (
        <div className="relative w-max min-w-max">
            <Modal>
                <Button size="small" className="h-8">
                    Ajouter
                </Button>
                <Modal.Content classNames={{ content: "w-full" }} isBlurred size="2xl">
                    <Modal.Header className="bg-bg-surface/50">
                        <div className="flex items-center gap-4">
                            <span className="size-12 flex items-center justify-center bg-bg-surface border border-border rounded-xl">
                                <UserCog strokeWidth={1.2} className="size-5" />
                            </span>
                            <div className="flex-1">
                                <Modal.Title className="text-xl font-semibold">
                                    Nouvelle experience
                                </Modal.Title>
                                <Modal.Description className="text-fg-muted">
                                    Partagez votre expérience
                                </Modal.Description>
                            </div>
                        </div>
                    </Modal.Header>
                    <Modal.Body className="py-3 space-y-7 w-full">
                        <div className="flex flex-col w-full">
                            <div className="grid sm:grid-cols-2 gap-6">
                                <TextField label="Intitule" />
                                <Select label="Organisation" placeholder="Selectionner une organisation">
                                    <Select.Trigger />
                                    <Select.List items={[
                                        { id: 1, name: 'Live Nation Entertainment' },
                                        { id: 2, name: 'AEG Presents' },
                                        { id: 3, name: 'Brussels Expo' },
                                        { id: 4, name: 'Forest National' },
                                        { id: 5, name: 'Sportpaleis Group' },
                                        { id: 6, name: 'Rock Werchter' },
                                        { id: 7, name: 'Tomorrowland' },
                                        { id: 8, name: 'Ancienne Belgique' },
                                        { id: 9, name: 'Palais 12' },
                                        { id: 10, name: 'BOZAR' }
                                    ]}>
                                        {(item) => (
                                            <Select.Option id={item.id} textValue={item.name}>
                                                {item.name}
                                            </Select.Option>
                                        )}
                                    </Select.List>
                                </Select>
                                <div className="col-span-full">
                                    <Select label="Ville" placeholder="Selectionner une ville">
                                        <Select.Trigger />
                                        <Select.List items={cities}>
                                            {(item) => (
                                                <Select.Option id={item.id} textValue={item.name}>
                                                    {item.name}
                                                </Select.Option>
                                            )}
                                        </Select.List>
                                    </Select>
                                </div>
                                <DatePicker label="Debut prestation" />
                                <DatePicker label="Fin prestation" />
                                <Checkbox label="Encore sous contrat" />
                                <div className="col-span-2">
                                <TagField className=" max-w-full" label="Competences" list={selectedItems} />
                                </div>
                                <div className="col-span-full">
                                    <Textarea label="Description de la mission" />
                                </div>
                            </div>
                        </div>
                    </Modal.Body>
                    <Modal.Footer className="flex">
                        <Button>
                            Enregistrer
                        </Button>
                    </Modal.Footer>
                </Modal.Content>
            </Modal>
        </div>
    )
}
