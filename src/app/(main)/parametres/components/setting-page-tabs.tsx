"use client"

import { KeySquare, User<PERSON><PERSON> } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

const dashItems = [
    {
        id: 1,
        text: "Informations",
        href: "/mon-espace/parametres/",
        icon: <UserCog strokeWidth={1.2} className="size-5" />
    }, {
        id: 2,
        text: "Authentification",
        href: "/mon-espace/parametres/authentification",
        icon: <KeySquare strokeWidth={1.2} className="size-5" />
    },
]

export default function SettingPageTabs() {
    const pathname = usePathname()
    return (
        <div className="w-full h-14 border-b bg-bg/40 backdrop-blur-2xl pt-0.5 sticky top-16 z-30 flex items-center">
            <ul className="flex items-center gap-2 app-container px-4 sm:px-6 lg:px-4 text-sm md:text-base text-fg h-full">
                {
                    dashItems.map(item => <li key={item.id} className="px-2 border-b-2 border-transparent data-[state=active]:border-primary h-full flex items-center" data-state={pathname === item.href ? "active" : ""}>
                        <Link 
                            href={item.href} 
                            className="flex items-center gap-2 in-[[data-state=active]]:text-fg-primary"
                        >
                            {item.icon}
                            {item.text}
                        </Link>
                    </li>)
                }
            </ul>
        </div>
    )
}
