
import { AccessUserWrapper } from "@/components/access-user-wrapper";
import { GlobalAppDashNav } from "@/components/organisms/global-app-dash-nav";
import { UserCog, KeySquare } from "lucide-react";


const dashItems = [
    {
        id: 1,
        text: "Informations",
        href: "/parametres",
        icon: <UserCog strokeWidth={1.2} className="size-5" />
    }, {
        id: 2,
        text: "Authentification",
        href: "/parametres/authentification",
        icon: <KeySquare strokeWidth={1.2} className="size-5" />
    },
]


export default function DashLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <>
            <AccessUserWrapper noAccess={<></>}>
                <GlobalAppDashNav items={dashItems} />
                {children}
            </AccessUserWrapper>
        </>
    );
}