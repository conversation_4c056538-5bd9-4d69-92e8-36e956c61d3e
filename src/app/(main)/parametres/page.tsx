import ContainerWrapper from '@/components/artiste/container-wrapper'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Calendar, Globe, Mail, MapPin, Mars, Phone, Trash } from 'lucide-react'
import Image from 'next/image'
import React from 'react'
import { BlockBlockWrapper } from './BlockBlockWrapper'
import { ChangeProfilePicture } from '@/components/artiste/change-profile-picture'
import { EditpProfileSettings } from './components/edit-profile-settings'
import { EditProfileSkill } from './components/edit-profile-skill'
import { NewProfileExperience } from './components/new-profile-experience'
import { ProfileJobPreference } from './components/job-preference'
import { ArtUserWrapper } from '@/components/art-user-wrapper'
import { OrgUserWrapper } from '@/components/org-user-wrapper'


export default function HomeSettingProfile() {
  return (
    <>
      <ArtUserWrapper noAccess={<></>}>
        <ContainerWrapper className="pt-8 space-y-10">
          <BlockBlockWrapper title='Profil Personnel' description='Gérez vos informations personnelles et professionnelles'>
            <div className="flex gap-6 items-start justify-between">
              <div className="size-20 rounded-full relative">
                <Image
                  src="/avatar_def1.webp"
                  width={200}
                  height={200}
                  alt={"artistName"}
                  className="size-full rounded-xl object-cover"
                />
                <ChangeProfilePicture btnClass="size-9" />
              </div>
              <EditpProfileSettings />
            </div>
            <div className="flex flex-col pt-6">
              <h1 className="text-lg font-semibold text-fg-title">John Doe</h1>
              <p className="text-sm text-fg-muted">Danseur chorégraphe</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4 text-fg">
              <div className="flex items-center gap-2 text-sm">
                <Calendar strokeWidth={1.2} className="size-4" />
                <span>01/04/1995</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <MapPin strokeWidth={1.2} className="size-4" />
                <span>Berlin</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Globe strokeWidth={1.2} className="size-4" />
                <span>Disponible uniquement dans ma ville</span>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <Mars strokeWidth={1.2} className="size-4" />
                <span>Homme</span>
              </div>          <div className="flex items-center gap-2 text-sm">
                <Mail strokeWidth={1.2} className="size-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone strokeWidth={1.2} className="size-4" />
                <span>+243 818078456</span>
              </div>
            </div>
            <p className="mt-4 text-fg-muted text-sm lg:text-base">
              Danseur et chorégraphe passionné avec une expérience significative dans les arts du spectacle. Spécialisé dans la danse contemporaine et moderne, je cherche à créer des performances uniques et émotionnelles. Mon approche artistique combine technique classique et innovation créative.
            </p>
          </BlockBlockWrapper>

          <BlockBlockWrapper title="Préférences Professionnelles" description="Définissez vos critères de recherche d'emploi et conditions de travail souhaitées">
            <Card.Header className="pb-2">
              <div className="flex justify-between items-center">
                <div>
                  <Card.Title className="text-lg font-semibold">Opportunités recherchées</Card.Title>
                </div>
                <ProfileJobPreference />
              </div>
            </Card.Header>
            <Card.Content className="pt-2">
              <p className="text-sm text-fg-muted mb-4">Voici en quoi vos recherches sur le job que vous recherchez.</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-xs mb-1">Intitulé du poste</p>
                  <p className="text-sm">Danseur chorégraphe</p>
                </div>

                <div>
                  <p className="text-xs mb-1">Localisation</p>
                  <div className="flex items-center gap-1">
                    <MapPin strokeWidth={1.2} className="h-3 w-3" />
                    <p className="text-sm">Berlin</p>
                  </div>
                </div>
                <div>
                  <p className="text-xs mb-1">Localisation</p>
                  <div className="flex items-center gap-1">
                    <Globe strokeWidth={1.2} className="h-3 w-3" />
                    <p className="text-sm">Disponible partout au pays</p>
                  </div>
                </div>

                <div>
                  <p className="text-xs mb-1">Type de contrat</p>
                  <div className="flex gap-2">
                    <Badge className="text-xs rounded-sm">
                      Freelance
                    </Badge>
                    <Badge className="text-xs rounded-sm">
                      CDD / Temporaire
                    </Badge>
                    <Badge className="text-xs rounded-sm">
                      CDI
                    </Badge>
                  </div>
                </div>

                <div>
                  <p className="text-xs mb-1">Niveau d'expérience</p>
                  <div className="flex gap-2">
                    <Badge className="text-xs rounded-sm">
                      1-3 ans
                    </Badge>
                    <Badge className="text-xs rounded-sm">
                      3-5 ans
                    </Badge>
                  </div>
                </div>

                <div className="col-span-2">
                  <p className="text-xs mb-1">Contexte / Élément sur ce que vous cherchez</p>
                  <p className="text-sm text-gray-400">Non renseigné</p>
                </div>
              </div>
            </Card.Content>
          </BlockBlockWrapper>
          <BlockBlockWrapper title="Parcours Professionnel" description="Détaillez vos expériences et réalisations artistiques">
            <Card.Header className="pb-2">
              <div className="flex justify-between items-center">
                <div>
                  <Card.Title className="text-lg font-semibold">Expériences</Card.Title>
                </div>
                <div className="flex gap-2">
                  <NewProfileExperience />
                </div>
              </div>
            </Card.Header>
            <Card.Content className="pt-2">
              <p className="text-sm text-fg-muted mb-6">
                Partagez-nous vos expériences passées et actuelles, de vos projets.
              </p>
              <div className="flex flex-col divide-y divide-border *:py-6 *:last:pb-0 *:first:pt-0">
                <div className="flex flex-col">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <p className="text-xs mb-1">Intitulé du poste</p>
                      <p className="text-sm font-semibold">Danseur Principal & Chorégraphe</p>
                    </div>
                    <div className="flex gap-2">
                      <Button intent="outline" size="small" className="h-9">
                        Modifier
                      </Button>
                      <Button intent="danger" size="extra-small" className="size-9">
                        <Trash strokeWidth={1.2} className='size-4' />
                      </Button>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs mb-1">L'entreprise ou client</p>
                      <div className="flex items-center gap-2">
                        <div className="w-5 h-5 bg-gray-200 dark:bg-gray-800 rounded flex items-center justify-center text-[10px]">L</div>
                        <p className="text-sm sm:text-base text-fg">Lumi, OCS, Lubumbashi</p>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-fg-muted mb-1">Localisation</p>
                      <p className="text-sm sm:text-base text-fg">Non renseigné</p>
                    </div>

                    <div>
                      <p className="text-sm text-fg-muted mb-1">Type de contrat</p>
                      <p className="text-sm sm:text-base text-fg">Non renseigné</p>
                    </div>

                    <div>
                      <p className="text-sm text-fg-muted mb-1">Periode</p>
                      <p className="text-sm sm:text-base text-fg">De août 2021 à janvier 2023</p>
                    </div>

                    <div className="col-span-full">
                      <p className="text-sm text-fg-muted mb-1">Description, missions</p>
                      <p className="text-sm sm:text-base text-balanc text-fg">
                        Direction artistique et création de performances de danse contemporaine. Collaboration avec divers artistes et compagnies pour développer des spectacles innovants. Formation et encadrement de danseurs, participation à des festivals internationaux de danse.
                      </p>
                    </div>
                    <div className="col-span-full">
                      <p className="text-xs mb-1">Compétences</p>
                      <p className="text-sm">Non renseigné</p>
                    </div>
                  </div>
                </div>
              </div>
            </Card.Content>
          </BlockBlockWrapper>
          <BlockBlockWrapper title="Expertise Artistique" description="Mettez en avant vos talents et compétences distinctives">
            <Card.Header className="pb-2">
              <div className="flex justify-between items-center">
                <div>
                  <Card.Title className="text-lg font-semibold">Compétences & expertises</Card.Title>
                </div>
                <EditProfileSkill />
              </div>
            </Card.Header>
            <CardContent className="pt-2">
              <p className="text-sm text-fg-muted mb-6">
                Listez les savoir-faire, compétences et expertises qui vous différencient.
              </p>
              <h3 className="text-sm font-semibold uppercase mb-3">COMPÉTENCES</h3>
              <div className="flex flex-wrap gap-1.5">
                <div className="text-sm flex items-center bg-fg-title text-bg rounded-lg px-2 py-0.5">
                  Danse contemporaine
                </div>
                <div className="text-sm flex items-center bg-fg-title text-bg rounded-lg px-2 py-0.5">
                  Chorégraphie
                </div>
                <div className="text-sm flex items-center bg-fg-title text-bg rounded-lg px-2 py-0.5">
                  Improvisation
                </div>
                <div className="text-sm flex items-center bg-fg-title text-bg rounded-lg px-2 py-0.5">
                  Performance scénique
                </div>
              </div>
            </CardContent>
          </BlockBlockWrapper>
        </ContainerWrapper>
      </ArtUserWrapper>
      <OrgUserWrapper noAccess={<></>}>
        <ContainerWrapper className="pt-8 space-y-10">
          <></>
        </ContainerWrapper>
      </OrgUserWrapper>
    </>
  )
}
