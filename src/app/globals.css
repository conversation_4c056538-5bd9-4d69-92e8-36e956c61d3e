/*
* Uncomment the following line to use the Inter font when not working on a Next.js project.
* @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
* Then replace var(--font-inter) to Inter
*/

@import "tailwindcss";
@import "tw-animate-css";
/*
---break---
*/
@custom-variant dark (&:is(.dark *));
@plugin 'tailwindcss-react-aria-components';

@theme {
  --font-sans: var(--font-inter-sans), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
  "Segoe UI Symbol", "Noto Color Emoji";
  --font-mono: "Geist Mono", 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', '"Liberation Mono"', '"Courier New"', 'monospace';
  --font-title: var(--font-druk-cond);

  --color-border: var(--border);
  --color-input: var(--input);

  --color-ring: var(--ring);

  --color-bg: var(--bg);
  --color-bg-surface: var(--bg-surface);
  --color-bg-light:var(--bg-light);
  --color-bg-surface-muted: var(--bg-surface-muted);
  --color-bg-elevated: var(--bg-elevated);

  --color-bg-success-soft: var(--bg-success-soft);
  --color-bg-warning-soft: var(--bg-warning-soft);
  --color-bg-gray-soft: var(--bg-gray-soft);

  --color-fg: var(--fg);
  --color-fg-title:var(--fg-title);
  --color-fg-muted: var(--fg-muted);
  --color-fg-primary-btn: var(--fg-primary-btn);
  --color-fg-primary:var(--fg-primary);

  --color-primary: var(--primary);
  --color-primary-fg: var(--primary-fg);

  --color-secondary: var(--secondary);
  --color-secondary-fg: var(--secondary-fg);

  --color-accent: var(--accent);
  --color-accent-fg: var(--accent-fg);

  --color-success: var(--success);
  --color-success-fg: var(--success-fg);

  --color-danger: var(--danger);
  --color-danger-fg: var(--danger-fg);

  --color-warning: var(--warning);
  --color-warning-fg: var(--warning-fg);

  --color-muted: var(--muted);
  --color-muted-fg: var(--muted-fg);

  --color-overlay: var(--overlay);
  --color-overlay-fg: var(--overlay-fg);

  --color-navbar: var(--navbar);
  --color-navbar-fg: var(--navbar-fg);

  --color-sidebar: var(--sidebar);
  --color-sidebar-fg: var(--sidebar-fg);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --color-primary-50: hsl(54, 100%, 94%);
  --color-primary-100: hsl(60, 100%, 88%);
  --color-primary-200: hsl(58, 100%, 76%);
  --color-primary-300: hsl(54, 100%, 61%);
  --color-primary-400: hsl(52, 100%, 51%);
  --color-primary-500: hsl(51, 100%, 50%);
  --color-primary-600: hsl(45, 100%, 41%);
  --color-primary-700: hsl(40, 98%, 33%);
  --color-primary-800: hsl(39, 84%, 29%);
  --color-primary-900: hsl(36, 75%, 26%);
  --color-primary-950: hsl(33, 85%, 14%);


  --color-gray-50: oklch(0.985 0 0);
  --color-gray-100: oklch(0.97 0 0);
  --color-gray-200: oklch(0.922 0 0);
  --color-gray-300: oklch(0.87 0 0);
  --color-gray-400: oklch(0.708 0 0);
  --color-gray-500: oklch(0.556 0 0);
  --color-gray-600: oklch(0.439 0 0);
  --color-gray-700: oklch(0.371 0 0);
  --color-gray-800: oklch(0.269 0 0);
  --color-gray-900: oklch(0.205 0 0);
  --color-gray-950: oklch(0.145 0 0);
}

@layer base {
  :root {
    --bg: var(--color-white);
    --bg-surface: var(--color-gray-200);
    --bg-light: var(--color-gray-50);
    --bg-surface-muted: var(--color-gray-200);
    --bg-success-soft: var(--color-emerald-50);
    --bg-warning-soft: var(--color-amber-50);
    --bg-gray-soft: var(--color-gray-50);
    --bg-elevated: var(--color-gray-200);


    --fg: var(--color-gray-950);
    --fg-title: var(--color-gray-900);
    --fg-muted: var(--color-gray-600);
    --fg-primary-btn: var(--color-primary-950);
    --fg-primary:var(--color-primary-700);

    --primary: var(--color-primary-500);
    --primary-fg: var(--color-white);

    --secondary: var(--color-gray-100);
    --secondary-fg: var(--color-gray-950);

    --overlay: var(--color-white);
    --overlay-fg: var(--color-gray-950);

    --accent: var(--color-primary-600);
    --accent-fg: var(--color-white);

    --muted: var(--color-gray-100);
    --muted-fg: var(--color-gray-600);

    --success: var(--color-emerald-600);
    --success-fg: var(--color-white);

    --warning: var(--color-amber-400);
    --warning-fg: var(--color-amber-950);

    --danger: var(--color-red-600);
    --danger-fg: var(--color-red-50);

    --border: var(--color-gray-200);
    --input: var(--color-gray-300);
    --ring: var(--color-primary-600);

    --navbar: var(--color-gray-50);
    --navbar-fg: var(--color-gray-950);

    --sidebar: var(--color-gray-50);
    --sidebar-fg: var(--color-gray-950);

    --chart-1: var(--color-primary-600);
    --chart-2: var(--color-primary-400);
    --chart-3: var(--color-primary-300);
    --chart-4: var(--color-primary-200);
    --chart-5: var(--color-primary-100);

    --radius-lg: 0.5rem;
    --radius-xs: calc(var(--radius-lg) * 0.5);
    --radius-sm: calc(var(--radius-lg) * 0.75);
    --radius-md: calc(var(--radius-lg) * 0.9);
    --radius-xl: calc(var(--radius-lg) * 1.25);
    --radius-2xl: calc(var(--radius-lg) * 1.5);
    --radius-3xl: calc(var(--radius-lg) * 2);
    --radius-4xl: calc(var(--radius-lg) * 3);
  }

  .dark {
    --bg: var(--color-gray-950);
    --bg-surface: var(--color-gray-900);
    --bg-light: var(--color-gray-950);
    --bg-surface-muted: var(--color-gray-900);
    --bg-elevated: var(--color-gray-800);

    --bg-success-soft: --alpha(var(--color-emerald-950) / 40%);
    --bg-warning-soft: --alpha(var(--color-amber-950) / 40%);
    --bg-gray-soft: --alpha(var(--color-gray-950) / 40%);

    --fg: var(--color-gray-300);
    --fg-title: var(--color-white);
    --fg-muted: var(--color-gray-400);
    --fg-primary-btn: var(--color-primary-950);
    --fg-primary:var(--color-primary-500);

    --primary: var(--color-primary-500);
    --primary-fg: var(--color-white);

    --secondary: oklch(0.239 0 0);
    --secondary-fg: var(--color-gray-50);

    --accent: var(--color-primary-600);
    --accent-fg: var(--color-white);

    --muted: var(--color-gray-900);
    --muted-fg: var(--color-gray-400);

    --overlay: oklch(0.165 0 0);
    --overlay-fg: var(--color-gray-50);

    --success: var(--color-emerald-600);
    --success-fg: var(--color-white);

    --warning: var(--color-amber-400);
    --warning-fg: var(--color-amber-950);

    --danger: var(--color-red-600);
    --danger-fg: var(--color-red-50);

    --border: oklch(0.271 0 0);
    --input: oklch(0.291 0 0);
    --ring: var(--color-primary-600);

    --navbar: oklch(0.165 0 0);
    --navbar-fg: var(--color-gray-50);

    --sidebar: oklch(0.155 0 0);
    --sidebar-fg: var(--color-gray-50);

    --chart-1: var(--color-primary-700);
    --chart-2: var(--color-primary-500);
    --chart-3: var(--color-primary-400);
    --chart-4: var(--color-primary-300);
    --chart-5: var(--color-primary-200);
  }
}

@variant dark (&:is(.dark *));

@utility app-container {
  @apply xl:max-w-[86rem] mx-auto w-full;
}

.scrollbox, .scroll-hidden{
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.scrollbox{
  scroll-padding-left: 10px;
  scroll-padding-right: 10px;
}
.scrollbox::-webkit-scrollbar, .scroll-hidden:-webkit-scrollbar {
  display: none;
}


@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--border, currentColor);
  }

  * {
    text-rendering: optimizeLegibility;
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
  }

  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: normal;
    scroll-behavior: smooth;
    height: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-tap-highlight-color: transparent;
  }

  body {
    background-color: var(--bg);
    color: var(--fg);
    @apply font-sans;
  }

  ::-webkit-scrollbar {
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 4px;
  }




  .lightbox-overlay {
    @apply fixed inset-0 bg-gray-900/80 z-50 flex items-center justify-center touch-pan-y;
  }

  .lightbox-container {
    @apply relative w-full h-full md:w-4/5 md:h-4/5 flex items-center justify-center cursor-grab active:cursor-grabbing;
  }

  .lightbox-navigation {
    @apply absolute top-1/2 -translate-y-1/2 text-white p-2 md:p-4 opacity-70 hover:opacity-100 transition-opacity z-10;
  }

  .zoom-control {
    @apply bg-gray-900/50 text-white p-2 rounded-full hover:bg-gray-900/70 transition-colors;
  }
}

@utility auth-wrapper{
  @apply grid min-h-screen;
  @apply lg:[grid-template-columns:minmax(0,608fr)_minmax(0,832fr)];
  @apply xl:[grid-template-columns:608px_minmax(0,1fr)];
  @apply [@media(min-width:1440px)]:[grid-template-columns:minmax(0,608fr)_minmax(0,832fr)];
}
