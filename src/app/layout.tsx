
import "./globals.css";
import { Providers } from "@/components/providers";

import type { Metadata } from "next";
import { drukCondFont, geistMono, geistSans } from "./fonts";
import { AppGlobalQueryProvider } from "@/providers/query-client";
export const metadata: Metadata = {
  title: "Leap",
  description: "Bienvenue sur cette plateforme qui permet la rencontre entre les organisme et les artistes. Découvrez, collaborer et brillez",
  icons: {
    icon: "/fav-icon.png",
    apple: "/fav-icon.png"
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <AppGlobalQueryProvider>
      <html lang="en" className="scroll-smooth" suppressHydrationWarning>
        <body
          className={`${geistSans.variable} ${geistMono.variable} ${drukCondFont.variable} antialiased bg-bg-light min-h-dvh overflow-hidden overflow-y-auto`}
        >
          <Providers>
            {children}
            <div id="portal-root" />
          </Providers>
        </body>
      </html>
    </AppGlobalQueryProvider>
  );
}
