"use client"

import { RecurrenceRule } from '@/types/app-calendar';
import { useState } from 'react';
import { Button } from '../ui/button';
import { Modal } from '../ui/modal';
import { Form } from '../ui/form';
import { Select } from '../ui/select';
import { TextField } from '../ui/text-field';
import { Checkbox } from '../ui/checkbox';



interface AvailabilityFormProps {
  artistId: string;
  selectedDate: Date;
  isOpen: boolean,
  onClose: () => void;
}


const addAvailabilitySlot = (artistId: string | number,
  start: Date,
  end: Date,
  status: string,
  recurrenceRule?: RecurrenceRule) => {
  console.log('Adding availability slot:', {
    artistId,
    start,
    end,
    status,
    recurrenceRule
  });
}

const AvailabilityForm = ({
  artistId,
  selectedDate,
  onClose,
  isOpen
}: AvailabilityFormProps) => {


  const [status, setStatus] = useState<'available' | 'unavailable'>('available');
  const [startTime, setStartTime] = useState('09:00');
  const [endTime, setEndTime] = useState('17:00');
  const [isRecurring, setIsRecurring] = useState(false);
  const [recurrenceType, setRecurrenceType] = useState<'daily' | 'weekly' | 'monthly'>('weekly');
  const [selectedDays, setSelectedDays] = useState<number[]>([selectedDate.getDay()]);
  const [endDate, setEndDate] = useState<string>(
    new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 3, selectedDate.getDate())
      .toISOString().split('T')[0]
  );


  const formattedDate = selectedDate.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();


    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);

    const start = new Date(selectedDate);
    start.setHours(startHour, startMinute, 0, 0);

    const end = new Date(selectedDate);
    end.setHours(endHour, endMinute, 0, 0);

    let recurrenceRule: RecurrenceRule | undefined;

    if (isRecurring) {
      recurrenceRule = {
        frequency: recurrenceType,
        interval: 1,
        daysOfWeek: recurrenceType === 'weekly' ? selectedDays : undefined,
        endDate: new Date(endDate)
      };
    }

    addAvailabilitySlot(
      artistId,
      start,
      end,
      status,
      recurrenceRule
    );

    onClose();
  };

  const toggleDay = (day: number) => {
    if (selectedDays.includes(day)) {
      setSelectedDays(selectedDays.filter(d => d !== day));
    } else {
      setSelectedDays([...selectedDays, day]);
    }
  };

  return (
    <Modal.Content isOpen={isOpen} onOpenChange={() => onClose()}>
      <Modal.Header>
        <Modal.Title className="md:text-lg">
          Définir la disponibilité pour {formattedDate}
        </Modal.Title>
      </Modal.Header>
      <Form className="space-y-4">
        <Modal.Body>
          <div className="space-y-4">
            <Select
              className='col-span-full'
              label="Status"
              placeholder="Sélectionnez un status"
              onSelectionChange={e => setStatus(e?.toString() as 'available' | 'unavailable')}
            >
              <Select.Trigger />
              <Select.List>
                <Select.Option id="available" textValue="Disponible">
                  Disponible
                </Select.Option>
                <Select.Option id="unavailable" textValue="Indisponible">
                  Indisponible
                </Select.Option>
              </Select.List>
            </Select>

            <div className="grid grid-cols-2 gap-4">
              <TextField
                type="time"
                label="Start Time"
                value={startTime}
                onChange={e => setStartTime(e)}
              />
              <TextField
                type="time"
                label="End Time"
                value={endTime}
                onChange={e => setEndTime(e)}
              />
            </div>

            <Checkbox label="Faire de cette disponibilité une récurrence" onChange={() => setIsRecurring(!isRecurring)} />

            {isRecurring && (
              <div className="space-y-4 p-4 bg-muted rounded-md">
                <Select
                  className='col-span-full'
                  label="Type de récurrence"
                  placeholder="Sélectionner le type de récurrence"
                  onSelectionChange={e => setRecurrenceType(e?.toString() as 'daily' | 'weekly' | 'monthly')}
                >
                  <Select.Trigger />
                  <Select.List>
                    <Select.Option id="daily" textValue="Quotidien">
                      Quotidien
                    </Select.Option>
                    <Select.Option id="weekly" textValue="Hebdomadaire">
                      Hebdomadaire
                    </Select.Option>
                    <Select.Option id="monthly" textValue="Mensuel">
                      Mensuel
                    </Select.Option>
                  </Select.List>
                </Select>

                {recurrenceType === 'weekly' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Répéter le
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'].map((day, index) => (
                        <button
                          key={day}
                          type="button"
                          className={`
                            h-8 w-8 rounded-full text-sm font-medium
                            ${selectedDays.includes(index)
                              ? 'bg-indigo-600 text-white'
                              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }
                          `}
                          onClick={() => toggleDay(index)}
                        >
                          {day.charAt(0)}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                <TextField
                  type="date"
                  label="Date de fin"
                  value={endDate}
                  onChange={e => setEndDate(e)}
                />
              </div>
            )}
          </div>
        </Modal.Body>

        <Modal.Footer className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            intent="outline"
            onClick={onClose}
          >
            Annuler
          </Button>
          <Button
            type="submit"
            intent={status === 'available' ? 'success' : 'danger'}
            onClick={handleSubmit}
          >
            Enreigistrer {status === 'available' ? 'La disponibilité' : "L'indisponibilité"}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal.Content>
  );
};

export default AvailabilityForm;