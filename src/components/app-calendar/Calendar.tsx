"use client"

import { useState } from 'react';
import { format, addMonths, subMonths } from 'date-fns';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import CalendarHeader from './CalendarHeader';
import { CalendarGrid } from './CalendarGrid';
import { cn } from '@/lib/utils';
import { Button } from '../ui/button';
import { getMonthName } from '@/utils/calendarUtils';



interface CalendarProps {
  artistId?: string;
  className?: string;
  onDateSelect?: (date: Date, status: string | null) => void;
  isReadOnly?: boolean;
  availabilityData: {
    date: string;
    status: string;
    id: string;
    isRecurrent?: boolean;
    recurrence?: {
      frequency: 'daily' | 'weekly' | 'monthly';
      daysOfWeek?: number[];
      endDate?: string;
    };
  }[];
  gridCellClass?:string
}

export const AppCalendar = ({
  className = '',
  onDateSelect,
  isReadOnly = false,
  availabilityData,
  gridCellClass
}: CalendarProps) => {
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const convertedAvailabilityData = availabilityData;
  const handlePrevious = () => {
    setCurrentDate(subMonths(currentDate, 1));
  };
  const handleNext = () => {
    setCurrentDate(addMonths(currentDate, 1));
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  const getHeaderText = () => {
    const day = getMonthName(currentDate.getMonth())
    if (day) {
      return `${day} ${currentDate.getFullYear()}`;
    }
    return format(currentDate, 'MMMM yyyy');
  };

  return (
    <div className={cn("overflow-hidden p-4 rounded-xl bg-bg border border-border/70", className)}>
      <div className="pb-1.5 flex justify-between items-center gap-4">
        <h2 className="text-xl font-semibold mr-4 capitalize">{getHeaderText()}</h2>
        <div className="flex space-x-0.5 p-0.5 border border-border/90 rounded-xl">
          <Button
            intent="plain"
            size="square-petite"
            onClick={handlePrevious}
            aria-label="Previous"
          >
            <ChevronLeft size={16} />
          </Button>
          <Button
            intent="plain"
            size="square-petite"
            onClick={handleNext}
            aria-label="Next"
          >
            <ChevronRight size={16} />
          </Button>
        </div>
      </div>
      <div className="calendar-container">
        <CalendarHeader />
        <CalendarGrid
          availabilityData={convertedAvailabilityData}
          currentDate={currentDate}
          onDateSelect={onDateSelect}
          isReadOnly={isReadOnly}
          gridCellClass={gridCellClass}
        />
      </div>
    </div>
  );
};
