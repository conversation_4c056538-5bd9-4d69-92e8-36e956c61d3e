"use client"
import { format, isSameMonth, isToday, startOfMonth, startOfWeek, addDays, endOfWeek, endOfMonth, isBefore } from 'date-fns';


interface AvailabilityData {
  date: string;
  status: string;
  id: string;
  isRecurrent?: boolean;
  recurrence?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    daysOfWeek?: number[];
    endDate?: string;
  };
}

interface CalendarGridProps {
  availabilityData: AvailabilityData[];

  currentDate: Date;
  onDateSelect?: (date: Date, status: string | null) => void;
  isReadOnly?: boolean;
  gridCellClass?:string
}

export const CalendarGrid = ({
  availabilityData,
  currentDate,
  onDateSelect,
  isReadOnly = false,
  gridCellClass
}: CalendarGridProps) => {
  const getAvailabilityStatus = (date: Date): string | null => {
    const dateString = format(date, 'yyyy-MM-dd');

    // Check for direct date match
    const directMatch = availabilityData.find(item => item.date === dateString);
    if (directMatch) return directMatch.status;

    // Check for recurring availability
    const recurringMatch = availabilityData.find(item => {
      if (!item.isRecurrent || !item.recurrence) return false;

      const itemDate = new Date(item.date);
      const endDate = item.recurrence.endDate ? new Date(item.recurrence.endDate) : null;

      if (endDate && date > endDate) return false;

      switch (item.recurrence.frequency) {
        case 'daily':
          return true;
        case 'weekly':
          return item.recurrence.daysOfWeek?.includes(date.getDay()) || false;
        case 'monthly':
          return itemDate.getDate() === date.getDate();
        default:
          return false;
      }
    });

    return recurringMatch?.status || null;
  };

  const getStatusDot = (status: string | null) => {
    if (!status) return null;

    const colorMap: Record<string, string> = {
      available: 'bg-emerald-500',
      unavailable: 'bg-red-500',
      booked: 'bg-amber-500',
    };

    return (
      <div className={`size-1.5 rounded-full ${colorMap[status] || colorMap.available}`} />
    );
  };

  const renderMonthView = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(monthStart);
    const startDate = startOfWeek(monthStart);
    const endDate = endOfWeek(monthEnd);

    const rows = [];
    let days = [];
    let day = startDate;

    while (day <= endDate) {
      for (let i = 0; i < 7; i++) {
        const cloneDay = new Date(day);
        const status = getAvailabilityStatus(cloneDay);
        const isCurrentMonth = isSameMonth(day, monthStart);
        const isPastDate = isBefore(day, new Date().setHours(0, 0, 0, 0));

        days.push(
          <div
            key={day.toString()}
            className={`
              ${gridCellClass ? gridCellClass :"sm:min-h-[100px] p-2"} border border-border/70 relative rounded-xl
              ${!isCurrentMonth ? 'bg-bg-surface/30' : ''}
              ${isToday(day) ? 'bg-primary-50 dark:bg-primary-950/40 text-fg-primary' : ''}
              ${!isReadOnly && !isPastDate && isCurrentMonth ? 'cursor-pointer hover:bg-bg-surface' : ''}
              ${isPastDate && isCurrentMonth ? 'bg-bg' : ''}
            `}
            onClick={() => {
              if (!isReadOnly && !isPastDate && isCurrentMonth && onDateSelect) {
                onDateSelect(cloneDay, status);
              }
            }}
          >
            <div className={`
              h-6 w-6 flex items-center justify-center rounded-full 
              ${isToday(day) ? '' : ''}
            `}>
              {format(day, 'd')}
            </div>
            {status && (
              <div className="absolute bottom-1 right-1">
                {getStatusDot(status)}
              </div>
            )}
          </div>
        );
        day = addDays(day, 1);
      }
      rows.push(
        <div key={day.toString()} className="grid grid-cols-7 gap-0.5">
          {days}
        </div>
      );
      days = [];
    }

    return (
      <div className="space-y-0.5 mt-2">
        {rows}
        <div className="flex justify-end gap-4 mb-4 text-sm pt-5">
          <div className="flex items-center gap-2">
            <div className="h-1 w-1 rounded-full bg-emerald-500" />
            <span>Disponible</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-1 w-1 rounded-full bg-red-500" />
            <span>Indisponible</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-1 w-1 rounded-full bg-amber-500" />
            <span>Réservé</span>
          </div>
        </div>
      </div>
    );
  };

  return renderMonthView();
};
