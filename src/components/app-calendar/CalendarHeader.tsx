"use client"

const days = ['Dim', 'Lun', '<PERSON>', '<PERSON><PERSON>', 'Je<PERSON>', 'V<PERSON>', '<PERSON>'];
const CalendarHeader = () => {

    
    return (
      <div className="grid grid-cols-7 text-center border bg-bg-surface/80 rounded-xl">
        {days.map((day, index) => (
          <div 
            key={day} 
            className={`py-2 font-medium text-sm
              ${index === 0 || index === 6 ? 'text-fg-primary' : 'text-fg-muted'}`}
          >
            {day}
          </div>
        ))}
      </div>
    );
};

export default CalendarHeader;