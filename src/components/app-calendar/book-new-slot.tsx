"use client"

import { useState } from 'react';
import { Modal } from "../ui/modal";
import { Form } from '../ui/form';
import { TextField } from '../ui/text-field';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';

export const BookNewSlot = ({ isOpen, onClose }: { isOpen: boolean, onClose: () => void }) => {
    const [startTime, setStartTime] = useState('09:00');
    const [endTime, setEndTime] = useState('17:00');
    const [date, setDate] = useState('');
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Handle form submission
        console.log({
            date,
            title,
            description,
            startTime,
            endTime
        });
        onClose();
    };

    return (
        <Modal.Content isOpen={isOpen} onOpenChange={() => onClose()}>
            <Modal.Header>
                <Modal.Title>
                    Nouvelle réservation
                </Modal.Title>
            </Modal.Header>
            <Form className="space-y-4">
                <Modal.Body>
                    <div className="space-y-4">
                        <TextField
                            type="date"
                            label="Date Reservation"
                            onChange={e => setDate(e)}
                        />

                        <TextField
                            label="Intitulé reservation"
                            onChange={e => setTitle(e)}
                        />

                        <Textarea
                            label="Description"
                            placeholder="Décrivez les détails de votre réservation..."
                            className="resize-none"
                            onChange={e => setDescription(e)}
                        />

                        <div className="grid grid-cols-2 gap-4">
                            <TextField
                                type="time"
                                label="Heure debut"
                                value={startTime}
                                onChange={e => setStartTime(e)}
                            />
                            <TextField
                                type="time"
                                label="Heure Fin"
                                value={endTime}
                                onChange={e => setEndTime(e)}
                            />
                        </div>
                    </div>
                </Modal.Body>

                <Modal.Footer className="flex justify-end space-x-3 pt-4">
                    <Button
                        type="button"
                        intent="outline"
                        onClick={onClose}
                    >
                        Annuler
                    </Button>
                    <Button
                        type="submit"
                        intent="success"
                        onClick={handleSubmit}
                    >
                        Réserver
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal.Content>
    );
};
