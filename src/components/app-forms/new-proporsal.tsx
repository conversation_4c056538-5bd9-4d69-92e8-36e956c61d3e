import React from 'react'
import { Modal } from '../ui/modal'
import { MessageSquarePlus } from 'lucide-react'
import { Form } from '../ui/form'
import { TextField } from '../ui/text-field'
import { Select } from '../ui/select'
import { Textarea } from '../ui/textarea'
import { cities } from '@/data/cities'
import { Button } from '../ui/button'
import { Checkbox } from '../ui/checkbox'

export const NewProporsalModal = ({ isOpen, onClose, annexToChat }: { isOpen: boolean, onClose: () => void, annexToChat?: boolean }) => {
    return (
        <>
            <Modal.Content isOpen={isOpen} onOpenChange={() => onClose()} size="2xl" classNames={{ content: "w-full", overlay: "backdrop-blur-lg" }}>
                <Modal.Header className="bg-bg-surface/40">
                    <div className="flex items-center gap-4">
                        <span className="size-12 flex items-center justify-center bg-bg-surface border border-border rounded-xl">
                            <MessageSquarePlus strokeWidth={1.2} className="size-6" />
                        </span>
                        <div className="flex-1">
                            <Modal.Title className="text-xl font-semibold">
                                John Doe
                            </Modal.Title>
                            <Modal.Description className="text-fg-muted truncate">
                                Reponse au bout de 2heures
                            </Modal.Description>
                        </div>
                    </div>
                </Modal.Header>
                <Form>
                    <Modal.Body className="py-4 gap-4 grid grid-cols-2">
                        <div className="col-span-2">
                            <Checkbox isSelected={annexToChat} label="Annexé au chat" />
                        </div>
                        <TextField className='col-span-full' label="What is the mission about?" />
                        <Textarea className='col-span-full' label="Contenu" id="Description of the task/project" />
                        <Select className='col-span-full sm:col-span-1' label="Localisation" placeholder="Selectionner une ville">
                            <Select.Trigger />
                            <Select.List items={cities}>
                                {(item) => (
                                    <Select.Option id={item.id} textValue={item.name}>
                                        {item.name}
                                    </Select.Option>
                                )}
                            </Select.List>
                        </Select>
                        <Select className='col-span-full sm:col-span-1' label="Type de contrat" placeholder="Selectionner un type">
                            <Select.Trigger />
                            <Select.List items={[{ id: '1', name: 'CDI' }, { id: '2', name: 'CDD' }, { id: '3', name: 'Freelance' }]}>
                                {(item) => (
                                    <Select.Option id={item.id} textValue={item.name}>
                                        {item.name}
                                    </Select.Option>
                                )}
                            </Select.List>
                        </Select>
                        <TextField label="Durée (en jours)" />
                        <TextField label="Budget proposé" />
                        <Textarea className='col-span-full' label="Message additionnel" />
                    </Modal.Body>
                    <Modal.Footer className="flex justify-end pt-3 border-t border-border">
                        <Button>
                            Soumettre
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal.Content>
        </>
    )
}
