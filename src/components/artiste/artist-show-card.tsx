import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { GetStartedChat } from "@/components/artiste/get-started-chat";
import Link from "next/link";
import { ArtisteProfile } from "@/types/artiste-profile";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { genres } from "@/data/genres";



export function ArtistCard(props: ArtisteProfile & { className?: string }) {
    return (
        <>
            <article className={`${cn("rounded-xl p-1 bg-bg border border-border/70 dark:bg-muted relative", props.className)}`}>
                <div className="relative aspect-[3/4] rounded-[8px] overflow-hidden">
                    <Image
                        src={props.avatar}
                        alt={`${props.username} profile`}
                        width={1300}
                        height={950}
                        className="size-full object-cover"
                    />
                    <div className="absolute inset-x-0 bottom-0 p-5 bg-gradient-to-t from-black/90 via-black/50 to-transparent backdrop-blur-xs backdrop-saturate-100">
                        <div>
                            <h2 className="flex items-center sm:text-xl font-bold text-white">
                                {props.about.firstName} {props.about.lastName}
                                {/* {isVerified && <Badge variant="dark" />} */}
                            </h2>
                            <Badge intent="none" className="bg-white text-gray-800 rounded-lg">
                                <Tag strokeWidth={1.2} className='size-4' />
                                {genres.find(genre => genre.slug === props.genre)?.name}
                            </Badge>
                        </div>

                        <div className="py-2">
                            <div className='text-sm text-gray-200 flex items-center gap-3.5'>
                                <span className="flex items-center">
                                    <Star strokeWidth={0} className="size-4 fill-amber-500 mr-0.5" />
                                    {props.rating}
                                </span>
                                <span className='flex items-center'>
                                    <BrickWallFire strokeWidth={1.2} className='size-4 mr-0.5' />
                                    {props.completedPrestations} Prestation{props.completedPrestations > 1 ? "s" : ""}
                                </span>
                            </div>
                        </div>

                        <div className="relative flex gap-2 pt-2">
                            <Button intent="none" size="square-petite" className={"bg-gray-100/60 rounded-lg justify-center items-center flex text-white backdrop-blur-sm saturate-50 relative z-[4]"}>
                                <Bookmark strokeWidth={1.2} stroke='currentColor' className='size-4' />
                            </Button>
                            <GetStartedChat btnText="Prendre contact" btnClass="flex-1 justify-center relative z-[4]" />
                        </div>
                    </div>
                </div>
                <Link href={`/${props.username}`} className="absolute inset-0" aria-label={`Affiche profile de : ${props.username}`}></Link>
            </article>
        </>
    );
}
