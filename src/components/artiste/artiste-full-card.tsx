
import Link from 'next/link'
import React from 'react'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { GetStartedChat } from '@/components/artiste/get-started-chat'
import { Bookmark, BrickWallFire, ImageIcon, MapPin, Star, Tag, Timer } from 'lucide-react'
import Image from 'next/image'
import { ArtisteProfile } from '@/types/artiste-profile'
import { Mediabox } from '../media-box/media-box'
import { genres } from '@/data/genres'

export const CardFullArtist = (artiste: ArtisteProfile) => {
    return (
        <article className="w-full bg-bg border border-border/70 p-5 sm:p-6 rounded-xl flex flex-col relative isolate z-[5] overflow-hidden">
            <div className="pointer-events-none flex items-start gap-5">
                <div className="size-12 sm:size-14 rounded-full bg-bg-surface">
                    <Image
                        src={artiste.avatar}
                        width={200}
                        height={200}
                        alt={artiste.username}
                        className="size-full object-cover rounded-full"
                    />
                </div>
                <div className="flex-1 flex flex-col">
                    <div className="flex items-center flex-wrap gap-x-5 gap-y-2">
                        <h2 className="font-semibold text-fg-title sm:text-xl">
                            {artiste.about.firstName} {artiste.about.lastName}
                        </h2>
                        <div className='text-sm text-fg-muted flex items-center gap-3.5'>
                            <span className="flex items-center">
                                <Star strokeWidth={0} className="size-4 fill-amber-500 mr-0.5" />
                                {artiste.rating}
                            </span>
                            <span className='flex items-center'>
                                <BrickWallFire strokeWidth={1.2} className='size-4 mr-0.5' />
                                {artiste.completedPrestations} Prestation{artiste.completedPrestations > 1 ? "s" : ""}
                            </span>
                        </div>
                    </div>
                    <div className="mt-1.4">
                        <span className="text-sm text-fg-muted">
                            {artiste.about.title}
                        </span>
                    </div>

                </div>
            </div>
            <Mediabox data={artiste.marketingMedia.slice(0, 4)} gridItemClass="w-[45%] sm:w-[38%] min-w-[45%] sm:min-w-[38%] md:min-w-0 md:w-auto aspect-[4/5] overflow-hidden rounded-md cursor-pointer" gridClass="relative mt-5 z-[4] w-full flex md:grid md:grid-cols-4 gap-1 sm:gap-2 overflow-x-auto md:overflow-x-hidden scroll-hidden">
                {
                    artiste.marketingMedia.length < 4 ?
                        Array(4 - artiste.marketingMedia.length).fill(0).map((_, index) => (
                            <div
                                key={`placeholder-${index}`}
                                className="relative flex items-center justify-center bg-bg-surface/30 border border-dashed w-[45%] sm:w-[38%] min-w-[45%] sm:min-w-[38%] md:min-w-0 md:w-auto aspect-[4/5] overflow-hidden rounded-md"
                            >
                                <span className='absolute w-3/5 aspect-square top-1/2 left-1/2 -translate-1/2 border border-dashed border-gray-400/60 rotate-12 flex' />
                                <ImageIcon strokeWidth={1.2} className="size-10 text-fg-muted/70" />
                            </div>
                        )) : null
                }
            </Mediabox>
            <div className="pointer-events-none mt-4 flex flex-col flex-1 justify-start">
                <div className="flex items-center gap-3 flex-wrap">
                    <span className='text-sm text-fg-muted flex items-center'>
                        <MapPin className='size-4 mr-0.5' strokeWidth={1.2} />
                        {artiste.about.city}
                    </span>

                    <span className='text-sm text-fg-muted flex items-center'>
                        <Timer className='size-4 mr-0.5' strokeWidth={1.2} />
                        {artiste.quickResponse ? 'Réponse rapide' : `${artiste.responseTime}`}
                    </span>
                    <Badge intent="none" className="bg-primary text-fg-primary-btn rounded-md w-max">
                        <Tag strokeWidth={1.2} className='size-4' />
                        {genres.find(genre => genre.slug === artiste.genre)?.name}
                    </Badge>
                </div>
                <div className="mt-3.5 flex flex-wrap items-start gap-1.5 mb-5">
                    {artiste.skills.slice(0, 6).map((competence, index) => (
                        <span key={index} className="h-max bg-bg-surface/70 border border-border/60 text-sm text-fg-muted px-2 py-0.5 rounded-lg">
                            {competence}
                        </span>
                    ))}
                    {artiste.skills.length > 6 && (
                        <span className="h-max bg-bg-surface/70 border border-border/60 text-sm text-fg-muted px-2 py-0.5 rounded-lg">
                            + {artiste.skills.length - 6}
                        </span>
                    )}
                </div>
            </div>
            <Link href={`/${artiste.username}`} aria-label={`Lien vers le profile de ${artiste.about.firstName}`} className="absolute inset-0"></Link>
            <div className="flex justify-end items-center mt-auto gap-2 pt-4 border-t border-border/80">
                <Button intent="outline" size="square-petite">
                    <Bookmark strokeWidth={1.2} stroke='currentColor' className='size-4' />
                </Button>
                <GetStartedChat btnText="Prendre contact" btnClass="w-max" />
            </div>
        </article>
    )
}
