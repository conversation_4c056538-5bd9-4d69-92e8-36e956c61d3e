"use client"

import { Camera, PictureInPicture } from "lucide-react"
import { Button } from "../ui/button"
import { Modal } from "../ui/modal"
import { Form } from "../ui/form"
import { FileTrigger } from "../ui/file-trigger"
import { DropZone } from "../ui/drop-zone"
import { Description } from "../ui/field"
import { useState } from "react"
import { DropEvent, isFileDropItem } from "react-aria"
import Image from "next/image"


export const ChangeProfilePicture = ({btnClass}:{btnClass?:string}) => {
    const [selectedImage, setSelectedImage] = useState<string | null>(null)

    const onDropHandler = async (e: DropEvent) => {
        const item = e.items
            .filter(isFileDropItem)
            .find((item) => item.type.includes("image"))
        if (item) {
            const file = await item.getFile()
            setSelectedImage(URL.createObjectURL(file))
            // setValue("cover", file)
        }
    }
    const onSelectHandler = async (e: FileList | null) => {
        if (e) {
            const files = Array.from([...e])
            const item = files[0]

            if (item) {
                setSelectedImage(URL.createObjectURL(item))
                // setValue("cover", item)
            }
        }
    }
    return (
        <Modal>
            <Button intent="outline" size="square-petite" className={`size-10 absolute -bottom-4 -right-4 bg-bg border-bg border-4 rounded-xl ${btnClass}`}>
                <Camera strokeWidth={1.2} className="size-4" />
            </Button>
            <Modal.Content>
                <Modal.Header className="bg-bg-surface/50">
                    <div className="flex items-center gap-4">
                        <span className="size-12 flex items-center justify-center bg-bg-surface border border-border rounded-xl">
                            <PictureInPicture strokeWidth={1.2} className="size-5" />
                        </span>
                        <div className="flex-1">
                            <Modal.Title className="text-xl font-semibold">
                                Photo de profile
                            </Modal.Title>
                            <Modal.Description className="text-fg-muted">
                                Changer la photo de profile
                            </Modal.Description>
                        </div>
                    </div>
                </Modal.Header>
                <Form>
                    <Modal.Body className="py-5">
                        <div className="grid grid-cols-2 gap-6">
                            <div className="w-full aspect-square border border-dashed p-0.5 rounded-xl">
                                {selectedImage ? <Image src={selectedImage} width={1000} height={1000} alt="image" className="w-full h-auto max-h-80 object-cover rounded-xl" /> : null}
                            </div>
                            <div className="flex flex-col gap-4">
                                <DropZone
                                    getDropOperation={(types) =>
                                        types.has("image/") ? "copy" : "cancel"
                                    }
                                    onDrop={onDropHandler}
                                    className={"sm:max-w-none"}
                                >
                                    <div className="grid space-y-3">
                                        <div className="justify-center flex">
                                            <FileTrigger
                                                acceptedFileTypes={["image/png", "image/jpeg", "image/webp"]}
                                                allowsMultiple={false}
                                                onSelect={onSelectHandler}
                                            >
                                                Upload un fichier
                                            </FileTrigger>
                                        </div>
                                        <Description>Ou Glisser-Deposser votre image up to 6MB</Description>
                                    </div>
                                </DropZone>
                                <div className="flex">
                                    <Button>
                                        Enregistrer
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </Modal.Body>
                </Form>
            </Modal.Content>
        </Modal>
    )
}
