"use client"
import { NewProporsalModal } from '@/components/app-forms/new-proporsal';
import { Button } from '@/components/ui/button';
import { MessageSquarePlus } from 'lucide-react';
import { useState } from 'react';


export const GetStartedChat = ({ btnClass, btnText = "Me contacter" }: { btnClass?: string, btnText?: string }) => {
    const [isOpen, setIsOpen] = useState(false)

    return (
        <>
            <Button onPress={() => setIsOpen(true)} size="small" className={btnClass}>
                <MessageSquarePlus strokeWidth={1.2} className="size-4" />
                {btnText}
            </Button>
            <NewProporsalModal annexToChat isOpen={isOpen} onClose={() => setIsOpen(false)} /> 
        </>
    )
}
