"use client"

import { buttonStyles } from "@/components/ui/button"
import { Menu } from "@/components/ui/menu"
import { IconBrandFacebook, IconBrandTwitter } from "@intentui/icons"
import { Copy, Link2, Share2 } from "lucide-react"

export const ShareOptionsProfile = () => {
    return (
        <>
            <Menu >
                <Menu.Trigger className={buttonStyles({ size: "square-petite", intent: "plain", className: "rounded-xl" })}>
                    <Share2 className="size-4" />
                </Menu.Trigger>
                <Menu.Content placement="bottom" className="w-dvw sm:w-auto">
                    <Menu.Item>
                        <Copy className="size-4 mr-2" strokeWidth={1.2} />
                        <Menu.Label>
                            Copier le lien du profil
                        </Menu.Label>
                    </Menu.Item>
                    <Menu.Item>
                        <Link2 className="size-4 mr-2" strokeWidth={1.2} />
                        <Menu.Label>
                            Partager via lien
                        </Menu.Label>
                    </Menu.Item>
                    <Menu.Item>
                        <IconBrandFacebook className="size-4 mr-2" strokeWidth={1.2} />
                        <Menu.Label>
                            Partager sur Facebook
                        </Menu.Label>
                    </Menu.Item>
                    <Menu.Item>
                        <IconBrandTwitter className="size-4 mr-2" strokeWidth={1.2} />
                        <Menu.Label>
                            Partager sur Twitter
                        </Menu.Label>
                    </Menu.Item>
                </Menu.Content>
            </Menu>
        </>
    )
}
