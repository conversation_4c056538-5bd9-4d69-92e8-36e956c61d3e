"use client"

import Link from "next/link"
import { Button, buttonStyles } from "../ui/button"
import { Menu } from "../ui/menu"
import { HandHelping, User2 } from "lucide-react"

export const AppNavLogout = () => {
    return (
        <>
            <div className="hidden sm:flex items-center gap-2">
                <Link href={"/auth/connexion"} className={buttonStyles({ intent: "plain", size: "small", className: "h-9 rounded-xl" })}>
                    Se connecter
                </Link>
                <Link href={"/auth/inscription"} className={buttonStyles({ intent: "primary", size: "small", className: "h-9 rounded-xl" })}>
                    Créer un compte
                </Link>
            </div>
            <div className="flex sm:hidden">
                <Menu>
                    <Button intent="none" size="none" className={"p-0.5 text-fg-muted size-9 rounded-xl bg-bg flex items-center justify-center border shadow-sm"}>
                        <User2 strokeWidth={1.2} className="size-4"/>
                    </Button>
                    <Menu.Content placement="bottom" className="min-w-dvw sm:min-w-48">
                        <Menu.Item href='/parametres'>
                            <Menu.Label>Se connecter</Menu.Label>
                        </Menu.Item>
                        <Menu.Item href='/parametres'>
                            <Menu.Label>S'inscrire</Menu.Label>
                        </Menu.Item>
                        <Menu.Item>
                            <HandHelping strokeWidth={1.2} className="size-4 mr-2.5" />
                            <Menu.Label>Aides</Menu.Label>
                        </Menu.Item>
                    </Menu.Content>
                </Menu>
            </div>
        </>
    )
}
