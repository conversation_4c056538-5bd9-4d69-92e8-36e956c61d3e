"use client"

import { ReactNode } from "react"
import { useScrollBackground } from "@/hooks/use-scroll-background"
import { cn } from "@/lib/utils"

export const AppNavWrapper = ({ children }: { children: ReactNode }) => {
    const isScrolled = useScrollBackground()
    return (
        <header className={cn("fixed w-full left-0 top-0 h-16 flex items-center z-[60] transition-colors duration-200 border-b bg-bg border-border/60", `${isScrolled ? 'bg-bg border-border/60' : 'bg-transparent border-transparent'}`)}>
            <nav className="app-container px-4 sm:px-6 lg:px-4 flex items-center justify-between">
                {children}
            </nav>
        </header>
    )
}
