"use client"

import { useEffect, useState, type ReactNode } from "react"
import { createPortal } from "react-dom"

interface PortalProps {
  children: ReactNode
}

export function AppPortal({ children }: PortalProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)

    return () => setMounted(false)
  }, [])

  return mounted ? createPortal(children, document.body) : null
}
