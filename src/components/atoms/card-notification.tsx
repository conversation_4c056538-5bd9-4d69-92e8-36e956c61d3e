import { CardNotificationProps } from '@/types';
import { Button } from '../ui/button';

export const CardNotification = ({
    type,
    title,
    message,
    sender,
    time,
    source,
    onApprove,
    onDeny,
    fileName
}: CardNotificationProps) => {
    return (
        <div className="flex items-start gap-[15px] px-4">
            <div className="relative flex shrink-0 items-center justify-center rounded-full select-none text-center uppercase size-10 text-sm bg-border text-fg">
                <span className="font-medium">{sender.slice(0, 2).toUpperCase()}</span>
                <div className="absolute flex size-8 items-center justify-center drop-shadow-[0_2px_4px_#1b1c1d0a] -right-1.5 scale-[.5625] top-0 origin-top-right">
                    <div className="box-content size-3 rounded-full border-4 border-white bg-red-500" />
                </div>
            </div>
            <div className="space-y-4 flex-1">
                <div className="space-y-1">
                    <div className="text-sm font-normal text-fg-muted">
                        <strong className="font-medium text-gray-950">{title}</strong> <br />
                        <strong className="font-medium text-fg-title">{sender}</strong> {message}
                        {type === 'file' && fileName && (
                            <span className="ml-2 text-fg-primary">📎 {fileName}</span>
                        )}
                    </div>
                    <div className="flex items-center gap-1 text-xs text-fg-muted">
                        <span>{time}</span>
                        {source && <><span className="px-0.5">∙</span><div className="flex items-center gap-1"><div className="size-4 shrink-0 bg-gradient-to-br from-blue-400 to-purple-500 rounded" /><span>{source}</span></div></>}
                    </div>
                </div>
                {(type === 'action' || type === 'demande_rendez_vous') && (
                    <div className="flex gap-2.5">
                        <Button intent="outline" size="small" className="h-7 text-xs px-3" onClick={onDeny}>
                            Annuler
                        </Button>
                        <Button size="small" className="h-7 text-xs px-3" onClick={onApprove}>
                            Accepter
                        </Button>
                    </div>
                )}
                {type === 'facture' && (
                    <div className="flex gap-2.5">
                        <Button intent="outline" size="small" className="h-7 text-xs px-3" onClick={onDeny}>
                            Details
                        </Button>
                        <Button size="small" className="h-7 text-xs px-3" onClick={onApprove}>
                            Valider
                        </Button>
                    </div>
                )}
                {type === 'rendez_vous' && (
                    <div className="text-sm text-fg-primary">
                        Rendez-vous confirmé par l'artiste.
                    </div>
                )}
                {type === 'demande_signature' && (
                    <div className="text-sm text-fg-primary">
                        Demande de signature pour le contrat.
                    </div>
                )}
            </div>
        </div>
    );
}
