"use client"

import { useEffect, useImperativeHandle, useRef, forwardRef, } from "react";
import { VideoPlay, VideoPlayProps } from "@/components/atoms/video-play";


export const ForceVideoPlay = forwardRef<HTMLVideoElement, VideoPlayProps>(
    ({ src, poster, className, volume, ...props }, ref) => {
        const videoRef = useRef<HTMLVideoElement>(null);
        useImperativeHandle(ref, () => videoRef.current as HTMLVideoElement);

        useEffect(() => {
            if (videoRef.current) {
                const video = videoRef.current
                const playPromise = video.play();
                if (playPromise !== undefined) {
                    playPromise.catch((error) => {
                        console.warn("Autoplay failed:", error);
                    });
                }
            }
        }, []);

        return (
            <VideoPlay
                ref={videoRef}
                src={src}
                playsInline
                autoPlay
                muted
                loop
                preload="auto"
                {...props}
                className={className}
            />
        )
    }
)