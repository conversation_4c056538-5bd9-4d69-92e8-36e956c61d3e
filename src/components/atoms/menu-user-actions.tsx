"use client"

import { <PERSON>, <PERSON>ers2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>el<PERSON>, LogOut } from "lucide-react"

import { Avatar } from "../ui/avatar"
import { Menu } from "../ui/menu"
import { Button } from "../ui/button"
import { App_User } from "@/lib/fakeAuthStore"
import { useAuthStore } from "@/lib/auth/authStore"
import { SwitchThemeInDrop } from "./switch-theme-in-drop"


export const MenuUserActions = ({ user }: { user: App_User }) => {

    const { logout } = useAuthStore()
    const logoutUser = async () => {
        await logout()
    }
    return (
        <>
            <Menu>
                <Button intent="none" size="none">
                    <Avatar src={user.type === "artiste" && 'artisteData' in user ? user.artisteData.avatar : 'orgData' in user ? user.orgData.profileImage : ""} size="medium" alt={`${user.type === "artiste" && 'artisteData' in user ? user.artisteData.username : 'orgData' in user ? user.orgData.name : ""}`} initials={`${user.type === "artiste" && 'artisteData' in user ? user.artisteData.username.charAt(0) : 'orgData' in user ? user.orgData.name.charAt(0) : ""}`} />
                </Button>
                <Menu.Content showArrow placement="bottom" className="min-w-dvw sm:min-w-64">
                    <Menu.Section>
                        <Menu.Header separator>
                            {
                                user.type === "artiste" && 'artisteData' in user ?
                                    <div className="flex items-center gap-3">
                                        <Avatar src={user.artisteData.avatar} size="large" alt={`${user.artisteData.username}`} initials={`${user.artisteData.username.charAt(0)}`} />
                                        <div className="flex-1 flex flex-col">
                                            <span className="block line-clamp-1">
                                                {user.artisteData.about.firstName} {user.artisteData.about.lastName}
                                            </span>
                                            <span className="font-normal text-fg-muted text-sm line-clamp-1">
                                                @{user.artisteData.username}
                                            </span>
                                        </div>
                                    </div> : (
                                        <div className="flex items-center gap-3">
                                            <Avatar src={user.orgData.profileImage} size="large" alt={`${user.orgData.name}`} initials={`${user.orgData.name.charAt(0)}`} />
                                            <div className="flex-1 flex flex-col">
                                                <span className="block line-clamp-1">
                                                    {user.orgData.name}
                                                </span>
                                                <span className="font-normal text-fg-muted text-sm line-clamp-1">
                                                    @{user.orgData.slug}
                                                </span>
                                            </div>
                                        </div>
                                    )
                            }
                            <Menu.Separator />
                            <SwitchThemeInDrop />
                        </Menu.Header>
                    </Menu.Section>
                    {
                        user.type === "org" ? (
                            <Menu.Item href="/mon-organisme">
                                <Building strokeWidth={1.2} className="size-4 mr-2.5" />
                                <Menu.Label>Mon organization</Menu.Label>
                            </Menu.Item>
                        ) : null
                    }
                    {
                        user.type === "artiste" ? <Menu.Item href="/mon-espace">
                            <Layers2 strokeWidth={1.2} className="size-4 mr-2.5" />
                            <Menu.Label>Dashboard</Menu.Label>
                        </Menu.Item> : null
                    }
                    <Menu.Item href='/parametres'>
                        <Settings strokeWidth={1.2} className="size-4 mr-2.5" />
                        <Menu.Label>Parametres</Menu.Label>
                    </Menu.Item>
                    <Menu.Item>
                        <HandHelping strokeWidth={1.2} className="size-4 mr-2.5" />
                        <Menu.Label>Aides</Menu.Label>
                    </Menu.Item>
                    <Menu.Separator />
                    <Menu.Item onAction={async () => {
                        await logoutUser()
                    }} isDanger>
                        <LogOut strokeWidth={1.2} className="size-4 mr-2.5" />
                        <Menu.Label>Deconnection</Menu.Label>
                    </Menu.Item>
                </Menu.Content>
            </Menu>
        </>
    )
}
