"use client"

import Link from 'next/link'
import React from 'react'
import {  buttonStyles } from '../ui/button'
import { MessagesSquare } from 'lucide-react'

export const MessageLinkClient = () => {
    return (
        <>
            <Link href="/messages" className={buttonStyles({ intent: "plain", size: 'square-petite' })}>
                <MessagesSquare strokeWidth={1.2} className="size-4" />
            </Link>
        </>
    )
}
