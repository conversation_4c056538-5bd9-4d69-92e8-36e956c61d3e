"use client"



import { <PERSON><PERSON><PERSON>, <PERSON>, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { Button } from "../ui/button"
import { ClientOnly } from "./client-only"

export const SwitchThemeInDrop = () => {
    const { setTheme, theme } = useTheme()

    const changeTheme = (val: string) => {
        setTheme(val)
    }
    return (
        <>
            <ClientOnly>
                <div className="w-full mt-2.5 grid grid-cols-3 border bg-bg border-border rounded-xl p-1">
                    <Button onPress={() => changeTheme("system")} data-state={theme === "system" ? "active" : "inactive"} intent="plain" size="none" className="data-[state=active]:bg-bg-surface px-2 py-2 text-xs rounded-sm hover:bg-bg-surface transition-colors">
                        <Laptop strokeWidth={1.2} className="size-4" />
                    </Button>
                    <Button onPress={() => changeTheme("light")} data-state={theme === "light" ? "active" : "inactive"} intent="plain" size="none" className="data-[state=active]:bg-bg-surface px-2 py-2 text-xs rounded-sm hover:bg-bg-surface transition-colors">
                        <Moon strokeWidth={1.2} className="size-4" />
                    </Button>
                    <Button onPress={() => changeTheme("dark")} data-state={theme === "dark" ? "active" : "inactive"} intent="plain" size="none" className="data-[state=active]:bg-bg-surface px-2 py-2 text-xs rounded-sm hover:bg-bg-surface transition-colors">
                        <Sun strokeWidth={1.2} className="size-4" />
                    </Button>
                </div>
            </ClientOnly>
        </>
    )
}
