"use client"

import { IconMoon, IconSun } from "@intentui/icons"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"

export function ThemeSwitcher() {
    const { theme, setTheme } = useTheme()

    const toggleTheme = () => {
        const nextTheme = theme === "light" ? "dark" : "light"
        setTheme(nextTheme)
    }

    return (
        <Button
            shape={"square"}
            intent={"plain"}
            size="square-petite"
            aria-label="Switch theme"
            onClick={() => toggleTheme()}

        >
             <IconSun className="dark:hidden"/>
             <IconMoon className="hidden dark:inline"/>
        </Button>
    )
}
