"use client"

import { useNavbarDispatch, useNavbarState } from "@/context/app-actions-context"

export const ToggleNavAction = () => {
    const { isOpen } = useNavbarState();
    const dispatch = useNavbarDispatch();

    const toggleNavbar = () => {
        dispatch({ type: 'TOGGLE_NAVBAR' });
    };

    return (
        <>
            <div className="flex xl:hidden">
                <button onClick={toggleNavbar} className="outline-none border-l border-border pl-3 relative py-3" aria-label="Toggle navbar">
                    <span aria-hidden="true" className={`
                            flex h-0.5 w-6 rounded bg-fg transition duration-300
                            ${isOpen ? "rotate-45 translate-y-[0.33rem]" : ""}
                        `} />
                    <span aria-hidden="true" className={`
                            flex mt-2 h-0.5 w-6 rounded bg-fg transition duration-300
                            ${isOpen ? "-rotate-45 -translate-y-[0.33rem]" : ""}
                        `} />
                </button>
            </div>
        </>
    );
};