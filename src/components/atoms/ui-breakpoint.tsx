"use client"
import { useBreakpoints } from "@/hooks/use-breakpoints"
import { ReactNode } from "react"


export const UiBreakpoint = ({ children, size }: { children: ReactNode, size: "lg" | "md" | "sm" | "mobile" | "mobile-sm" |"mobile-sm-tablet" | "md-sm" | "lg-md" }) => {
    const { isDesktop, isMiddle, isMobile, isTablet } = useBreakpoints()
    if (size === "mobile" && isMobile) {
        return <>
            {children}
        </>
    }
    if (size === "mobile-sm" && (isMobile || isMiddle)) return <>{children}</>
    if(size === "mobile-sm-tablet" && (isMobile || isMiddle || isTablet)) return <>{children}</>
    if (size === "sm" && isMiddle) return <>{children}</>
    if (size === "md-sm" && (isMiddle || isTablet)) return <>{children}</>
    if (size === "md" && isTablet) return <>{children}</>
    if (size === "lg-md" && (isTablet || isDesktop)) return <>{children}</>
    if (size === "lg" && isDesktop) return <>{children}</>

    return <></>
}
