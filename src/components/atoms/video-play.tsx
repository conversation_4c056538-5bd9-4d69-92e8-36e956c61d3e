"use client"
import React, { useEffect, useImperativeHandle } from "react";


export interface VideoPlayProps extends React.VideoHTMLAttributes<HTMLVideoElement> {
    src: string;
    poster?: string;
    volume?: number
}

export const VideoPlay = React.forwardRef<HTMLVideoElement, VideoPlayProps>(
    ({ src, poster, className, volume, ...props }, ref) => {
        const videoRef = React.useRef<HTMLVideoElement>(null);
        useImperativeHandle(ref, () => videoRef.current as HTMLVideoElement);

        useEffect(() => {
            if (videoRef.current && typeof volume === 'number') {
                videoRef.current.volume = volume;
            }
        }, [volume]);

        return (
            <video
                ref={videoRef}
                src={src}
                poster={poster}
                className={`${className || ''}`}
                playsInline
                {...props}
            />
        );
    }
);

VideoPlay.displayName = 'VideoPlay';
