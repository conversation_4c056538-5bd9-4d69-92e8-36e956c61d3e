"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar } from "@/components/ui/avatar"
import { Calendar, Users, CheckCircle, Clock, CreditCard, Bell } from "lucide-react"
import { U_Message } from "@/data/messages"



interface ActionMessageProps {
  message: U_Message
}

export function ActionMessage({ message }: ActionMessageProps) {
  if (!message.action) return null

  const getActionIcon = () => {
    switch (message.action?.type) {
      case "meeting":
        return <Calendar className="h-5 w-5" />
      case "payment":
        return <CreditCard className="h-5 w-5" />
      case "task":
        return <CheckCircle className="h-5 w-5" />
      case "reminder":
        return <Bell className="h-5 w-5" />
      default:
        return <Clock className="h-5 w-5" />
    }
  }

  const getActionColor = () => {
    switch (message.action?.type) {
      case "meeting":
        return "bg-muted/80 border-border"
      case "payment":
        return "bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800"
      case "task":
        return "bg-purple-50 border-purple-200 dark:bg-purple-950 dark:border-purple-800"
      case "reminder":
        return "bg-orange-50 border-orange-200 dark:bg-orange-950 dark:border-orange-800"
      default:
        return "bg-muted border-border"
    }
  }

  const renderActionContent = () => {
    switch (message.action?.type) {
      case "meeting":
        return (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-blue-600" />
              <span className="font-medium">{message.action.data.title}</span>
            </div>
            <div className="text-sm text-fg-muted">
              <p>{message.action.data.date}</p>
            </div>
            <div className="flex space-x-2">
              <Button size="small" >
                Rejoindre
              </Button>
              <Button size="small" intent="outline">
                Voir le details
              </Button>
            </div>
          </div>
        )

      case "payment":
        return (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4 text-green-600" />
                <span className="font-medium">Payment Request</span>
              </div>
              <Badge intent="secondary" className="bg-green-100 text-green-800">
                ${message.action.data.amount}
              </Badge>
            </div>
            <p className="text-sm text-fg-muted">{message.action.data.description}</p>
            <div className="flex space-x-2">
              <Button size="small">
                Pay Now
              </Button>
              <Button size="small" intent="outline">
                Decline
              </Button>
            </div>
          </div>
        )

      case "task":
        return (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-purple-600" />
              <span className="font-medium">{message.action.data.title}</span>
            </div>
            <p className="text-sm text-fg-muted">Due: {message.action.data.dueDate}</p>
            <div className="flex space-x-2">
              <Button size="small">
                Mark Complete
              </Button>
              <Button size="small" intent="outline">
                View Task
              </Button>
            </div>
          </div>
        )

      default:
        return (
          <div className="space-y-2">
            <p className="font-medium">{message.content}</p>
            <p className="text-sm text-fg-muted">System notification</p>
          </div>
        )
    }
  }

  return (
    <div className="flex justify-center">
      <div className={`max-w-md w-full rounded-lg ${getActionColor()}`}>
        <div className="p-4">
          {renderActionContent()}
          <div className="mt-3 pt-2 border-t border-border/50">
            <span className="text-xs text-fg-muted">
              {message.timestamp.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
