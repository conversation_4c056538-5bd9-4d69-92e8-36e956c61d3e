"use client"

import { Ava<PERSON> } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Download, ExternalLink, FileText, ImageIcon, Play, CheckCheck, Clock, Music } from "lucide-react"
import { cn } from "@/lib/utils"
import { U_Message } from "@/data/messages"


interface AttachmentMessageProps {
  message: U_Message
  showAvatar: boolean
}

export function AttachmentMessage({ message, showAvatar }: AttachmentMessageProps) {
  const isUser = message.sender === "user"

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const getFileIcon = (type: string) => {
    switch (type) {
      case "image":
        return <ImageIcon className="h-4 w-4" />
      case "video":
        return <Play className="h-4 w-4" />
      case "audio":
        return <Music className="h-4 w-4" />
      case "link":
        return <ExternalLink className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getStatusIcon = () => {
    switch (message.status) {
      case "sending":
        return <Clock className="h-3 w-3 text-fg-muted" />
      case "sent":
        return <CheckCheck className="h-3 w-3 text-fg-muted" />
      case "delivered":
        return <CheckCheck className="h-3 w-3 text-blue-500" />
      case "read":
        return <CheckCheck className="h-3 w-3 text-blue-600" />
      default:
        return null
    }
  }

  return (
    <div
      className={cn("flex items-end space-x-2 max-w-[80%]", isUser ? "ml-auto flex-row-reverse space-x-reverse" : "")}
    >
      {showAvatar && !isUser && (
        <Avatar initials="ML"/>
      )}

      <div className={cn("space-y-2", !showAvatar && !isUser && "ml-10")}>
        {message.content && (
          <div
            className={cn(
              "rounded-2xl px-4 py-2 max-w-full break-words",
              isUser ? "bg-primary text-fg-primary-btn rounded-br-md" : "bg-muted rounded-bl-md",
            )}
          >
            <p className="text-sm leading-relaxed">{message.content}</p>
          </div>
        )}

        <div className="space-y-2">
          {message.attachments?.map((attachment) => (
            <Card key={attachment.id} className="overflow-hidden">
              <CardContent className="p-0">
                {attachment.type === "image" && (
                  <div className="relative group">
                    <img
                      src={attachment.thumbnail || attachment.url}
                      alt={attachment.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Button intent="secondary" size="small">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </div>
                )}

                {attachment.type === "video" && (
                  <div className="relative group">
                    <div className="w-full h-48 bg-muted flex items-center justify-center">
                      {attachment.thumbnail ? (
                        <img
                          src={attachment.thumbnail || "/placeholder.svg"}
                          alt={attachment.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Play className="h-12 w-12 text-fg-muted" />
                      )}
                    </div>
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Button intent="secondary" size="small">
                        <Play className="h-4 w-4 mr-2" />
                        Play
                      </Button>
                    </div>
                  </div>
                )}

                {(attachment.type === "file" || attachment.type === "audio") && (
                  <div className="p-4 flex items-center space-x-3">
                    <div className="p-2 bg-muted rounded-lg">{getFileIcon(attachment.type)}</div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{attachment.name}</p>
                      {attachment.size && (
                        <p className="text-xs text-fg-muted">{formatFileSize(attachment.size)}</p>
                      )}
                    </div>
                    <Button intent="plain" size="small">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        <div
          className={cn(
            "flex items-center space-x-1 text-xs text-fg-muted",
            isUser ? "justify-end" : "justify-start",
          )}
        >
          <span>
            {message.timestamp.toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </span>
          {isUser && getStatusIcon()}
        </div>
      </div>
    </div>
  )
}
