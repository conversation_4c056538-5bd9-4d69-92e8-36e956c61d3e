"use client"

import React, { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { BookmarkCheck, ChevronRight, HandHelping, ReceiptText } from 'lucide-react'
import { ContratForm } from '../contrat/contrat-form'
import { NewProporsalModal } from '../app-forms/new-proporsal'

export const ChatActionOrg = () => {
    const [contratOpen, setContratOpen] = useState(false)
    const [proporsalOpen, setProposalOpen] = useState(false)
    return (
        <>
            <div className="rounded-lg mt-1 grid border border-border overflow-hidden divide-y divide-border">
                <Button onPress={() => setProposalOpen(true)} intent="plain" className={"justify-between rounded-none"}>
                    <div className="flex-1 flex items-center">
                        <HandHelping strokeWidth={1.2} className='size-4 mr-2' />
                        Nouvelle sollicitation
                    </div>
                    <ChevronRight strokeWidth={1.2} className="size-3.5" />
                </Button>
                <Button onPress={() => setContratOpen(true)} intent="plain" className={"justify-between rounded-none"}>
                    <div className="flex-1 flex items-center">
                        <ReceiptText strokeWidth={1.2} className='size-4 mr-2' />
                        Generer un contrat
                    </div>
                    <ChevronRight strokeWidth={1.2} className="size-3.5" />
                </Button>
                <Button intent="plain" className={"justify-between rounded-none"}>
                    <div className="flex-1 flex items-center">
                        <BookmarkCheck strokeWidth={1.2} className='size-4 mr-2' />
                        Reserver une date
                    </div>
                    <ChevronRight strokeWidth={1.2} className="size-3.5" />
                </Button>
            </div>
            <ContratForm open={contratOpen} onOpenChange={() => setContratOpen(false)} onSubmit={function (data: { description: string; artiste: string; projet: string; dateDebut: string; dateFin: string; lieu: string; remuneration: string; typeContrat: 'freelance' | 'cdd' | 'prestation' | 'autre'; statut: 'draft' | 'pending' | 'active' | 'completed'; conditions?: string | undefined }): void {
                throw new Error('Function not implemented.')
            }} mode={'create'} />

            <NewProporsalModal isOpen={proporsalOpen} onClose={() => setProposalOpen(false)} />

        </>
    )
}
