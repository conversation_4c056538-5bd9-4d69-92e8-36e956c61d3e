"use client"

import { ReactNode } from "react"
import { ChatInfo } from "./chat-info"
import Link from "next/link"
import { Archive, Ban, ChevronLeft, EllipsisVertical, FileUser, TriangleAlert } from "lucide-react"
import { Button, buttonStyles } from "@/components/ui/button"
import { Avatar } from "@/components/ui/avatar"
import { Sheet } from "@/components/ui/sheet"
import { Menu } from "../ui/menu"
import { ChatInput } from "./chat-input"
import { ArtisteProfile } from "@/types/artiste-profile"
import { Organization } from "@/types/org_"


type ChatHeaderProps = {
    children: ReactNode,
} & ({
    detailShowIsFor: "artiste",
    user: ArtisteProfile
} | {
    detailShowIsFor: "org",
    user: Organization
})


export const ChatDetailBox = ({ children, detailShowIsFor, user }: ChatHeaderProps) => {
    return (
        <div className="grid lg:grid-cols-[1fr_320px] h-full max-h-[calc(100dvh-5.5rem+0.75rem)] md:max-h-[calc(100dvh-5.5rem)]">
            <div className="flex flex-col h-full overflow-hidden w-full">
                <div className="px-4 py-0.5 border-b h-14 grid w-full items-center">
                    <div className="flex w-full items-center flex-1 gap-2 sm:gap-3 -ml-2.5 md:ml-0">
                        <Link href="/messages" className={buttonStyles({ intent: "plain", className: "rounded-full size-10 md:hidden", size: "extra-small" })}>
                            <ChevronLeft strokeWidth={1.5} className="size-5" />
                        </Link>
                        <div className="flex items-center gap-2 flex-1">
                            {
                                detailShowIsFor === "artiste" ? (
                                    <>
                                        <Avatar src={user.avatar} alt={`Avatar de ${user.about.firstName} `} initials={`${user.about.firstName.charAt(0)}${user.about.lastName.charAt(0)}`} size="large" className="size-8 *:size-8 sm:size-10 sm:*:size-10" />
                                        <Link href={`/${user.username}`} className="text-sm flex flex-col overflow-hidden flex-1">
                                            <span className="text-fg-title line-clamp-1 font-semibold">{user.about.firstName} {user.about.lastName}</span>
                                            <span className="text-fg-muted text-xs">Offline</span>
                                        </Link>
                                    </>
                                ) : <>
                                    <Avatar src={user.profileImage} alt={`Avatar de ${user.name} `} initials={`${user.name.charAt(0)}`} size="large" className="size-8 *:size-8 sm:size-10 sm:*:size-10" />
                                    <Link href={`/organisations/${user.slug}`} className="text-sm flex flex-col overflow-hidden flex-1">
                                        <span className="text-fg-title line-clamp-1 font-semibold">{user.name}</span>
                                        <span className="text-fg-muted text-xs">Offline</span>
                                    </Link>
                                </>
                            }
                        </div>
                        <div className="flex items-center min-w-max">
                            <div className="hidden sm:flex items-center">
                                <Button size="extra-small" intent="plain" className={"rounded-full size-10"}>
                                    <TriangleAlert strokeWidth={1.2} className="size-4" />
                                </Button>
                                <Button size="extra-small" intent="plain" className={"rounded-full size-10"}>
                                    <Archive strokeWidth={1.2} className="size-4" />
                                </Button>
                                <Button size="extra-small" intent="plain" className={"rounded-full size-10"}>
                                    <Ban strokeWidth={1.2} className="size-4" />
                                </Button>
                            </div>
                            <div className="flex lg:hidden">
                                <div className="sm:hidden flex">
                                    <Menu>
                                        <Menu.Trigger className={buttonStyles({ intent: "plain", size: "extra-small", className: "size-10 justify-center" })}>
                                            <EllipsisVertical strokeWidth={1.2} className="size-4" />
                                        </Menu.Trigger>
                                        <Menu.Content className="max-md:w-dvw">
                                            <Menu.Item id={"menu-signaler"}>
                                                <TriangleAlert strokeWidth={1.2} className="size-4 mr-2 text-fg-muted" />
                                                <Menu.Label>
                                                    Signaler
                                                </Menu.Label>
                                            </Menu.Item>
                                            <Menu.Item id={"menu-archive"}>
                                                <Archive strokeWidth={1.2} className="size-4 mr-2 text-fg-muted" />
                                                <Menu.Label>
                                                    Archiver la discussion
                                                </Menu.Label>
                                            </Menu.Item>
                                            <Menu.Item id={"menu-block"}>
                                                <Ban strokeWidth={1.2} className="size-4 mr-2 text-fg-muted" />
                                                <Menu.Label>
                                                    Bloquer
                                                </Menu.Label>
                                            </Menu.Item>

                                        </Menu.Content>
                                    </Menu>
                                </div>
                                <Sheet>
                                    <Button size="extra-small" intent="plain" className={"rounded-full size-10"}>
                                        <FileUser strokeWidth={1.2} className="size-4" />
                                    </Button>
                                    <Sheet.Content>
                                        {
                                            detailShowIsFor === "artiste" ? (
                                                <ChatInfo for="artiste" artiste={user} />
                                            ) : <ChatInfo for="org" org={user} />
                                        }
                                    </Sheet.Content>
                                </Sheet>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex-1 flex flex-col overflow-hidden">
                    <div className="flex-1 py-6 overflow-hidden overflow-y-auto scroll-hidden px-4 sm:px-6 lg:px-4">
                        <div className="mx-auto pb-20 h-full w-full max-w-xl pt-8">
                            {children}
                        </div>
                    </div>
                    <div className="h-14 flex items-center border-t px-4 sm:px-6 lg:px-4 py-2">
                        <ChatInput onSendMessage={(e) => { }} />
                    </div>
                </div>
            </div>
            <div className="hidden lg:flex border-l border-border/80 h-full lg:flex-col">
                {
                    detailShowIsFor === "artiste" ? (
                        <ChatInfo for="artiste" artiste={user} />
                    ) : <ChatInfo for="org" org={user} />
                }
            </div>
        </div>
    )
}
