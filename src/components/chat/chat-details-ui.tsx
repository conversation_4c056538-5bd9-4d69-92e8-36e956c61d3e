"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { TextField as Input } from "@/components/ui/text-field"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, Paperclip, MoreVertical, Phone, Video, Search } from "lucide-react"
import { SingleUserMessage, UserMessage } from "@/types/message"
import { MessageBubble } from "./message-bubble"
import { AttachmentMessage } from "./attachement-message"
import { ActionMessage } from "./action-message"

// import { LoadingMessage } from "./loading-message"



export function ChatDetails({ messages: messages_ }: { messages: UserMessage["messages"] }) {
    const [messages, setMessages] = useState<UserMessage["messages"]>(messages_)
    const [newMessage, setNewMessage] = useState("")
    const [isLoading, setIsLoading] = useState(false)
    const scrollAreaRef = useRef<HTMLDivElement>(null)
    const fileInputRef = useRef<HTMLInputElement>(null)

    useEffect(() => {
        if (scrollAreaRef.current) {
            scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
        }
    }, [messages])

    const handleSendMessage = async () => {
        if (!newMessage.trim()) return

        const message: SingleUserMessage = {
            id: Date.now().toString(),
            type: "text",
            content: newMessage,
            sender: "user",
            timestamp: new Date(),
            status: "sending",
            senderIs: "artiste"
        }

        setMessages((prev) => [...prev, message])
        setNewMessage("")
        setIsLoading(true)

        // Simulate message sending
        setTimeout(() => {
            setMessages((prev) => prev.map((msg) => (msg.id === message.id ? { ...msg, status: "sent" as const } : msg)))
            setIsLoading(false)
        }, 1000)
    }

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files
        if (!files) return

        Array.from(files).forEach((file) => {
            const attachment = {
                id: Date.now().toString() + Math.random(),
                name: file.name,
                type: file.type.startsWith("image/") ? ("image" as const) : ("file" as const),
                url: URL.createObjectURL(file),
                size: file.size,
                thumbnail: file.type.startsWith("image/") ? URL.createObjectURL(file) : undefined,
            }

            const message: SingleUserMessage = {
                id: Date.now().toString() + Math.random(),
                type: "attachment",
                content: `Shared ${file.name}`,
                sender: "user",
                timestamp: new Date(),
                status: "sending",
                attachments: [attachment],
                senderIs: "artiste"
            }

            setMessages((prev) => [...prev, message])
        })
    }

    return (
        <div className="flex flex-col h-screen max-w-4xl mx-auto border-x">
            {/* Chat Header */}
            <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="flex items-center space-x-3">
                    {/* <Avatar className="h-10 w-10">
            <AvatarImage src="/placeholder.svg?height=40&width=40" />
            <AvatarFallback>SC</AvatarFallback>
          </Avatar> */}
                    <div>
                        <h2 className="font-semibold">Sarah Chen</h2>
                        <p className="text-sm text-fg-muted">Online • Last seen 2 min ago</p>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <Button intent="plain" size="square-petite">
                        <Search className="h-4 w-4" />
                    </Button>
                    <Button intent="plain" size="square-petite">
                        <Phone className="h-4 w-4" />
                    </Button>
                    <Button intent="plain" size="square-petite">
                        <Video className="h-4 w-4" />
                    </Button>
                    <Button intent="plain" size="square-petite">
                        <MoreVertical className="h-4 w-4" />
                    </Button>
                </div>
            </div>

            {/* Messages Area */}
            <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>

            </ScrollArea>

            {/* Message Input */}
            <div className="p-4 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="flex items-end space-x-2">
                    <div className="flex-1 relative">
                        <Input
                            value={newMessage}
                            onChange={(e) => setNewMessage(e)}
                            placeholder="Type a message..."
                            className="pr-12 min-h-[44px] resize-none"
                            onKeyDown={(e) => {
                                if (e.key === "Enter" && !e.shiftKey) {
                                    e.preventDefault()
                                    handleSendMessage()
                                }
                            }}
                        />
                        <Button
                            intent="plain"
                            size="square-petite"
                            className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8"
                            onClick={() => fileInputRef.current?.click()}
                        >
                            <Paperclip className="h-4 w-4" />
                        </Button>
                    </div>
                    <Button
                        onClick={handleSendMessage}
                        isDisabled={!newMessage.trim() || isLoading}
                        className="h-11 w-11"
                        size="square-petite"
                    >
                        <Send className="h-4 w-4" />
                    </Button>
                </div>

                <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    className="hidden"
                    onChange={handleFileUpload}
                    accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
                />
            </div>
        </div>
    )
}
