"use client"

import { Chat, U_Message } from "@/data/messages"

import { AttachmentMessage } from "./attachement-message"
import { MessageBubble } from "./message-bubble"
import { ActionMessage } from "./action-message"
import { useState } from "react"




interface ChatDetailsProps {
  chat: Chat
}


export function ChatDetails({ chat }: ChatDetailsProps) {
  const [messages, setMessages] = useState<U_Message[]>(chat.messages)


  return (
    <>
      <div className="space-y-4 pb-6">
        {messages.map((message, index) => {
          const showAvatar =
            message.sender !== "user" && (index === 0 || messages[index - 1].sender !== message.sender)
          const avatar = chat.contactType === "artiste" ? {
            avatar: chat.contact.avatar
          } : chat.contactType === "organisation" ? {
            avatar:chat.contact.profileImage
          } : {
            initial: "US"
          }
          const showTimestamp =
            index === 0 ||
            new Date(message.timestamp).getTime() - new Date(messages[index - 1].timestamp).getTime() > 300000

          return (
            <div key={message.id} className="space-y-2">

              {message.type === "text" && <MessageBubble message={message} showAvatar={showAvatar} avatar={avatar}/>}

              {message.type === "attachment" && <AttachmentMessage message={message} showAvatar={showAvatar} />}

              {message.type === "action" && <ActionMessage message={message} />}
            </div>
          )
        })}

        {/* {isLoading && <LoadingMessage />} */}
      </div>
    </>
  )
}

