import { Avatar } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArtisteProfile } from '@/types/artiste-profile'
import { Organization } from '@/types/org_'
import { BookmarkCheck, ChevronRight, HandHelping, MapPin, ReceiptText, Tag } from 'lucide-react'
import Image from 'next/image'
import React from 'react'
import { ChatActionOrg } from './chat-action-org'
import Link from 'next/link'

type ChatInfoType = {
    for: "artiste"
    artiste: ArtisteProfile
} | {
    for: "org",
    org: Organization
}

export const ChatInfo = (info: ChatInfoType) => {
    return (
        <>
            <div className="px-4 py-0.5 border-b h-14 flex items-center justify-center">
                <span className="font-semibold text-fg-title">
                    Info
                </span>
            </div>
            <div className="p-4 flex-1">
                {
                    info.for === "artiste" ? (
                        <header className="rounded-xl p-1 bg-bg border border-border/70">
                            <div className="h-20 sm:rounded-lg bg-primary-50 overflow-hidden relative">
                                <Image
                                    src={info.artiste.mediaCover}
                                    alt={"Cover app profile"}
                                    width={1000}
                                    height={1000} className="size-full object-cover sm:rounded-lg" />
                                <span className="absolute inset-0 hidden dark:flex bg-gradient-to-b from-primary-950 via-primary-400/20 to-transparent"></span>
                            </div>
                            <div className="px-3 sm:px-4 md:px-5 -mt-9 relative flex items-end justify-between">
                                <div className="flex justify-center sm:justify-start flex-1">
                                    <div className="size-16 overflow-hidden rounded-full border-2 border-bg">
                                        <Image
                                            src={info.artiste.avatar}
                                            width={200}
                                            height={200}
                                            alt={"artistName"}
                                            className="size-full object-cover"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className="text-center sm:text-left px-4 md:px-5 mt-3">
                                <div className='flex flex-wrap items-center justify-center sm:justify-start'>
                                    <Link href={`/${info.artiste.username}`} className='font-semibold text-fg-title'>
                                        {info.artiste.about.firstName} {info.artiste.about.lastName}
                                    </Link>
                                </div>
                                <div className="mt-3.5 flex items-center justify-center sm:justify-start flex-wrap gap-x-5 gap-y-2.5">
                                    <Badge intent="none" className="rounded-xl bg-primary text-fg-primary-btn w-max">
                                        <Tag strokeWidth={1.2} className='size-4' />
                                        {info.artiste.genre}
                                    </Badge>
                                    <span className='text-sm text-fg-muted flex items-center'>
                                        <MapPin className='size-4 mr-0.5' strokeWidth={1.2} />
                                        {info.artiste.about.city}
                                    </span>
                                </div>
                                <div className="mt-5 flex items-center justify-center sm:justify-start gap-2">

                                </div>
                            </div>
                        </header>
                    ) : (
                        <header className="rounded-xl p-1 bg-bg border border-border/70">
                            <div className="h-20 sm:rounded-lg bg-primary-50 overflow-hidden relative">
                                <Image
                                    src={"/default_cover.webp"}
                                    alt={"Cover app profile"}
                                    width={1000}
                                    height={1000} className="size-full object-cover sm:rounded-lg" />
                                <span className="absolute inset-0 hidden dark:flex bg-gradient-to-b from-primary-950 via-primary-400/20 to-transparent"></span>
                            </div>
                            <div className="px-3 sm:px-4 md:px-5 -mt-8 relative flex items-end justify-between">
                                <div className="flex justify-center sm:justify-start flex-1">
                                    <div className="overflow-hidden rounded-full border-4 border-bg bg-bg">
                                        <Avatar className="size-14 *:size-14" initials={info.org.name.charAt(0)} />
                                    </div>
                                </div>
                            </div>
                            <div className="text-center sm:text-left px-4 md:px-5 mt-3">
                                <div className='flex flex-wrap items-center justify-center sm:justify-start'>
                                    <Link href={`/organisations/${info.org.slug}`} className='font-semibold text-fg-title'>
                                        {info.org.name}
                                    </Link>
                                </div>
                                <div className="mt-3.5 flex items-center justify-center sm:justify-start flex-wrap gap-x-5 gap-y-2.5">
                                    <span className='text-sm text-fg-muted flex items-center'>
                                        <MapPin className='size-4 mr-0.5' strokeWidth={1.2} />
                                        {info.org.location}
                                    </span>
                                </div>
                                <div className="mt-5 flex items-center justify-center sm:justify-start gap-2">

                                </div>
                            </div>
                        </header>
                    )
                }
                {
                    info.for === "artiste" ?
                        <div className="mt-4">
                            <span className='text-xs text-fg-muted uppercase'>Collaboration</span>
                            <ChatActionOrg />
                        </div>
                        : <>
                        </>
                }
            </div>
        </>
    )
}
