"use client"

import { But<PERSON> } from '@/components/ui/button'
import { Send } from 'lucide-react'
import React from 'react'

export const ChatInputMessageBox = () => {
    return (
        <div className="flex items-center gap-2">
                    <textarea name="" id="" className="h-10 px-4 py-2 text-sm resize-none border border-border rounded-xl flex-1"></textarea>
                    <Button
                        size="square-petite"
                        className={"rounded-xl size-10"}
                    >
                        <Send className="size-4" strokeWidth={1.2} />
                    </Button>
                </div>
    )
}
