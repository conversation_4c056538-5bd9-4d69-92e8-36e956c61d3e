import { Send, Paperc<PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { TextField as Input } from "@/components/ui/text-field";
import { useState } from "react";

interface ChatInputProps {
    onSendMessage: (message: string) => void;
}

export function ChatInput({ onSendMessage }: ChatInputProps) {
    const [message, setMessage] = useState("");

    const handleSend = () => {
        if (message.trim()) {
            onSendMessage(message);
            setMessage("");
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };

    return (
        <>
            <div className="flex items-center space-x-2 w-full mx-auto max-w-xl">

                <div className="flex-1 relative">
                    <Input
                        value={message}
                        onChange={(e) => setMessage(e)}
                        onKeyDown={handleKeyPress}
                        placeholder="Type a message..."
                        className="[&_input]:pe-12 [&_input]:ps-10"
                    />
                    <Button intent="plain" size="square-petite" className="absolute left-1 top-1/2 transform -translate-y-1/2">
                        <Paperclip className="h-4 w-4" />
                    </Button>
                    <Button
                        intent="plain"
                        size="square-petite"
                        className="absolute right-1 top-1/2 transform -translate-y-1/2"
                    >
                        <Smile className="h-4 w-4" />
                    </Button>
                </div>
                <Button
                    onClick={handleSend}
                    isDisabled={!message.trim()}
                    size="extra-small"
                    className="hidden sm:flex bg-primary hover:bg-primary/90 size-9"
                >
                    <Send strokeWidth={1.2} className="size-4" />
                </Button>
            </div>
        </>
    );
}
