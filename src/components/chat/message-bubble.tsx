"use client"

import { Avatar } from "@/components/ui/avatar"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface Message {
  id: string
  type: "text" | "attachment" | "action" | "system"
  content: string
  sender: "user" | "contact" | "system"
  timestamp: Date
  status?: "sending" | "sent" | "delivered" | "read"
}

interface MessageBubbleProps {
  message: Message
  showAvatar: boolean,
  avatar: {
    avatar?: string;
    initial?: string;
  }
}

export function MessageBubble({ message, showAvatar, avatar }: MessageBubbleProps) {
  const isUser = message.sender === "user"

  const getStatusIcon = () => {
    switch (message.status) {
      case "sending":
        return <Clock className="h-3 w-3 text-fg-muted" />
      case "sent":
        return <CheckCheck className="h-3 w-3 text-fg-muted" />
      case "delivered":
        return <CheckCheck className="h-3 w-3 text-blue-500" />
      case "read":
        return <CheckCheck className="h-3 w-3 text-blue-600" />
      default:
        return null
    }
  }

  return (
    <div
      className={cn("flex items-end space-x-2 max-w-[80%]", isUser ? "ml-auto flex-row-reverse space-x-reverse" : "")}
    >
      {
        showAvatar && !isUser ? (<>
      {
        avatar.avatar ? <Avatar src={avatar.avatar} initials="ml" className="size-8 *:size-8" /> : <Avatar initials={avatar.initial} className="size-8 *:size-8" />
      }
        </>) : null
      }

      <div
        className={cn(
          "rounded-2xl px-4 py-2 max-w-full break-words",
          isUser ? "bg-primary text-fg-primary-btn rounded-br-md" : "bg-muted rounded-bl-md",
          !showAvatar && !isUser && "ml-10",
        )}
      >
        <p className="text-sm leading-relaxed">{message.content}</p>

        {isUser && (
          <div className="flex items-center justify-end mt-1 space-x-1">
            <span className="text-xs opacity-70">
              {message.timestamp.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </span>
            {getStatusIcon()}
          </div>
        )}
      </div>

      {!isUser && (
        <span className="text-xs text-fg-muted self-end pb-1">
          {message.timestamp.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </span>
      )}
    </div>
  )
}
