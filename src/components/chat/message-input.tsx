"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Paperclip, Send } from "lucide-react"
import { FormEvent, useState } from "react"

type MessageInputProps = {
  onSendMessage: (message: string) => void
  disabled?: boolean
}

export const MessageInput = ({ onSendMessage, disabled = false }: MessageInputProps) => {
  const [message, setMessage] = useState('')

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    if (message.trim()) {
      onSendMessage(message)
      setMessage('')
    }
  }

  return (
    <form onSubmit={handleSubmit} className="border-t border-border/60 p-3">
      <div className="flex items-center gap-2">
        <Button 
          type="button" 
          size="extra-small"
          intent="plain" 
          className="rounded-full" 
          isDisabled={disabled}
        >
          <Paperclip className="size-5" />
        </Button>
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Écrivez votre message..."
          className="flex-1 bg-muted/50 rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary"
          disabled={disabled}
        />
        <Button 
          type="submit" 
          size="extra-small"
          className="rounded-full" 
          isDisabled={disabled || !message.trim()}
        >
          <Send className="size-5" />
        </Button>
      </div>
    </form>
  )
}