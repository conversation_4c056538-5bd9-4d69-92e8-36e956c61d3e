"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Menu } from "@/components/ui/menu"
import { Archive, ChevronDown, HandHelping, Inbox, Search } from "lucide-react"
import { useState } from "react"
import { Input } from "react-aria-components"

const dropFilterMessageItems = [
    {
        id: 1,
        text: "Messages",
        name: "all_messages",
        icon: <Inbox className="size-5" strokeWidth={1.2} />
    }, {
        id: 2,
        text: "Archives",
        name: "archived_messages",
        icon: <Archive className="size-5" strokeWidth={1.2} />
    }, {
        id: 3,
        text: "Solicitations",
        name: "proposal_messages",
        icon: <HandHelping className="size-5" strokeWidth={1.2} />
    }
]

export const ChatListHeader = () => {
    const [selectedType, setSelectedType] = useState(dropFilterMessageItems[0])

    return (
        <>
            <div className="p-4 border-b border-border flex md:flex-col items-center md:items-start gap-4">
                <div className="flex items-center">
                    <Menu>
                        <Button className={"px-2.5 -ml-2.5"} size='small' intent="plain">
                            {selectedType.icon}
                            <span className="ml-0.5">{selectedType.text}</span>
                            <ChevronDown strokeWidth={1.2} className='size-4 ml-0.5' />
                        </Button>
                        <Menu.Content className="sm:min-w-3xs min-w-dvw">
                            {dropFilterMessageItems.map((item) => (
                                <Menu.Item
                                    key={item.id}
                                    isSelected={item.id === selectedType.id}
                                    onAction={() => setSelectedType(item)}
                                >
                                    {item.icon}
                                    <Menu.Label className="ml-2">
                                        {item.text}
                                    </Menu.Label>
                                </Menu.Item>
                            ))}
                        </Menu.Content>
                    </Menu>
                </div>
                {/* Search */}
                <div className="relative hidden sm:flex sm:flex-1 md:w-full bg-bg-surface/40 rounded-xl focus-within:border-primary border-2 border-border/60 focus-within:bg-bg">
                    <Input className={"h-9 flex w-full items-center py-1 ps-10 rounded-xl outline-none focus:outline-none"} placeholder='Search...' />
                    <Search strokeWidth={1.2} className="absolute top-2.5 left-3.5 size-4" />
                </div>
                
            </div>
        </>
    )
}
