"use client"

import { Avatar } from '@/components/ui/avatar'
import { Archive, BotMessageSquare, Inbox, MessageSquare, Paperclip, Reply } from 'lucide-react'
import Link from 'next/link'
import { useState } from 'react'
import { mockChatsForArtiste, mockChatsForOrg } from '@/data/mock-chats'
import { Chat } from '@/data/messages'

const filterOptions = [
  { id: 'all', label: 'All Messages', icon: <Inbox className="size-4" /> },
  { id: 'unread', label: 'Unread', icon: <MessageSquare className="size-4" /> },
  { id: 'archived', label: 'Archived', icon: <Archive className="size-4" /> }
]

export function ChatList({ listFor, list }: { listFor: "artiste" | "org", list: Chat[] }) {
  const [filter, setFilter] = useState(filterOptions[0])
  const [searchQuery, setSearchQuery] = useState('')

  // Select the appropriate chat list based on listFor parameter
  const chats = listFor === "artiste" ? mockChatsForArtiste : mockChatsForOrg

  const filteredChats = chats.filter(chat =>
    chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    chat.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // const lastChat = list[list.length - 1].messages

  return (
    <>
      {list.length === 0 ? (
        <div className="flex items-center justify-center h-full p-4 text-center text-fg-muted">
          <div>
            <MessageSquare className="mx-auto mb-2 size-8 opacity-40" />
            <p>No messages found</p>
          </div>
        </div>
      ) : (
        <ul className="divide-y ">
          {list.map(chat => {
            const lastMessage = chat.messages[chat.messages.length - 1]
            return <>
              <li className='w-full overflow-hidden' key={chat.id}>
                <Link
                  href={`/messages/${chat.slug}`}
                  className="w-full flex items-center p-4 hover:bg-muted"
                >
                  <div className="relative mr-3 flex min-w-max">
                    <Avatar size="large" src={chat.contactType === "artiste" ? chat.contact.avatar : chat.contact.profileImage} alt={`Avatar de ${chat.contactType === "artiste" ? chat.contact.username : chat.contact.name}`} initials={chat.contactType === "artiste" ? chat.contact.about.firstName.charAt(0) : chat.contact.name.charAt(0)} />
                    {/* {chat.isOnline && (
                    <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></span>
                  )} */}
                  </div>
                  <div className="flex-1 min-w-0 max-w-full">
                    <div className="flex justify-between items-baseline">
                      <h3 className="font-medium truncate text-sm">
                        {
                          chat.contactType === "artiste" ? `${chat.contact.about.firstName} ${chat.contact.about.lastName}` : chat.contact.name
                        }
                      </h3>
                      <span className="text-xs text-fg-muted ml-2">
                        {/* {chat.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} */}
                      </span>
                    </div>
                    <p className="text-xs sm:text-sm text-fg-muted line-clamp-1">
                      {/* {chat.lastMessage} */}
                      {
                        lastMessage.type === "text" ? <>
                          {lastMessage.sender === "user" ? <>
                            <span className="flex items-center flex-1 line-clamp-1">
                              <Reply strokeWidth={1.2} className="size-3 mr-2" />
                              <span className='flex-1 line-clamp-1'>{lastMessage.content}</span>
                            </span>
                          </> : lastMessage.content}
                        </> : lastMessage.type === "action" ? <>
                          <span className="flex items-center line-clamp-1">
                            <BotMessageSquare strokeWidth={1.2} className="size-3 mr-2" />
                            <span className='flex-1 line-clamp-1'>{lastMessage.content}</span>
                          </span>
                        </> : lastMessage.type === "attachment" ? <>
                        <span className="flex items-center line-clamp-1">
                            <Paperclip strokeWidth={1.2} className="size-3 mr-2" />
                            <span className='flex-1 line-clamp-1'>{lastMessage.content}</span>
                          </span>
                        </> : <>{lastMessage.content}</>
                      }
                    </p>
                  </div>
                  {/* {chat.unread > 0 && (
                  <span className="ml-3 bg-fg-title text-bg text-xs font-medium px-2 py-0.5 rounded-full">
                    {chat.unread}
                  </span>
                )} */}
                </Link>
              </li>
            </>
          })}
        </ul>
      )}
    </>
  )
}