"use client"

import { Chat<PERSON><PERSON>Header } from './chat-list-header'
import { ChatList } from './chat-list'
import { Chat } from '@/data/messages'

export const ChatSideBar = ({ forUser, list}: { forUser: "artiste" | "org", list:Chat[] }) => {

    return (
        <>
            <div className={`md:border-r border border-border/60 md:border-0 h-[calc(100dvh-5.5rem+0.75rem)] md:h-full md:max-h-[calc(100dvh-5.5rem)] flex flex-col`}>
                <ChatListHeader />
                <div className="flex-1 overflow-hidden overflow-y-auto scroll-hidden">
                    <div className="grid md:flex md:flex-col md:flex-1">
                        <ChatList list={list} listFor={forUser}/>
                    </div>
                </div>
            </div>
        </>
    )
}