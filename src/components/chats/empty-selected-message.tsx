import { MessageCircleDashed } from "lucide-react"


export const EmptySelectedMessage = () => {
    return (
        <div className="flex h-full w-full items-center justify-center flex-col">
            <MessageCircleDashed className="size-16 text-fg-muted/80" strokeWidth={1.2} />
            <div className="text-center text-fg-muted mt-4">
                <span className="text-fg-title font-medium">
                    Aucun message selectionné
                </span>
                <p className="text-sm text-fg-muted mt-3 mx-auto max-w-sm">Sélectionnez une conversation pour commencer à échanger des messages</p>
            </div>
        </div>
    )
}
