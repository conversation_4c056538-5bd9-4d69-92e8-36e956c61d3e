"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Form } from "@/components/ui/form"

import { Textarea } from "@/components/ui/textarea"
import { Select } from "@/components/ui/select"
import { ContratPreview } from "./contrat-preview"
import { Eye } from "lucide-react"
import { Modal } from "../ui/modal"
import { TextField } from "../ui/text-field"
import { DateField } from "../ui/date-field"
import { cities } from "@/data/cities"
import { NumberField } from "../ui/number-field"

const contratFormSchema = z.object({
    artiste: z.string().min(3, {
        message: "Le nom de l'artiste doit contenir au moins 3 caractères.",
    }),
    projet: z.string().min(5, {
        message: "Le nom du projet doit contenir au moins 5 caractères.",
    }),
    description: z.string().min(10, {
        message: "La description doit contenir au moins 10 caractères.",
    }),
    dateDebut: z.string().min(1, {
        message: "La date de début est requise.",
    }),
    dateFin: z.string().min(1, {
        message: "La date de fin est requise.",
    }),
    lieu: z.string().min(2, {
        message: "Le lieu doit être spécifié.",
    }),
    remuneration: z.string().min(1, {
        message: "La rémunération est requise.",
    }),
    typeContrat: z.enum(["freelance", "cdd", "prestation", "autre"], {
        required_error: "Veuillez sélectionner un type de contrat.",
    }),
    statut: z.enum(["draft", "pending", "active", "completed"], {
        required_error: "Veuillez sélectionner un statut.",
    }),
    conditions: z.string().optional(),
})

type ContratFormValues = z.infer<typeof contratFormSchema>

interface ContratFormProps {
    open: boolean
    onOpenChange: () => void
    initialData?: Partial<ContratFormValues>
    onSubmit: (data: ContratFormValues) => void
    mode: "create" | "edit"
}

export function ContratForm({ open, onOpenChange, initialData, onSubmit, mode }: ContratFormProps) {
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [isPreviewOpen, setIsPreviewOpen] = useState(false)

    const defaultValues: Partial<ContratFormValues> = {
        artiste: "",
        projet: "",
        description: "",
        dateDebut: "",
        dateFin: "",
        lieu: "",
        remuneration: "",
        typeContrat: "prestation",
        statut: "draft",
        conditions: "",
        ...initialData,
    }

    const form = useForm<ContratFormValues>({
        resolver: zodResolver(contratFormSchema),
        defaultValues,
    })

    function handleSubmit(data: ContratFormValues) {
        setIsSubmitting(true)

        // Simuler un délai d'envoi
        setTimeout(() => {
            onSubmit(data)
            setIsSubmitting(false)
            form.reset()
        }, 500)
    }

    return (
        <>

            <Modal.Content isOpen={open} onOpenChange={onOpenChange} classNames={{ content: "sm:max-w-[700px] max-h-[90vh] overflow-y-auto" }}>
                <Modal.Header>
                    <Modal.Title>{mode === "create" ? "Créer un nouveau contrat" : "Modifier le contrat"}</Modal.Title>
                </Modal.Header>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(handleSubmit)}>

                        <Modal.Body className="space-y-6">
                            <TextField onChange={e => form.setValue("projet", e)} label="Intitulé de la prestation" />
                            <Textarea label="Description"
                                placeholder="Décrivez les détails du contrat, les responsabilités de l'artiste..."
                                className="resize-none"
                                onChange={e => form.setValue("description", e)}
                            />

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <DateField onChange={e => form.setValue("dateDebut", `${e?.toString()}`)} label="Date Debut" />
                                <DateField onChange={e => form.setValue("dateFin", `${e?.toString()}`)} label="Date Fin" />

                                <Select className='col-span-full sm:col-span-1' label="Localisation" placeholder="Selectionner une ville">
                                    <Select.Trigger />
                                    <Select.List onSelectionChange={e => form.setValue("lieu", e.toString())} items={cities}>
                                        {(item) => (
                                            <Select.Option id={item.id} textValue={item.name}>
                                                {item.name}
                                            </Select.Option>
                                        )}
                                    </Select.List>
                                </Select>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <NumberField onChange={e => form.setValue("remuneration", `${e}`)} label="Rémunération (€)" placeholder="ex: 2500" />
                                <Select onSelectionChange={e => form.setValue("typeContrat", `${e?.toString()}` as "freelance" | "cdd" | "prestation" | "autre")} className='col-span-full sm:col-span-1' label="Type contrat" placeholder="Selectionner une ville">
                                    <Select.Trigger />
                                    <Select.List>
                                        <Select.Option id="freelance" textValue="Freelance">
                                            Freelance
                                        </Select.Option>
                                        <Select.Option id="cdd" textValue="CDD">
                                            CDD
                                        </Select.Option>
                                        <Select.Option id="prestation" textValue="Prestation">
                                            Prestation
                                        </Select.Option>
                                        <Select.Option id="autre" textValue="Autre">
                                            Autre
                                        </Select.Option>
                                    </Select.List>
                                </Select>
                                <Select
                                    className='col-span-full sm:col-span-1'
                                    label="Statut"
                                    placeholder="Sélectionnez un statut"
                                    onSelectionChange={e => form.setValue("statut", `${e?.toString()}` as "draft" | "pending" | "active" | "completed")}
                                >
                                    <Select.Trigger />
                                    <Select.List>
                                        <Select.Option id="draft" textValue="Brouillon">
                                            Brouillon
                                        </Select.Option>
                                        <Select.Option id="pending" textValue="En attente">
                                            En attente
                                        </Select.Option>
                                        <Select.Option id="active" textValue="Actif">
                                            Actif
                                        </Select.Option>
                                        <Select.Option id="completed" textValue="Terminé">
                                            Terminé
                                        </Select.Option>
                                    </Select.List>
                                </Select>
                            </div>
                            <Textarea onChange={e => form.setValue("description", e)} label="Conditions particulières (optionnel)" />


                        </Modal.Body>
                        <Modal.Footer>
                            <Button type="button" intent="outline" onClick={() => onOpenChange()}>
                                Annuler
                            </Button>
                            <Button type="button" intent="outline" onClick={() => setIsPreviewOpen(true)} className="gap-2">
                                <Eye className="h-4 w-4" />
                                Aperçu
                            </Button>
                            <Button type="submit" isDisabled={isSubmitting}>
                                {isSubmitting
                                    ? "Création en cours..."
                                    : mode === "create"
                                        ? "Créer le contrat"
                                        : "Enregistrer les modifications"}
                            </Button>
                        </Modal.Footer>
                    </form>
                </Form>
            </Modal.Content>


            <ContratPreview
                open={isPreviewOpen}
                onOpenChange={setIsPreviewOpen}
                contratData={form.getValues()}
                onEdit={() => setIsPreviewOpen(false)}
                onDownload={() => {
                    console.log("Téléchargement du contrat...")
                }}
                onSend={() => {
                    console.log("Envoi pour signature...")
                }}
            />
        </>
    )
}
