"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Modal } from "@/components/ui/modal"
import { Badge } from "@/components/ui/badge"
import { FileText, Download, Send, Edit, Calendar, MapPin, Euro, Clock, Signature } from "lucide-react"

interface ContratData {
  id?: string
  artiste: string
  projet: string
  description: string
  dateDebut: string
  dateFin: string
  lieu: string
  remuneration: string
  typeContrat: string
  statut: string
  conditions?: string
  // Données supplémentaires pour le contrat
  nomOrganisme?: string
  adresseOrganisme?: string
  representant?: string
  adresseArtiste?: string
  telephoneArtiste?: string
  heureDebut?: string
  heureFin?: string
  modalitePaiement?: string
  delaiPaiement?: string
  delaiAnnulation?: string
  pourcentageAnnulation?: string
  villeSignature?: string,


  isArtiste?: boolean,
  showAddSignature?: boolean
}

interface ContratPreviewProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  contratData: ContratData
  onEdit?: () => void
  onSend?: () => void
  onDownload?: () => void
}

export function ContratPreview({ open, onOpenChange, contratData, onEdit, onSend, onDownload }: ContratPreviewProps) {
  const [isGenerating, setIsGenerating] = useState(false)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      day: "numeric",
      month: "long",
      year: "numeric",
    })
  }

  const getCurrentDate = () => {
    return new Date().toLocaleDateString("fr-FR", {
      day: "numeric",
      month: "long",
      year: "numeric",
    })
  }

  const handleDownload = () => {
    setIsGenerating(true)
    // Simuler la génération du PDF
    setTimeout(() => {
      setIsGenerating(false)
      onDownload?.()
    }, 2000)
  }

  // Données par défaut pour la démo
  const defaultData = {
    nomOrganisme: "Théâtre National de Paris",
    adresseOrganisme: "123 Avenue des Arts, 75001 Paris",
    representant: "Marie Dubois, Directrice Artistique",
    adresseArtiste: "45 Rue de la Danse, 75011 Paris",
    telephoneArtiste: "06 12 34 56 78",
    heureDebut: "19h00",
    heureFin: "21h30",
    modalitePaiement: "Dans les 30 jours suivant la prestation",
    delaiAnnulation: "7",
    pourcentageAnnulation: "50",
    villeSignature: "Paris",
    ...contratData,
  }

  return (
    <Modal isOpen={open} onOpenChange={onOpenChange}>
      <Modal.Content classNames={{ content: "sm:max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col" }}>
        <Modal.Header className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <Modal.Title className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Aperçu du Contrat
            </Modal.Title>
            <Badge className="text-xs">
              {defaultData.typeContrat}
            </Badge>
          </div>
        </Modal.Header>

        {/* Contenu du contrat */}
        <div className="flex-1 overflow-y-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white text-black p-8 mx-4 rounded-lg shadow-sm border min-h-[800px]"
            style={{ fontFamily: "serif" }}
          >
            {/* En-tête */}
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold mb-2">CONTRAT DE PRESTATION ARTISTIQUE</h1>
              <div className="text-sm text-gray-600">Contrat n° {defaultData.id || "2025-001"}</div>
            </div>

            {/* Parties contractantes */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-4">Entre les soussignés :</h2>

              <div className="grid md:grid-cols-2 gap-6 mb-6">
                <div className="border rounded-lg p-4 bg-gray-50">
                  <h3 className="font-semibold mb-2">Organisme</h3>
                  <div className="space-y-1 text-sm">
                    <div>
                      <strong>Nom :</strong> {defaultData.nomOrganisme}
                    </div>
                    <div>
                      <strong>Adresse :</strong> {defaultData.adresseOrganisme}
                    </div>
                    <div>
                      <strong>Représenté par :</strong> {defaultData.representant}
                    </div>
                  </div>
                  <div className="mt-2 text-xs italic">Ci-après désigné le "Donneur d'ordre"</div>
                </div>

                <div className="border rounded-lg p-4 bg-gray-50">
                  <h3 className="font-semibold mb-2">Artiste Danseur(se)</h3>
                  <div className="space-y-1 text-sm">
                    <div>
                      <strong>Nom :</strong> {defaultData.artiste}
                    </div>
                    <div>
                      <strong>Adresse :</strong> {defaultData.adresseArtiste}
                    </div>
                    <div>
                      <strong>Téléphone :</strong> {defaultData.telephoneArtiste}
                    </div>
                  </div>
                  <div className="mt-2 text-xs italic">Ci-après désigné "l'Artiste"</div>
                </div>
              </div>
            </div>

            {/* Articles du contrat */}
            <div className="space-y-6">
              {/* Article 1 */}
              <div>
                <h3 className="font-semibold mb-2">Article 1 – Objet du contrat</h3>
                <p className="text-sm mb-3">
                  Le présent contrat a pour objet la réalisation d'une prestation artistique de danse par l'Artiste pour
                  le compte du Donneur d'ordre dans le cadre de :
                </p>
                <div className="bg-blue-50 p-4 rounded-lg space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <strong>Événement :</strong> {defaultData.projet}
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    <strong>Lieu :</strong> {defaultData.lieu}
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <strong>Date :</strong> Du {formatDate(defaultData.dateDebut)} au {formatDate(defaultData.dateFin)}
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <strong>Horaires :</strong> {defaultData.heureDebut} à {defaultData.heureFin}
                  </div>
                </div>
                {defaultData.description && (
                  <div className="mt-3 text-sm">
                    <strong>Description :</strong> {defaultData.description}
                  </div>
                )}
              </div>

              {/* Article 2 */}
              <div>
                <h3 className="font-semibold mb-2">Article 2 – Rémunération</h3>
                <p className="text-sm mb-2">Le Donneur d'ordre s'engage à verser à l'Artiste la somme de :</p>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 text-lg font-semibold text-green-700">
                    <Euro className="h-5 w-5" />
                    {defaultData.remuneration} EUR, toutes charges comprises
                  </div>
                </div>
                <p className="text-sm mt-2">
                  <strong>Le paiement s'effectuera :</strong> {defaultData.modalitePaiement}
                </p>
              </div>

              {/* Article 3 */}
              <div>
                <h3 className="font-semibold mb-2">Article 3 – Obligations de l'Artiste</h3>
                <p className="text-sm mb-2">L'Artiste s'engage à :</p>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Se présenter à l'heure convenue</li>
                  <li>• Réaliser la prestation dans des conditions professionnelles</li>
                  <li>• Ne pas annuler sauf cas de force majeure</li>
                  <li>• Respecter les consignes de sécurité du lieu</li>
                </ul>
              </div>

              {/* Article 4 */}
              <div>
                <h3 className="font-semibold mb-2">Article 4 – Obligations du Donneur d'ordre</h3>
                <p className="text-sm mb-2">Le Donneur d'ordre s'engage à :</p>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Fournir l'équipement sonore et logistique nécessaire</li>
                  <li>• Verser le paiement à la date convenue</li>
                  <li>• Assurer la sécurité de l'Artiste durant la prestation</li>
                  <li>• Mettre à disposition les espaces de préparation nécessaires</li>
                </ul>
              </div>

              {/* Article 5 */}
              <div>
                <h3 className="font-semibold mb-2">Article 5 – Assurance et Responsabilité</h3>
                <p className="text-sm">
                  Chaque partie est responsable de ses assurances respectives. Le Donneur d'ordre couvre les risques
                  liés à l'organisme de l'événement et met en place les mesures de sécurité appropriées.
                </p>
              </div>

              {/* Article 6 */}
              <div>
                <h3 className="font-semibold mb-2">Article 6 – Annulation</h3>
                <div className="text-sm space-y-2">
                  <p>
                    En cas d'annulation par le Donneur d'ordre à moins de {defaultData.delaiAnnulation} jours de
                    l'événement, {defaultData.pourcentageAnnulation}% de la rémunération est due.
                  </p>
                  <p>
                    En cas d'annulation par l'Artiste sans justification valable, aucune rémunération ne sera versée.
                  </p>
                </div>
              </div>

              {/* Conditions particulières */}
              {defaultData.conditions && (
                <div>
                  <h3 className="font-semibold mb-2">Article 7 – Conditions particulières</h3>
                  <p className="text-sm">{defaultData.conditions}</p>
                </div>
              )}

              {/* Article final */}
              <div>
                <h3 className="font-semibold mb-2">Article {defaultData.conditions ? "8" : "7"} – Litiges</h3>
                <p className="text-sm">
                  En cas de litige, les parties s'engagent à rechercher une solution amiable. À défaut, les tribunaux de{" "}
                  {defaultData.villeSignature} seront seuls compétents.
                </p>
              </div>
            </div>

            {/* Signatures */}
            <div className="mt-8 pt-6 border-t">
              <div className="text-center mb-6">
                <p className="text-sm">
                  Fait à {defaultData.villeSignature}, le {getCurrentDate()}
                </p>
                <p className="text-sm">En deux exemplaires originaux.</p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                <div className="text-center">
                  <div className="border-2 border-dashed border-gray-300 h-24 flex items-center justify-center mb-2 rounded">
                    <span className="text-gray-400 text-sm">Signature du Donneur d'ordre</span>
                  </div>
                  <p className="text-sm font-medium">{defaultData.representant}</p>
                </div>
                <div className="text-center">
                  <div className="border-2 border-dashed border-gray-300 h-24 flex items-center justify-center mb-2 rounded">
                    <span className="text-gray-400 text-sm">Signature de l'Artiste</span>
                  </div>
                  <p className="text-sm font-medium">{defaultData.artiste}</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Actions */}
        <Modal.Footer className="flex-shrink-0 border-t pt-4">
          <div className="flex items-center gap-2 w-full">
            {
              contratData.isArtiste ? <>
                <Button intent="outline" onClick={handleDownload} isDisabled={isGenerating} className="gap-2">
                  <Download className="h-4 w-4" />
                  {isGenerating ? "Génération..." : "Télécharger PDF"}
                </Button>
                {
                  contratData.showAddSignature ? <>
                    <Button onClick={onSend} className="gap-2 ml-auto">
                      <Signature className="h-4 w-4" />
                      Ajouter ma signature
                    </Button>
                  </> : null
                }
              </> : <>
                <Button intent="outline" onClick={onEdit} className="gap-2">
                  <Edit className="h-4 w-4" />
                  Modifier
                </Button>
                <Button intent="outline" onClick={handleDownload} isDisabled={isGenerating} className="gap-2">
                  <Download className="h-4 w-4" />
                  {isGenerating ? "Génération..." : "Télécharger PDF"}
                </Button>
                <Button onClick={onSend} className="gap-2 ml-auto">
                  <Send className="h-4 w-4" />
                  Envoyer pour signature
                </Button>
              </>
            }
          </div>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  )
}
