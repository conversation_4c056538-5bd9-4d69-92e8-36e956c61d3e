
import React, { useState, useEffect, useRef } from 'react';
import { X, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, Move } from 'lucide-react';
import { ArtisteMedia } from '@/types/artiste-profile';
import { AppPortal } from '../atoms/app-portal';
import { VideoPlayBox } from './video-play-box';


interface LightboxProps {
  isOpen: boolean;
  onClose: () => void;
  currentIndex: number;
  items: ArtisteMedia[];
  onPrevious: () => void;
  onNext: () => void;
}

export const Lightbox = ({
  isOpen,
  onClose,
  currentIndex,
  items,
  onPrevious,
  onNext
}: LightboxProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  // Remove the currentItem state and use computed value instead
  const currentItem = items[currentIndex];
  const contentRef = useRef<HTMLDivElement>(null);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState(0);

  // Zoom state
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isZoomed, setIsZoomed] = useState(false);

  // Minimum distance required for a swipe to register (in pixels)
  const minSwipeDistance = 50;

  // Zoom levels and constraints
  const maxZoom = 3;
  const minZoom = 1;
  const zoomStep = 0.5;

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          onPrevious();
          break;
        case 'ArrowRight':
          onNext();
          break;
        case '+':
          handleZoomIn();
          break;
        case '-':
          handleZoomOut();
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose, onPrevious, onNext]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  useEffect(() => {
    // Reset states when navigating to a different item
    setIsLoaded(false);
    setDragOffset(0);
    resetZoom();
    
    // Use a small timeout to ensure the opacity transition works
    const timer = setTimeout(() => {
      if (currentItem?.url) {
        const preloadMedia = currentItem.type === 'image'
          ? new Image()
          : document.createElement('video');
        preloadMedia.src = currentItem.url;
        if (currentItem.type === 'image') {
          preloadMedia.onload = () => setIsLoaded(true);
        } else {
          (preloadMedia as HTMLVideoElement).onloadeddata = () => setIsLoaded(true);
        }
      }
    }, 50);

    return () => clearTimeout(timer);
  }, [currentIndex, currentItem]);

  // Reset zoom to initial values
  const resetZoom = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
    setIsZoomed(false);
  };

  // Zoom in function
  const handleZoomIn = () => {
    if (scale < maxZoom) {
      setScale(prev => {
        const newScale = Math.min(prev + zoomStep, maxZoom);
        setIsZoomed(newScale > minZoom);
        return newScale;
      });
    }
  };

  // Zoom out function
  const handleZoomOut = () => {
    if (scale > minZoom) {
      setScale(prev => {
        const newScale = Math.max(prev - zoomStep, minZoom);
        setIsZoomed(newScale > minZoom);

        // If zooming back to minimum, reset position
        if (newScale === minZoom) {
          setPosition({ x: 0, y: 0 });
        }

        return newScale;
      });
    }
  };

  // Touch event handlers for mobile swipe
  const [initialTouchDistance, setInitialTouchDistance] = useState<number | null>(null);
  const [initialScale, setInitialScale] = useState(1);

  const handleTouchStart = (e: React.TouchEvent) => {
    if (e.touches.length === 2) {
      // Handle pinch zoom start
      const distance = Math.hypot(
        e.touches[0].clientX - e.touches[1].clientX,
        e.touches[0].clientY - e.touches[1].clientY
      );
      setInitialTouchDistance(distance);
      setInitialScale(scale);
      return;
    }

    // Handle single touch for swipe
    if (scale > 1) return;
    setTouchStart(e.touches[0].clientX);
    setIsDragging(true);
    setDragOffset(0);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (e.touches.length === 2 && initialTouchDistance !== null) {
      // Handle pinch zoom
      const distance = Math.hypot(
        e.touches[0].clientX - e.touches[1].clientX,
        e.touches[0].clientY - e.touches[1].clientY
      );
      const newScale = (distance / initialTouchDistance) * initialScale;
      setScale(Math.min(Math.max(newScale, minZoom), maxZoom));
      return;
    }

    // Handle single touch move
    if (!isDragging || scale > 1) return;
    const currentTouch = e.touches[0].clientX;
    setTouchEnd(currentTouch);
    const offset = currentTouch - touchStart;
    setDragOffset(offset);
  };

  const handleTouchEnd = () => {
    setInitialTouchDistance(null);
    if (scale > 1) return;
    setIsDragging(false);
    setDragOffset(0);

    if (touchStart - touchEnd > minSwipeDistance) {
      onNext();
    } else if (touchEnd - touchStart > minSwipeDistance) {
      onPrevious();
    }
  };

  // Mouse event handlers for desktop drag navigation
  const handleMouseDown = (e: React.MouseEvent) => {
    // If zoomed in, handle pan instead of swipe
    if (scale > 1) {
      return;
    }

    setTouchStart(e.clientX);
    setIsDragging(true);
    setDragOffset(0);

    // Prevent default to avoid image dragging behavior
    e.preventDefault();
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || scale > 1) return;

    setTouchEnd(e.clientX);
    const offset = e.clientX - touchStart;
    setDragOffset(offset);

    // Prevent default to avoid selecting text while dragging
    e.preventDefault();
  };

  const handleMouseUp = () => {
    if (!isDragging) return;

    if (scale > 1) {
      setIsDragging(false);
      return;
    }

    setIsDragging(false);
    setDragOffset(0);

    // Calculate if the drag was significant enough to navigate
    if (touchStart - touchEnd > minSwipeDistance) {
      onNext();
    } else if (touchEnd - touchStart > minSwipeDistance) {
      onPrevious();
    }
  };

  // Zoom pan handlers
  const handleZoomMouseDown = (e: React.MouseEvent) => {
    if (scale <= 1) return;

    setIsDragging(true);
    // Store starting point
    setTouchStart(e.clientX);
    setTouchEnd(e.clientY);
    e.preventDefault();
  };

  const handleZoomMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || scale <= 1) return;

    // Calculate movement
    const dx = e.clientX - touchStart;
    const dy = e.clientY - touchEnd;

    // Update position
    setPosition(prev => ({
      x: prev.x + dx,
      y: prev.y + dy
    }));

    // Update starting point for next movement
    setTouchStart(e.clientX);
    setTouchEnd(e.clientY);

    e.preventDefault();
  };

  const handleZoomMouseUp = () => {
    setIsDragging(false);
  };

  // Pinch zoom for touch devices
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();

    // Zoom in or out based on wheel direction
    if (e.deltaY < 0) {
      handleZoomIn();
    } else {
      handleZoomOut();
    }
  };

  // Clean up mouse events when mouse leaves the lightbox area
  const handleMouseLeave = () => {
    if (isDragging) {
      setIsDragging(false);
      setDragOffset(0);
    }
  };

  if (!isOpen) return null;

  // Determine which mouse handlers to use based on zoom state
  const mouseHandlers = scale > 1
    ? {
      onMouseDown: handleZoomMouseDown,
      onMouseMove: handleZoomMouseMove,
      onMouseUp: handleZoomMouseUp,
    }
    : {
      onMouseDown: handleMouseDown,
      onMouseMove: handleMouseMove,
      onMouseUp: handleMouseUp,
    };

  return (
    <AppPortal>
      <div className={`lightbox-overlay animate-fade-in`}>
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-insta-white hover:text-insta-blue z-20"
          aria-label="Close lightbox"
        >
          <X size={24} />
        </button>

        {/* Zoom controls */}
        <div className="absolute bottom-4 right-4 z-20 flex flex-col gap-2">
          <button
            onClick={handleZoomIn}
            className="bg-insta-black/50 text-insta-white p-2 rounded-full hover:bg-insta-black/70 transition-colors"
            aria-label="Zoom in"
            disabled={scale >= maxZoom}
          >
            <ZoomIn size={20} />
          </button>
          <button
            onClick={handleZoomOut}
            className="bg-insta-black/50 text-insta-white p-2 rounded-full hover:bg-insta-black/70 transition-colors"
            aria-label="Zoom out"
            disabled={scale <= minZoom}
          >
            <ZoomOut size={20} />
          </button>
          <button
            onClick={resetZoom}
            className={`bg-insta-black/50 text-insta-white p-2 rounded-full hover:bg-insta-black/70 transition-colors ${!isZoomed ? 'opacity-50 cursor-not-allowed' : ''}`}
            aria-label="Reset zoom"
            disabled={!isZoomed}
          >
            <Move size={20} />
          </button>
        </div>

        {/* Navigation buttons - only show when not zoomed */}
        {scale <= 1 && (
          <>
            <button
              className="lightbox-navigation left-2 md:left-8"
              onClick={onPrevious}
              aria-label="Previous media"
            >
              <ChevronLeft size={24} className="text-insta-white" />
            </button>

            <button
              className="lightbox-navigation right-2 md:right-8"
              onClick={onNext}
              aria-label="Next media"
            >
              <ChevronRight size={24} className="text-insta-white" />
            </button>
          </>
        )}

        <div
          className="lightbox-container"
          ref={contentRef}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onWheel={handleWheel}
          onMouseLeave={handleMouseLeave}
          {...mouseHandlers}
        >
          <div
            className={`w-full h-full flex items-center justify-center transition-all duration-200 ${isDragging ? '' : 'transition-transform'}`}
            style={{
              transform: scale > 1
                ? `scale(${scale}) translate(${position.x}px, ${position.y}px)`
                : `translateX(${dragOffset}px)`,
              cursor: scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default'
            }}
          >
            {currentItem.type === 'image' ? (
              <img
                src={currentItem.url}
                alt={currentItem.title || ''}
                className={`max-h-full max-w-full object-contain transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'} select-none`}
                onLoad={() => setIsLoaded(true)}
                draggable="false"
              />
            ) : (
              <div className={`max-h-full max-w-full flex items-center justify-center transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
                    <VideoPlayBox src={currentItem.url} onLoaded={()=>setIsLoaded(true)}/>
              </div>
            )}
          </div>
        </div>
      </div>
    </AppPortal>
  );
};
