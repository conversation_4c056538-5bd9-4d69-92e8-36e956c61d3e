"use client"
import { ReactNode, useState } from 'react';

import { ArtisteMedia } from '@/types/artiste-profile';
import MediaGrid from './media-grid';
import { Lightbox } from './lightbox';

export const Mediabox = ({ data, gridClass, children, gridItemClass }: { data: ArtisteMedia[], gridClass: string, gridItemClass?: string, children?: ReactNode }) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleMediaClick = (index: number) => {
    setCurrentIndex(index);
    setLightboxOpen(true);
  };

  const handlePrevious = () => {
    setCurrentIndex((prev) =>
      prev === 0 ? data.length - 1 : prev - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prev) =>
      prev === data.length - 1 ? 0 : prev + 1
    );
  };

  const handleCloseLightbox = () => {
    setLightboxOpen(false);
  };

  return (
    <>
      <MediaGrid
        items={data}
        onMediaClick={handleMediaClick}
        className={gridClass}
        gridItemClass={gridItemClass}
      >
        {children}
      </MediaGrid>
      <Lightbox
        isOpen={lightboxOpen}
        onClose={handleCloseLightbox}
        currentIndex={currentIndex}
        items={data}
        onPrevious={handlePrevious}
        onNext={handleNext}
      />

    </>
  );
};