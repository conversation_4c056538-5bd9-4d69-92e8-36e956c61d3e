"use client"
import { MediaItem } from './media-item';
import { ArtisteMedia } from '@/types/artiste-profile';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';


interface MediaGridProps {
    items: ArtisteMedia[];
    onMediaClick: (index: number) => void;
    className?: string,
    children?: ReactNode,
    gridItemClass?:string
}

const MediaGrid = ({ items, onMediaClick, className, children, gridItemClass }: MediaGridProps) => {
    return (
        <div className={cn("", className)}>
            {items.map((item, index) => (
                <MediaItem
                    className={gridItemClass}
                    key={`media-item-${item.id}_${index}`}
                    src={item.url}
                    type={item.type}
                    alt={item.title || `Media item ${index + 1}`}
                    onClick={() => onMediaClick(index)}
                />
            ))}
            {children}
        </div>
    );
};

export default MediaGrid;
