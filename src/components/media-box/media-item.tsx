"use client"
import { cn } from '@/lib/utils';
import { Play } from 'lucide-react';
import Image from 'next/image';
import { VideoPlay } from '../atoms/video-play';

export interface MediaItemProps {
    src: string;
    type: 'image' | 'video';
    alt?: string;
    onClick?: () => void;
    className?: string
}

export const MediaItem = ({ src, type, alt = '', onClick, className }: MediaItemProps) => {
    return (
        <div onClick={onClick} className={cn("aspect-[4/5] overflow-hidden rounded-md cursor-pointer", className)}>
            {type === 'image' ? (
                <Image
                    src={src}
                    width={800}
                    height={1600}
                    alt={`Image : ${alt}`}
                    className="size-full object-cover hover:scale-110 ease-linear duration-200"
                />
            ) : (
                <div className="relative size-full">
                    <VideoPlay
                        src={src}
                        playsInline
                        className="size-full object-cover"
                    />
                    <div className="absolute inset-0 flex items-start justify-end">
                        <div className="bg-black/30 backdrop-blur-sm rounded-bl-xl p-2 text-white">
                            <Play strokeWidth={1.2} fill='white' className="size-5" />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};


