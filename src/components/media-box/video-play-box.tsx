"use client"

import { VideoPlay } from "@/components/atoms/video-play"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { EllipsisVertical, Forward, Maximize, Play, Pause, Volume2, VolumeX } from "lucide-react"
import { useRef, useState } from 'react'

export const VideoPlayBox = ({ src, onLoaded }: { src: string , onLoaded:()=>void}) => {
    const [isPlaying, setIsPlaying] = useState(true)
    const [isMuted, setIsMuted] = useState(true)
    const [progress, setProgress] = useState(0)
    const videoRef = useRef<HTMLVideoElement>(null)

    const togglePlay = () => {
        if (videoRef.current) {
            if (isPlaying) {
                videoRef.current.pause()
            } else {
                videoRef.current.play()
            }
            setIsPlaying(!isPlaying)
        }
    }

    const toggleMute = () => {
        if (videoRef.current) {
            videoRef.current.muted = !isMuted
            setIsMuted(!isMuted)
        }
    }

    const handleTimeUpdate = () => {
        if (videoRef.current) {
            const progress = (videoRef.current.currentTime / videoRef.current.duration) * 100
            setProgress(progress)
        }
    }

    const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (videoRef.current) {
            const bounds = e.currentTarget.getBoundingClientRect()
            const x = e.clientX - bounds.left
            const width = bounds.width
            const percentage = (x / width) * 100
            const time = (percentage / 100) * videoRef.current.duration
            videoRef.current.currentTime = time
            setProgress(percentage)
        }
    }

    return (
        <>
            <div className="w-full max-w-[450px] mx-auto aspect-[9/16] bg-bg-surface rounded-xl h-full min-[500px]:h-auto lg:max-h-[calc(100%-0.5rem)] relative">
                <VideoPlay
                    ref={videoRef}
                    src={src}
                    onTimeUpdate={handleTimeUpdate}
                    onEnded={() => setIsPlaying(false)}
                    playsInline
                    onLoad={()=>onLoaded()}
                    loop
                    autoPlay
                    muted
                    className="size-full object-cover min-[500px]:rounded-xl"
                />
                <div className="absolute top-2.5 inset-x-2.5 flex items-center justify-between">
                    <div className='flex items-center gap-2'>
                        <Button
                            intent="none"
                            size="square-petite"
                            className={"bg-gray-800/40 text-white rounded-full size-8 justify-center flex items-center"}
                            onClick={togglePlay}
                        >
                            {isPlaying ? (
                                <Pause className="size-4" strokeWidth={1.2} />
                            ) : (
                                <Play className="size-4" strokeWidth={1.2} />
                            )}
                        </Button>
                        <Button
                            intent="none"
                            size="square-petite"
                            className={"bg-gray-800/40 text-white rounded-full size-8 justify-center flex items-center"}
                            onClick={toggleMute}
                        >
                            {isMuted ? (
                                <VolumeX className="size-4" strokeWidth={1.2} />
                            ) : (
                                <Volume2 className="size-4" strokeWidth={1.2} />
                            )}
                        </Button>
                    </div>
                    <div className='flex items-center gap-2'>
                        <Button intent="none" size="square-petite" className={"bg-gray-800/40 text-white rounded-full size-8 justify-center flex items-center"}>
                            <Maximize className="size-4" strokeWidth={1.2} />
                        </Button>
                    </div>
                </div>

                <div className="absolute inset-x-0 bottom-0 overflow-hidden min-[500px]:rounded-xl h-6 flex items-end">
                    <div
                        className="w-full flex h-1 rounded-xl bg-white/30 relative cursor-pointer"
                        onClick={handleProgressClick}
                    >
                        <div
                            className="absolute inset-y-0 left-0 bg-primary transition-all duration-100"
                            style={{ width: `${progress}%` }}
                        />
                    </div>
                </div>
                <div className="absolute inset-y-0 right-4 gap-2 flex flex-col justify-center py-20">
                    <Button intent="none" size="square-petite" className={"bg-gray-800/40 text-white sm:bg-muted sm:hover:bg-bg-light border border-border/50 sm:text-fg rounded-full size-10 justify-center flex items-center"}>
                        <Forward className="size-4" strokeWidth={1.8} />
                    </Button>
                    <Button intent="none" size="square-petite" className={"bg-gray-800/40 text-white sm:bg-muted sm:hover:bg-bg-light border border-border/50 sm:text-fg rounded-full size-10 justify-center flex items-center"}>
                        <EllipsisVertical className="size-4" strokeWidth={1.8} />
                    </Button>
                </div>
            </div>
        </>
    )
}
