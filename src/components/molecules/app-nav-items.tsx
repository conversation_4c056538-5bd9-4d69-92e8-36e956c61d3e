
import Link from 'next/link'
import React from 'react'

export const AppNavItems = () => {
  return (
    <ul className={`flex flex-col xl:flex-row xl:items-center gap-2`}>
        <li>
            <Link href={"/explorer"} className={`flex items-center ease-linear duration-200 hover:bg-bg-surface hover:text-fg-title px-2  py-1.5 rounded-xl`}>
                Explorer
            </Link>
        </li>
        <li>
            <Link href={"/leapscroll"} className={`flex items-center ease-linear duration-200 hover:bg-bg-surface hover:text-fg-title px-2 py-1.5 rounded-xl`}>
                Leapscroll
            </Link>
        </li>
        <li>
            <Link href={"/artistes"} className={`flex items-center ease-linear duration-200 hover:bg-bg-surface hover:text-fg-title px-2 py-1.5 rounded-xl`}>
                Artistes
            </Link>
        </li>
        <li>
            <Link href={"/opportunites"} className={`flex items-center ease-linear duration-200 hover:bg-bg-surface hover:text-fg-title px-2 py-1.5 rounded-xl`}>
                Opportunités
            </Link>
        </li>
        <li>
            <Link href={"/organismes"} className={`flex items-center ease-linear duration-200 hover:bg-bg-surface hover:text-fg-title px-2 py-1.5 rounded-xl`}>
                Organismes
            </Link>
        </li>
    </ul>
  )
}
