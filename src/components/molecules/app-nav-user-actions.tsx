
import { getCurrentUser } from '@/actions/auth'

import { UserNotificationsDropdown } from './notifications-drop'
import { MessageLinkClient } from '../atoms/message-link-client'
import { MenuUserActions } from '../atoms/menu-user-actions'
import { AppNavLogout } from '../atoms/app-nav-logout'

export const AppNavUserActions = async () => {
    const user = await getCurrentUser()
    return (
        <div className="flex items-center gap-2">

            {
                user ? (
                    <>
                        <MessageLinkClient />
                        <UserNotificationsDropdown user={user}/>
                        <MenuUserActions user={user} />
                    </>
                ) : (<>
                    <AppNavLogout/>
                </>)
            }
        </div>
    )
}
