"use client"

import { But<PERSON> } from "@/components/ui/button";
import { cn } from '@/lib/utils';
import { Organization } from "@/types/org_";
import { MapPin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";


export const CardOrganization = ({ org, className }: { org: Organization, className?: string }) => {
    return (
        <div className={`${cn("flex relative flex-col border border-border/70 bg-bg rounded-xl isolate overflow-hidden p-0.5", className)}`}>
            <Image
                src={org.profileImage} 
                width={1300} 
                height={800} 
                alt={org.name}
                className="w-full aspect-[3/2.3] rounded-xl object-cover pointer-events-none" 
            />
            <div className="p-5 flex flex-col pointer-events-none">
                <h3 className="font-semibold text-fg-title mt-2 pointer-events-none">
                    {org.name}
                </h3>
                <div className="mt-3 flex flex-col gap-2 pointer-events-none">
                    <div className="flex items-center text-fg-muted text-sm">
                        <MapPin strokeWidth={1.2} className="size-4 mr-1" />
                        <span>{org.location}</span>
                    </div>
                </div>
            </div>

            <div className="flex justify-end items-center gap-2 px-3.5 pb-3.5 mt-auto">
                <Link className="before:absolute before:inset-0 flex-1" href={`/organismes/${org.slug}`}>
                    <Button intent="white/dark" size="small" className="flex-1 justify-center w-full">
                        Voir l'organisme
                    </Button>
                </Link>
            </div>
        </div>
    )
}
