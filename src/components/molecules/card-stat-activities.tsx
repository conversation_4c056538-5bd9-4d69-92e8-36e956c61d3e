import { ChevronRight } from 'lucide-react'
import Link from 'next/link'
import React, { ReactNode } from 'react'

export const CardStatActivities = ({ children, title, href }: { children: ReactNode, title: string, href: string }) => {
    return (
        <div className="bg-bg dark:bg-bg-surface/60 border border-border/60 rounded-xl">
            <div className="flex items-center justify-center px-4 sm:px-5 py-2.5">
                <div className="flex-1 font-medium text-fg-title text-sm md:text-base">
                    {title}
                </div>
                <div className="flex items-center">
                    <Link href={href} className="text-fg-primary flex items-center text-sm">
                        Voir plus
                        <ChevronRight strokeWidth={1.2} className="size-4 ml-1"/>
                    </Link>
                </div>
            </div>
            <div>
                {
                    children
                }
            </div>
        </div>
    )
}
