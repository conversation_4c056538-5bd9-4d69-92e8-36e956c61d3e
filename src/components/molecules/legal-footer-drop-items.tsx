"use client"

import React from 'react'
import { Menu } from '../ui/menu'
import { But<PERSON> } from '../ui/button'
import { ChevronDown } from 'lucide-react'

export const LegalFooterDropItems = () => {
    const legalItems = [
        { label: 'Politique des Cookies', href: '/parametres' },
        { label: 'Politique de Confidentialité', href: '/' },
        { label: 'Politique des Marques', href: '/' },
        { label: 'Conditions de Support', href: '/' },
        { label: 'Politique DMCA', href: '/' },
        { label: 'Code de Conduite des Événements', href: '/' },
        { label: 'Code de Conduite', href: '/' }
    ]

    return (
        <>
            <Menu>
                <Button intent="none" size="none" className="text-sm text-fg-muted hover:text-fg flex items-center">
                    Legal
                    <ChevronDown className="h-4 w-4 ml-1 text-fg-muted" />
                </Button>
                <Menu.Content placement="top start" className="min-w-dvw sm:min-w-48">
                    {legalItems.map((item, index) => (
                        <Menu.Item key={index} href={item.href}>
                            <Menu.Label>{item.label}</Menu.Label>
                        </Menu.Item>
                    ))}
                </Menu.Content>
            </Menu>
        </>
    )
}
