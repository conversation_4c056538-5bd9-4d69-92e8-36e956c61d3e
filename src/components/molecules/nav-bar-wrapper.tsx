"use client"

import { useNavbarDispatch, useNavbarState } from "@/context/app-actions-context"
import { AppNavItems } from "./app-nav-items"



export const NavBarWrapper = () => {
    const { isOpen } = useNavbarState()

    const dispatch = useNavbarDispatch();

    const closeNavbar = () => {
        if(isOpen) dispatch({ type: 'CLOSE_NAVBAR' });
    };
    return (
        <div onClick={closeNavbar} className={`fixed inset-x-0 h-[100dvh] xl:h-max top-0 xl:px-10  xl:translate-y-0 xl:opacity-100 left-0 bg-bg xl:!bg-transparent py-32 xl:py-0 px-5 sm:px-10 md:px-12 w-full xl:w-auto xl:top-0 xl:relative  xl:flex duration-300 ease-linear 
        xl:transition-none xl:duration-0 ${isOpen ? "" : " -translate-y-10 opacity-0 invisible xl:visible"}`}>
            <AppNavItems />
        </div>
    )
}
