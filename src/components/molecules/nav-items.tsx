"use client"

import { usePathname } from "next/navigation"

import { Telescope, BrickWallFire, UserSearch, Clapperboard, BuildingIcon } from 'lucide-react'
import Link from "next/link"


const items = [
    { id: 1, name: "Explorer", href: "/explorer", icon: <Telescope strokeWidth={1.2} className="size-5" /> },
    { id: 4, name: "Leapscroll", href: "/leapscroll", icon: <Clapperboard strokeWidth={1.2} className="size-5" /> },
    { id: 2, name: "Artist<PERSON>", href: "/artistes", icon: <UserSearch strokeWidth={1.2} className="size-5" /> },
    { id: 3, name: "Opportunites", href: "/opportunites", icon: <BrickWallFire strokeWidth={1.2} className="size-5" /> },
    { id: 5, name: "Organismes", href: "/organismes", icon: <BuildingIcon strokeWidth={1.2} className="size-5" /> },
]


export const NavItems = () => {
    const pathname = usePathname()
    return (
        <>
            <ul className="flex flex-col lg:flex-row gap-1.5">
                {
                    items.map(item => <li key={item.id} className="">
                        <Link href={item.href} data-state={pathname.includes(item.href) ? 'active' : ''} className={"flex items-center gap-1.5 text-sm lg:text-base px-2 lg:px-2.5 py-1 lg:py-1.5 rounded-xl hover:bg-bg-surface data-[state=active]:bg-muted border border-transparent data-[state=active]:border-border"}>
                            {item.icon}
                            {item.name}
                        </Link>
                    </li>)
                }
            </ul>
        </>
    )
}
