"use client"
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/field'
import { Select } from '../ui/select'
import { Search } from 'lucide-react'

const selectItems = [
    { id: 'artiste', name: 'Artiste' },
    { id: 'organisation', name: 'Organisation' },
    { id: 'mission', name: 'Artiste' }
]

export const NavSearchBox = () => {
    return (
        <>
            <div className="w-full max-w-3xs relative p-0.5 pr-2 rounded-xl bg-bg items-center focus-within:border-primary border shadow-sm focus-within:shadow-primary/20 hidden md:flex">
                <Input placeholder="Rechercher..." className={"flex-1 flex h-9 ps-9"} />
                <span className="mx-3 w-0.5 h-3/5 bg-bg-surface"></span>
                <Select placeholder="" defaultSelectedKey={'artiste'} className="w-max">
                    <Select.Trigger notStylled className="flex items-center max-w-28 text-fg" />
                    <Select.List placement="bottom" items={selectItems}>
                        {(item) => (
                            <Select.Option id={item.id} textValue={item.name}>
                                {item.name}
                            </Select.Option>
                        )}
                    </Select.List>
                </Select>
                <Search className="size-4 absolute left-3 top-3" />
            </div>
            <Button intent='plain' size="square-petite" className={"md:hidden"}>
                <Search strokeWidth={1.2} className="size-4" />
            </Button>
        </>
    )
}
