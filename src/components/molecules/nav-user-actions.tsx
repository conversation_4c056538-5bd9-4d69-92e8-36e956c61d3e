
import { getCurrentUser } from '@/actions/auth'

import { UserNotificationsDropdown } from './notifications-drop'
import { MessageLinkClient } from '../atoms/message-link-client'
import { MenuUserActions } from '../atoms/menu-user-actions'
import { ActionDisconnectedUser } from '../atoms/action-disconnected-user'
import { ThemeSwitcher } from '../atoms/theme-switcher'

import { ReactNode } from 'react'

export const NavUserActions = async ({ children }: { children?: ReactNode }) => {
    const user = await getCurrentUser()
    return (
        <div className="flex items-center gap-2">

            {
                user ? (
                    <>
                        {children}
                        <MessageLinkClient />
                        <UserNotificationsDropdown user={user}/>
                        <MenuUserActions user={user} />
                    </>
                ) : (<>
                    {children}
                    <ActionDisconnectedUser />
                    <ThemeSwitcher />
                </>)
            }
        </div>
    )
}
