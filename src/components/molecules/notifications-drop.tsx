"use client"

import { Popover } from '../ui/popover'
import { But<PERSON> } from '../ui/button'
import { Bell } from 'lucide-react'
import { Tabs } from '../ui/tabs'
import { App_User } from '@/lib/fakeAuthStore'
import { org_notifications, user_notifications } from '@/data/notifications'
import { CardNotification } from '../atoms/card-notification'

export const UserNotificationsDropdown = ({user}:{user:App_User}) => {
  return (
    <Popover>
      <Button intent="plain" size="square-petite">
        <Bell strokeWidth={1.2} className="size-4" />
      </Button>
      <Popover.Content showArrow={false} className="w-full sm:max-w-96 p-0 sm:max-h-[500px] overflow-hidden">
        <div className="px-4 py-2.5 h-max border-b border-border flex flex-row justify-between items-center w-full">
          <Popover.Title className="text-sm h-max md:text-base py-0 text-fg-title items-start justify-start">
            Notifications
          </Popover.Title>
          <div className="">
            <Button className={"h-max text-fg-primary underline underline-offset-2 text-sm"} intent="none">
              Marquer lues
            </Button>
          </div>
        </div>
        <Popover.Body className="flex flex-col flex-1 !p-0 !px-0">
          <Tabs aria-label="Notifications" className={"pb-4  w-full overflow-hidden flex-1 flex flex-col"}>
            <Tabs.List className={"w-full px-4 min-h-max overflow-hidden overflow-x-auto pt-2"}>
              <Tabs.Tab id="all">Toutes</Tabs.Tab>
              <Tabs.Tab id="proposition">Propositions</Tabs.Tab>
              <Tabs.Tab id="inbox">Messages</Tabs.Tab>
            </Tabs.List>
            <Tabs.Panel id="all" className={"flex flex-col flex-1 overflow-hidden"}>
              <div className="divide-y *:py-3 flex flex-col overflow-hidden overflow-y-auto">
                {
                  user?.type === "artiste" ? user_notifications.map((notification, index)=><>
                  <CardNotification key={`notification-${index}`} {...notification}/>
                  </> ): org_notifications.map((notification, index)=><>
                  <CardNotification key={`notification-${index}`} {...notification}/>
                  </>)
                }
              </div>
            </Tabs.Panel>
            <Tabs.Panel id="proposition">
              "dsd"
            </Tabs.Panel>
            <Tabs.Panel id="inbox">
              "skld"
            </Tabs.Panel>
          </Tabs>
        </Popover.Body>
        <Popover.Footer className='h-max'>

        </Popover.Footer>
      </Popover.Content>
    </Popover>
  )
}