"use client"

import { Bookmark, Building2, Calendar, MapPin, Tag } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { Button } from "../ui/button"

import { Badge } from "../ui/badge"
import { cn } from "@/lib/utils"
import { Opportunity } from "@/types"


export const OpportunityCard = (job: Opportunity & { className?: string, titleClass?: string }) => {
    return (
        <div className={`${cn("flex relative flex-col border border-border/70 bg-bg rounded-xl isolate overflow-hidden p-0.5", job.className)}`}>
            <Image src={job.coverImage} width={1300} height={800} alt={`Image cover ${job.jobTitle}`} className="w-full aspect-[3/2.3] rounded-xl object-cover pointer-events-none" />
            <span className="text-sm flex items-center absolute top-2 left-2 bg-primary text-fg-primary-btn px-1 py-0.5 rounded-md">
                <Calendar strokeWidth={1.2} className="size-4 mr-0.5" />
                {job.datePublished}
            </span>
            <div className="p-5 flex flex-col pointer-events-none">
                <div className="flex items-center gap-2 justify-between pointer-events-none">

                    <Badge intent="none" className="bg-primary text-fg-primary-btn rounded-md w-max">
                        <Tag strokeWidth={1.2} className='size-4' />
                        {job.genre}
                    </Badge>
                </div>
                <h3 className={cn("font-semibold text-fg-title mt-2 pointer-events-none", job.titleClass)}>
                    {job.jobTitle}
                </h3>
                <div className="mt-3 flex flex-col gap-2 pointer-events-none">
                    <p className="line-clamp-1 text-sm text-fg-muted">
                        {job.description}
                    </p>
                    <div className="flex flex-wrap gap-3 items-center">
                        <span className="text-sm text-fg-muted flex items-center">
                            <MapPin strokeWidth={1.2} className="size-4 mr-1" />
                            {job.location.city}
                        </span>
                        <span className="size-1 rounded-full flex bg-fg-muted/50"></span>
                        <span className="text-sm text-fg-muted flex items-center">
                            <Building2 strokeWidth={1.2} className="size-4 mr-1" />
                            {job.organization.name}
                        </span>
                    </div>
                </div>
            </div>

            <div className="flex justify-end items-center gap-2 px-3.5 pb-3.5 mt-auto">
                <Button intent="outline" size="square-petite" className={"relative z-[2]"}>
                    <Bookmark strokeWidth={1.2} stroke='currentColor' className='size-4' />
                </Button>
                <Link className="before:absolute before:inset-0 flex-1" href={`/opportunites/${job.slug}`}>
                    <Button intent="white/dark" size="small" className={"flex-1 justify-center w-full"}>
                        Voir les détails
                    </Button>
                </Link>
            </div>
        </div>
    )
}
