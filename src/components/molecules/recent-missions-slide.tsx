"use client"

import { OpportunityCard } from "@/components/molecules/opportunite-card"
import { opportunites } from "@/data/opportunites"
import { cn } from "@/lib/utils"
import { Swiper, SwiperSlide } from "swiper/react"

export const RecentMissionsSlide = ({ className }: { className?: string }) => {
    return (
        <>
            <Swiper
                slidesPerView={'auto'}
                spaceBetween={0}
                className={cn("mySwiper mt-7 w-full", className)}
            >
                {
                    opportunites.slice(0, 8).map(job => <SwiperSlide className="w-[320px] max-sm:max-w-[87%] sm:w-[47%] md:w-[40%] sm:max-w-[47%] md:max-w-[40%] lg:w-1/4 lg:max-w-[24%] mr-3.5" key={job.id}>
                        <OpportunityCard className="flex-1 w-full h-full" titleClass="line-clamp-1" key={job.id} {...job} />
                    </SwiperSlide>)
                }
            </Swiper>
        </>
    )
}
