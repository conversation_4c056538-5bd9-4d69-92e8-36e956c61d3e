"use client"


import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useState, useRef, useCallback, useEffect, ReactNode } from "react";

type scrollboxType = {
    scrollLeft: number,
    scrollWidth: number,
    offsetWidth: number
}

export function ScrollableUlBox(
    { children, className='',hideControlOnSmall }: { children: ReactNode, className?:string, hideControlOnSmall?:boolean }
) {
    const [scrollboxPros, setScrollboxProps] = useState<scrollboxType>({
        scrollLeft: 0,
        scrollWidth: 0,
        offsetWidth: 0,
    })

    const boxScrollable = useRef<HTMLUListElement>(null)

    const initScroll = useCallback(() => {
        if (boxScrollable.current !== null) {
            setScrollboxProps({
                scrollLeft: boxScrollable.current?.scrollLeft,
                scrollWidth: boxScrollable.current?.scrollWidth,
                offsetWidth: boxScrollable.current?.offsetWidth
            })
        }
    }, []);

    const handleScrollLeft = useCallback(() => {
        if (boxScrollable.current !== null) {
            boxScrollable.current.scrollLeft -= boxScrollable.current.clientWidth;
        }
    }, []);

    const handleScrollRight = useCallback(() => {
        if (boxScrollable.current !== null) {
            boxScrollable.current.scrollLeft += boxScrollable.current.clientWidth;
        }
    }, [])

    useEffect(() => {
        initScroll();
        if (boxScrollable.current !== undefined && boxScrollable.current !== null) {
            const observer = new MutationObserver((mutations) => {
                if (mutations.some((mutation) => mutation.type === 'childList')) {
                    initScroll();
                }
            });

            observer.observe(boxScrollable.current, { childList: true });

            return () => observer.disconnect();
        }
    }, [boxScrollable, initScroll]);


    return (
        <nav className={`${cn("relative overflow-hidden", className)}`}>
            <button onClick={e => {
                e.preventDefault()
                handleScrollLeft()
            }} data-state={scrollboxPros.scrollLeft <= 0 ? "invisible" : "visible"} disabled={scrollboxPros.scrollLeft <= 0} aria-label="control-navigation prev" className={`absolute top-1/2 left-0 -translate-y-1/2 data-[state=visible]:opacity-100 data-[state=visible]:visible opacity-0 invisible border border-border bg-bg hover:bg-bg-surface size-9 flex items-center justify-center rounded-xl text-fg ${hideControlOnSmall ? " max-sm:hidden max-sm:invisible" :""}`}>
                <ChevronLeft className="size-4" strokeWidth={1.2} />
            </button>
            <button onClick={e => {
                e.preventDefault()
                handleScrollRight()
            }} data-state={scrollboxPros.scrollLeft >= scrollboxPros.scrollWidth - scrollboxPros.offsetWidth - 1 ? "invisible" : "visible"} disabled={scrollboxPros.scrollLeft >= scrollboxPros.scrollWidth - scrollboxPros.offsetWidth - 1} aria-label="control-navigation next" className={`absolute top-1/2 right-4 -translate-y-1/2 data-[state=visible]:opacity-100 data-[state=visible]:visible opacity-0 invisible border border-border bg-bg hover:bg-bg-surface size-9 flex items-center justify-center rounded-xl text-fg ${hideControlOnSmall ? " max-sm:hidden max-sm:invisible" :""}`}>
                <ChevronRight className="size-4" strokeWidth={1.2} />
            </button>
            <ul onScroll={e => {
                e.preventDefault()
                initScroll()
            }} ref={boxScrollable} className={`text-sm h-full flex items-center gap-3 flex-1 overflow-hidden overflow-x-auto *:min-w-max scrollbox text-fg-muted`}>
                {children}
            </ul>
        </nav>
    )
}