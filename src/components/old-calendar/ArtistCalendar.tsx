"use client"

import React, { useState } from 'react';
import CalendarHeader from './CalendarHeader';
import CalendarGrid from './CalendarGrid';
import CalendarLegend from './CalendarLegend';
import { DateEntry } from '@/types/calendar';
import { generateMonthData } from '@/utils/calendarUtils';
import { Button } from '../ui/button';

interface ArtistCalendarProps {
  dateEntries: DateEntry[];
  onDateUpdate?: (entry: DateEntry) => void;
  showLegend?: boolean,
  withActionRsv?: boolean,
  isArtiste?: boolean,
  isOwnProfile?: boolean
}

const ArtistCalendar: React.FC<ArtistCalendarProps> = ({ dateEntries, showLegend = true, withActionRsv = false }) => {
  const today = new Date();
  const [currentMonth, setCurrentMonth] = useState(today.getMonth());
  const [currentYear, setCurrentYear] = useState(today.getFullYear());

  const monthData = generateMonthData(currentYear, currentMonth, dateEntries);

  const navigateToPreviousMonth = () => {
    setCurrentMonth(prevMonth => {
      if (prevMonth === 0) {
        setCurrentYear(prevYear => prevYear - 1);
        return 11;
      }
      return prevMonth - 1;
    });
  };

  const setNewMonth = (month: number) => {

  }

  const setNewYear = (year: number) => {
    setCurrentYear(year);
  }

  const navigateToNextMonth = () => {
    setCurrentMonth(prevMonth => {
      if (prevMonth === 11) {
        setCurrentYear(prevYear => prevYear + 1);
        return 0;
      }
      return prevMonth + 1;
    });
  };

  const navigateToToday = () => {
    setCurrentMonth(today.getMonth());
    setCurrentYear(today.getFullYear());
  };

  const getDayStatusClass = (status: string): string => {
    switch (status) {
      case 'available':
        return 'bg-bg-success-soft opacity-80 hover:opacity-100';
      case 'unavailable':
        return 'bg-bg-gray-soft opacity-80 hover:opacity-100';
      case 'booked':
        return 'bg-bg-warning-soft opacity-80 hover:opacity-100';
      default:
        return 'bg-white';
    }
  };

  const getDayStatusText = (status: string): string => {
    switch (status) {
      case 'available':
        return 'Disponible';
      case 'unavailable':
        return 'Indisponible';
      case 'booked':
        return 'Deja pris';
      default:
        return '';
    }
  };

  return (
    <div className="rounded-xl bg-bg border border-border/60 overflow-hidden h-max p-2">
      <CalendarHeader
        onChangeYear={setNewYear}
        onChangeMonth={setNewMonth}
        currentMonth={currentMonth}
        currentYear={currentYear}
        onPrevMonth={navigateToPreviousMonth}
        onNextMonth={navigateToNextMonth}
        onToday={navigateToToday}
      />
      <CalendarGrid
        days={monthData.days}
        getDayStatusClass={getDayStatusClass}
        getDayStatusText={getDayStatusText}
      />
      {
        showLegend ? (
          <CalendarLegend getDayStatusText={getDayStatusText} />
        ) : null
      }

      {
        withActionRsv ? (
          <div className="flex justify-end px-4 py-2">
            <Button>
              cc
            </Button>
          </div>
        ) : null
      }
    </div>
  );
};

export default ArtistCalendar;