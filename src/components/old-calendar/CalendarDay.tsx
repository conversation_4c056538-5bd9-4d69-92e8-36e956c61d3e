"use client"

import React from 'react';
import { DayInfo } from '@/types/calendar';
import { formatDate } from '@/utils/calendarUtils';
import { Tooltip } from '@/components/ui/tooltip';
import { Button } from '../ui/button';
import { Modal } from '../ui/modal';


const getStatusDot = (status: string) => {
  switch (status) {
    case 'available':
      return 'bg-success text-success';
    case 'unavailable':
      return 'bg-gray-400/60 text-fg';
    case 'booked':
      return 'bg-warning text-warning';
    default:
      return 'bg-fg-muted';
  }
};

interface CalendarDayProps {
  day: DayInfo;
  getDayStatusClass: (status: string) => string;
  getDayStatusText: (status: string) => string;
}

const CalendarDay: React.FC<CalendarDayProps> = ({ day, getDayStatusClass, getDayStatusText }) => {
  const dayClasses = `
    
    ${getDayStatusClass(day.status)}
   
  `;

  return (
    <>

      {day.isCurrentMonth ? (
        <Tooltip>
          {
            day.status === "available" ? (
              <>
                <Modal>
                  <Button intent="none" size='none' className={"cursor-pointer"}>
                    <div className={`h-12 p-1 relative text-fg-muted rounded-md  ${!day.isCurrentMonth ? 'opacity-40' : ''} ${day.isToday ? 'ring-2 ring-primary' : ''}`}>
                      <span className="block text-right text-sm font-medium p-1">
                        {day.date.getDate()}
                      </span>

                      <div className="absolute bottom-0.5 left-1">
                        <span
                          className={`
                inline-block size-1.5 opacity-70 rounded-full
               ${getStatusDot(day.status)}
              `}
                        ></span>
                      </div>
                    </div>
                  </Button>
                </Modal>
              </>
            ) : (
              <div className={`h-12 bg-bg-surface/40 p-1 relative text-fg-muted rounded-md  ${!day.isCurrentMonth ? 'opacity-40' : ''}
    ${day.isToday ? 'ring-2 ring-primary' : ''}`}>
                <span className="block text-right text-sm font-medium p-1">
                  {day.date.getDate()}
                </span>

                <div className="absolute bottom-0.5 left-1">
                  <span
                    className={`
                inline-block size-1.5 opacity-70 rounded-full
                ${getStatusDot(day.status)}
              `}
                  ></span>
                </div>
              </div>
            )
          }

          <Tooltip.Content placement="bottom">
            <div className="w-48 text-sm text-fg">
              <p className="font-medium">{formatDate(day.date)}</p>
              <p className="mt-1">
                Status: <span className={`
                font-semibold
                ${day.status === 'available' ? 'text-success' : ''}
                ${day.status === 'unavailable' ? 'text-fg-muted' : ''}
                ${day.status === 'booked' ? 'text-warning' : ''}
              `}>
                  {getDayStatusText(day.status)}
                </span>
              </p>
              {day.notes && <p className="mt-1">{day.notes}</p>}
            </div>
          </Tooltip.Content>
        </Tooltip>
      ) : <div className={`h-12 p-1 bg-bg-surface/70 relative text-fg-muted rounded-md  ${!day.isCurrentMonth ? 'opacity-40' : ''}
      ${day.isToday ? 'ring-2 ring-primary' : ''}`}>
        <span className="block text-right text-sm font-medium p-1">
          {day.date.getDate()}
        </span>

        <div className="absolute bottom-0.5 left-1">
          <span
            className={`
                  inline-block size-1.5 opacity-70 rounded-full
                  ${getStatusDot(day.status)}
                `}
          ></span>
        </div>
      </div>}
    </>
  );
};

export default CalendarDay;