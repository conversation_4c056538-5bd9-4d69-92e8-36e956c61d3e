"use client"

import React from 'react';
import { DAYS_OF_WEEK } from '@/utils/calendarUtils';
import CalendarDay from '../old-calendar/CalendarDay';
import { DayInfo } from '@/types/calendar';

interface CalendarGridProps {
  days: DayInfo[];
  getDayStatusClass: (status: string) => string;
  getDayStatusText: (status: string) => string;
}

const CalendarGrid: React.FC<CalendarGridProps> = ({ days, getDayStatusClass, getDayStatusText }) => {
  return (
    <div className="px-2 pb-4">
      <div className="grid grid-cols-7 mb-1 mt-4">
        {DAYS_OF_WEEK.map((day, index) => (
          <div 
            key={day} 
            className={`text-xs font-medium text-fg-muted h-8 flex items-center justify-center ${
              index === 0 || index === 6 ? 'text-gray-400' : ''
            }`}
          >
            {day}
          </div>
        ))}
      </div>
      
      <div className="grid grid-cols-7 gap-0.5">
        {days.map((day, index) => (
          <CalendarDay
            key={`${day.date.toISOString()}-${index}`}
            day={day}
            getDayStatusClass={getDayStatusClass}
            getDayStatusText={getDayStatusText}
          />
        ))}
      </div>
    </div>
  );
};

export default CalendarGrid;