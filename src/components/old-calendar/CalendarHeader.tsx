"use client"


import { getMonthName } from '@/utils/calendarUtils';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '../ui/button';

interface CalendarHeaderProps {
  currentMonth: number;
  currentYear: number;
  onPrevMonth: () => void;
  onNextMonth: () => void;
  onToday: () => void;
  onChangeYear: (year: number) => void,
  onChangeMonth: (month: number) => void,
}

const CalendarHeader = ({
  currentMonth,
  currentYear,
  onPrevMonth,
  onNextMonth,
}:CalendarHeaderProps) => {
  return (
    <div className="px-2 pt-2 bg-bg ">
      <div className="flex justify-between items-center">
        <h2 className="text-lg capitalize font-semibold text-fg-title">
          {getMonthName(currentMonth)} {currentYear}
        </h2>
        <div className="flex border rounded-xl p-0.5 gap-px overflow-hidden">
          <Button onPress={() => onPrevMonth()} className={"size-8"} size="square-petite" intent="plain">
            <ChevronLeft size={16} className="mr-1" />
          </Button>
          <Button onPress={() => onNextMonth()} className={"size-8"} size="square-petite" intent="plain">
            <ChevronRight size={16} className="ml-1" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CalendarHeader;