
interface CalendarLegendProps {
  getDayStatusText: (status: string) => string;
}
const statuses = ['available', 'unavailable', 'booked'] as const;

const CalendarLegend = ({ getDayStatusText }:CalendarLegendProps) => {
  return (
    <div className="px-2 pb-2">
      <div className="flex flex-wrap gap-4">
        {statuses.map(status => (
          <div key={status} className="flex items-center">
            <span
              className={`
                inline-block size-2.5 mr-1
                ${status === 'available' ? 'bg-success' : ''}
                ${status === 'unavailable' ? 'bg-gray-500' : ''}
                ${status === 'booked' ? 'bg-warning' : ''}
              `}
            ></span>
            <span className="text-sm text-fg-muted">{getDayStatusText(status)}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CalendarLegend;