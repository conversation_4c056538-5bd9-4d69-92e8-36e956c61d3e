"use client"

import { CalendarProvider } from "@/context/CalendarContext"
import ArtistCalendar from "./ArtistCalendar"
import { DateEntry } from "@/types/calendar";


// Sample date entries
const sampleDateEntries: DateEntry[] = [
    {
        date: '2025-03-15',
        status: 'available',
        notes: 'Available for evening performance'
    },
    {
        date: '2025-03-20',
        status: 'booked',
        notes: 'Workshop at City Dance Studio'
    },
    {
        date: '2025-04-25',
        status: 'unavailable',
        notes: 'Personal commitment'
    },
    {
        date: '2025-05-24',
        status: 'booked',
        notes: 'Workshop at City Dance Studio'
    },
    {
        date: '2025-05-20',
        status: 'available',
        notes: 'Workshop at City Dance Studio'
    },
    {
        date: '2025-06-25',
        status: 'unavailable',
        notes: 'Personal commitment'
    }
];
export default function ViewArtisteCalendar() {
    return (
        <>
            <CalendarProvider>
                <ArtistCalendar dateEntries={sampleDateEntries}
                    onDateUpdate={() => {

                    }} />
            </CalendarProvider>
        </>
    )
}
