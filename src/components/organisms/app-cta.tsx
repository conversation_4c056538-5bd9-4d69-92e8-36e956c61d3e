"use client"
import { drukCondFont } from "@/app/fonts"
import { AnimatePresence, motion } from "motion/react"
import Link from "next/link"
import { buttonStyles } from "../ui/button"

export const AppCta = () => {
    return (
        <>
            <AnimatePresence>
                <section className="mt-4 border-y">
                    <div className="app-container px-4 sm:px-6 lg:px-4 relative py-24">
                        <motion.div initial={{ opacity: .7, filter: "blur(10px)" }}
                            animate={{ opacity: 1, filter: "blur(0px)" }}
                            transition={{ duration: 0.4 }} className="inset-y-0 inset-x-4 md:inset-x-20 xl:inset-x-32 absolute border-x border-border/40 isolate">
                            <div className="absolute opacity-30 dark:opacity-70 [--grid-color:var(--color-bg-surface-muted)] dark:[--grid-color:var(--color-bg-surface)] inset-0 -z-10 h-full w-full bg-bg/40 bg-[linear-gradient(to_right,var(--grid-color)_1px,transparent_1px),linear-gradient(to_bottom,var(--grid-color)_1px,transparent_1px)] bg-[size:6rem_4rem]"></div>
                        </motion.div>
                        <div className="flex flex-col justify-center text-center relative">
                            <motion.h1
                                className={`${drukCondFont.className} leading-[0.8] text-[4.5rem] sm:text-[6rem] md:text-[9rem] text-transparent bg-clip-text bg-gradient-to-bl from-fg-title to-primary-900 dark:to-primary-50 mb-4 mx-auto max-w-5xl`}
                                initial={{ opacity: .01, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.1 }}
                            >
                                VOTRE SCENE, NOTRE COMMUNAUTE
                            </motion.h1>
                            <motion.p
                                className="text-sm md:text-base text-fg-muted mb-10 max-w-lg mx-auto text-balance"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                            >
                                Connectez-vous aux meilleurs talents et opportunités de la scène culturelle.
                            </motion.p>

                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.5 }}
                                className="flex flex-wrap items-center justify-center gap-2.5">
                                <div className="border w-max rounded-[calc(var(--radius-xl)+2px)] p-0.5">
                                    <Link href={""} className={buttonStyles({ className: "rounded-xl" })}>
                                        Je suis un artiste
                                    </Link>
                                </div>
                                <div className="border w-max rounded-[calc(var(--radius-xl)+2px)] p-0.5">
                                    <Link href={""} className={buttonStyles({ intent: "outline", className: "rounded-xl" })}>
                                        Je suis un organisme
                                    </Link>
                                </div>
                            </motion.div>

                        </div>
                    </div>
                </section>
            </AnimatePresence>
        </>
    )
}
