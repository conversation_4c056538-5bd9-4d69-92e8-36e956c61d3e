"use client"
import React from 'react'
import { VideoPlay } from '../atoms/video-play'
import { ArrowRight, Calendar, FileText, Star, Users } from 'lucide-react'
import { motion } from 'motion/react'
import Link from 'next/link'
import { buttonStyles } from '../ui/button'
import Image from 'next/image'



const features = [
    {
        icon: Users,
        text: "Accès à plus de 10 000 artistes professionnels",
    },
    {
        icon: Calendar,
        text: "Organisation simplifiée des auditions et événements",
    },
    {
        icon: FileText,
        text: "Contrats intelligents avec signatures numériques",
    },
    {
        icon: Star,
        text: "Système d'évaluation et de recommandation",
    },
]
export const CtaOrg = () => {
    return (
        <section className="app-container px-4 sm:px-6 lg:px-4 mt-20">
            <div className='relative rounded-xl overflow-hidden'>
                <div className="absolute inset-0 before:absolute before:flex before:inset-y-0 before:w-3/5 before:bg-gradient-to-br before:from-gray-800 before:via-30% before:via-transparent before:backdrop-blur-xs">
                    <Image src="/recruiter.webp" alt="side-view-illustrator-drawing-tablet" width={3886} height={2596} className="size-full object-cover rounded-xl"/>
                </div>

                <div className="relative p-5 sm:p-12 md:p-20">
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className="space-y-4 max-w-xl"
                    >
                        <h2 className="text-4xl md:text-5xl font-semibold text-white tracking-tight">
                            Trouvez les talents qui feront briller
                        </h2>
                        <p className="mt-6 text-sm md:text-base text-gray-50">
                            Simplifiez votre processus de recrutement artistique. De la recherche de talents à la signature de contrats, tout en un seul endroit.
                        </p>
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.3, duration: 0.6 }}
                        className="space-y-2 mt-7 max-w-2xl"
                    >
                        {features.map((feature, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.4 + index * 0.1, duration: 0.5 }}
                                className="flex items-center gap-3"
                            >
                                <div className="flex items-center justify-center size-8 rounded-xl bg-primary/10">
                                    <feature.icon className="h-4 w-4 text-primary" />
                                </div>
                                <span className="text-white">{feature.text}</span>
                            </motion.div>
                        ))}
                    </motion.div>
                    <div className="mt-10 grid sm:grid-cols-2 lg:grid-cols-3 gap-3">
                        <motion.div
                            initial={{ opacity: 0, x: -20, y: -6 }}
                            animate={{ opacity: 1, x: 0, y: 0 }}
                            transition={{ delay: 0.4 + 0 * 0.1, duration: 0.5 }}
                            className="w-full flex">
                            <Link href={"/"} className={buttonStyles({ size: "none", className: "w-full p-4 flex-col justify-between items-start" })}>
                                <span className="text-xl sm:text-2xl font-semibold">
                                    Inscrivez votre organisme
                                </span>
                                <span className="text-sm opacity-70 mt-2 flex items-center">
                                    Inscrivez votre organisation
                                    <ArrowRight strokeWidth={1.2} className="size-4 ml-2" />
                                </span>
                            </Link>
                        </motion.div>
                        <motion.div
                            initial={{ opacity: 0, x: -20, y: -6 }}
                            animate={{ opacity: 1, x: 0, y: 0 }}
                            transition={{ delay: 0.4 + 1 * 0.1, duration: 0.5 }}
                            className="w-full flex">
                            <Link href={"/"} className={buttonStyles({ size: "none", className: "w-full p-4 flex-col justify-between items-start" })}>
                                <span className="text-xl sm:text-2xl font-semibold">
                                    Publiez des offres et recrutez des talents
                                </span>
                                <span className="text-sm opacity-70 mt-2 flex items-center">
                                    Publier une offre
                                    <ArrowRight strokeWidth={1.2} className="size-4 ml-2" />
                                </span>
                            </Link>
                        </motion.div>
                        <motion.div
                            initial={{ opacity: 0, x: -20, y: -6 }}
                            animate={{ opacity: 1, x: 0, y: 0 }}
                            transition={{ delay: 0.4 + 2 * 0.1, duration: 0.5 }}
                            className="w-full flex">
                            <Link href={"/artistes"} className={buttonStyles({ size: "none", className: "w-full p-4 flex-col justify-between items-start" })}>
                                <span className="text-xl sm:text-2xl font-semibold">
                                    Explorez et trouvez des artistes
                                </span>
                                <span className="text-sm opacity-70 mt-2 flex items-center">
                                    Explorer
                                    <ArrowRight strokeWidth={1.2} className="size-4 ml-2" />
                                </span>
                            </Link>
                        </motion.div>
                    </div>
                </div>
            </div>
        </section>
    )
}
