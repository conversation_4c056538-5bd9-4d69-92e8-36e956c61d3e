"use client"


import { Menu } from "../ui/menu"
import { Sheet } from "../ui/sheet"
import { Menu as MenuPrimitive } from "react-aria-components"
import { Button, buttonStyles } from "../ui/button"
import {
    Bitcoin, BookUser, ChartNoAxesColumn, Home, Info, MenuIcon, UserSearch, BrickWallFire,
    MessagesSquare, BookMarked, BuildingIcon,
    LogOut
} from "lucide-react"
import Link from "next/link"


const navigationItems = [
    { id: 'accueil', text: 'Accueil', icon: Home, href: '/' },
    { id: 'artistes', text: 'Artistes', icon: UserSearch, href: '/artistes' },
    { id: 'opportunites', text: 'Opportunités', icon: BrickWallFire, href: '/opportunites' },
    { id: 'leapscroll', text: 'Leapscroll', icon: BookMarked, href: '/leapscroll' },
    { id: 'organismes', text: 'Organismes', icon: BuildingIcon, href: '/organismes' },
    { id: 'a-propos', text: 'A propos', icon: Info, href: '/a-propos' },
    { id: 'tarifs', text: 'Tarifs', icon: Bitcoin, href: '/tarifications' },
    { id: 'contact', text: 'Contact', icon: BookUser, href: '/contact' },
    { id: 'mentions-legales', text: 'Mentions Légales', icon: ChartNoAxesColumn, href: '/mentions-legales' },
]

export const GlobalNavigationMobile = () => {
    return (
        <>
            <Sheet>
                <Sheet.Trigger aria-label="Open menu" as-child>
                    <Button intent="none" size="square-petite" className={"size-max outline-none border-r border-border pr-3 relative py-3"}>
                        <span aria-hidden="true" className={`flex h-0.5 w-6 rounded bg-fg transition duration-300`} />
                        <span aria-hidden="true" className={`flex mt-2 h-0.5 w-6 rounded bg-fg transition duration-300`} />
                    </Button>
                </Sheet.Trigger>
                <Sheet.Content side="left" isFloat={false} closeButton={false}>
                    <Sheet.Header className="flex flex-row gap-x-3.5 border-b sm:gap-x-3 sm:px-4 sm:pt-3 sm:pb-2">
                        <div>
                            <Sheet.Title className="text-base/4 sm:text-base/4">Leap</Sheet.Title>
                            {/* <Sheet.Description>@cobain</Sheet.Description> */}
                        </div>
                    </Sheet.Header>
                    <Sheet.Body className="px-0 sm:px-0">
                        <MenuPrimitive className="divide-y *:[[role=group]]:p-2">
                            <Menu.Section>
                                {navigationItems.map((item) => (
                                    <Menu.Item href={item.href} key={item.id}>
                                        {item.icon && <item.icon strokeWidth={1.2} className="size-5" />}
                                        <Menu.Label className="flex-1 ml-2 text-fg-muted">{item.text}</Menu.Label>
                                    </Menu.Item>
                                ))}
                            </Menu.Section>
                        </MenuPrimitive>
                    </Sheet.Body>
                    <Sheet.Footer className="border-t bg-muted/20 sm:p-4 grid sm:flex sm:flex-col">
                        <Link href="/messages" className={buttonStyles({ size: "small", className: "w-full", intent: "plain" })}>
                            <MessagesSquare strokeWidth={1.2} className="size-5 mr-2" />
                            <span className="flex-1 text-left text-fg-muted">Messages</span>
                        </Link>
                        <Button size="small" className="w-full" intent="plain">
                            <LogOut strokeWidth={1.2} className="size-5 mr-2" />
                            <span className="flex-1 text-left text-fg-muted">Se deconnecter</span>
                        </Button>
                    </Sheet.Footer>
                </Sheet.Content>
            </Sheet>
        </>
    )
}
