


import Link from 'next/link'


import { NavUserActions } from "../molecules/nav-user-actions"
import { NavItems } from "../molecules/nav-items"
import { Button } from '../ui/button'
import { Search } from 'lucide-react'
import Image from 'next/image'
import { Input } from '../ui/field'
import { GlobalNavigationMobile } from './global-navigation-mobile'



export function Navbar() {
    return (
        <>
            <header className={" h-16 pt-1 flex items-center fixed w-full top-0 px-1 z-40 bg-bg-light"}>
                <nav className="flex justify-between items-center h-full bg-bg border border-border/70 rounded-xl app-container px-3">
                    <div className="flex items-center gap-3">
                        <div className="flex lg:hidden">
                            <GlobalNavigationMobile />
                        </div>

                        <Link href={"/"} aria-label="Lien vers la page principale" className="flex items-center text-fg-title/80 w-max min-w-max">
                            <Image src="/logo.svg" alt="logo leap" width={400} height={400} className="size-8 lg:size-9 border-2 border-border rounded-xl" />
                            <svg width={767} height={401} className="h-5 sm:h-6 w-auto ml-2 hidden min-[360px]:flex" viewBox="0 0 767 401" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M175 237.33L173.28 400.31H0V396.88C13.16 396.88 23.45 386.01 23.45 372.86V27.45C23.45 14.3 13.16 3.42999 0 3.42999V0H99.51V3.42999C86.36 3.42999 76.06 14.3 76.06 27.45V392.3H108.09C157.27 392.3 168.13 237.33 175 237.33ZM354.56 237.33L352.85 400.31H179.57V396.88C192.72 396.88 203.02 386.01 203.02 372.86V27.45C203.02 14.3 192.72 3.42999 179.57 3.42999V0H347.13L348.84 162.98C341.98 162.98 331.12 8.00998 281.93 8.00998H255.63V124.1H315.67L320.82 239.61C313.39 239.61 310.53 132.1 255.63 132.1V392.3H287.65C336.83 392.3 347.7 237.33 354.56 237.33ZM564.44 400.31H488.95V396.88C494.67 396.88 500.39 394.02 503.82 389.44C507.82 385.44 508.97 379.15 508.39 373.43L487.24 256.77H405.46C396.88 306.52 400.31 357.42 426.05 400.31H366.57L434.05 23.45C435.2 18.3 434.05 13.73 430.62 9.72C427.76 6.29 423.19 4.00999 418.61 3.42999H418.04V0H494.1L564.44 400.31ZM446.06 30.88L407.17 245.91H485.52L446.06 30.88ZM766.31 97.22C766.31 174.99 724.56 208.73 688.53 208.73C668.52 208.73 653.08 200.73 644.5 186.43V372.86C644.5 386.01 654.79 396.88 667.95 396.88V400.31H568.44V396.88C581.59 396.88 591.89 386.01 591.89 372.86V27.45C591.89 14.3 581.59 3.42999 568.44 3.42999V0H675.38C745.15 0 766.31 54.33 766.31 97.22ZM714.27 97.22C714.27 58.33 702.83 8.00998 667.95 8.00998H644.5V154.98C644.5 203.59 714.27 203.59 714.27 97.22Z" fill="currentColor" />
                            </svg>
                        </Link>
                    </div>
                    <div className="hidden lg:flex flex-1 md:px-8 lg:px-10 ">
                        <NavItems />
                    </div>
                    <NavUserActions>
                        <Button intent='plain' size="square-petite" className={"md:hidden"}>
                            <Search strokeWidth={1.2} className="size-4" />
                        </Button>
                        <div className="w-full max-w-[10rem] mx-auto relative p-0 pr-2 rounded-xl bg-bg items-center focus-within:border-primary border shadow-sm focus-within:shadow-primary/20 hidden md:flex">
                            <Input placeholder="Rechercher..." className={"flex-1 flex h-9 ps-9"} />
                            <span className="mx-3 w-0.5 h-3/5 bg-bg-surface"></span>
                            <Search className="size-4 absolute left-3 top-2.5 text-fg-muted/70" />
                        </div>
                    </NavUserActions>
                </nav>
            </header>
            <div aria-hidden={true} className="flex h-16 w-full"></div>
        </>
    )
}
