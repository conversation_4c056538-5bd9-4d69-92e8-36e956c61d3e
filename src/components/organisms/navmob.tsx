"use client"

import { usePathname } from "next/navigation"
import Link from 'next/link'

import { <PERSON>lapperboard, BrickWallFire, MessagesSquare,  Telescope, UserSearch } from 'lucide-react'


const items = [
    { id: 1, name: "Explorer", href: "/explorer", icon: <Telescope strokeWidth={1.2} className="size-5" /> },
    { id: 4, name: "Leapscroll", href: "/leapscroll", icon: <Clapperboard strokeWidth={1.2} className="size-5" /> },
    { id: 2, name: "Artist<PERSON>", href: "/artistes", icon: <UserSearch strokeWidth={1.2} className="size-5" /> },
    { id: 3, name: "Opportunites", href: "/opportunites", icon: <BrickWallFire strokeWidth={1.2} className="size-5" /> },
    { id: 5, name: "Messages", href: "/messages", icon: <MessagesSquare strokeWidth={1.2} className="size-5" /> },
]

const pathToShowMobileTab = [
    "/explorer",
    "/opportunites",
    "/artistes",
    "/notifications",
    "/inbox",
    "/messages",
    "/leapscroll"
]

const shouldShowMobileTab = (pathname: string) => (
    pathToShowMobileTab.includes(pathname)
    || pathname.startsWith("/mon-espace")
    || pathname.startsWith("/mon-organisme")
) && !pathname.match(/^\/messages\/.*/)

export default function AppMobNavigation() {
    const pathname = usePathname()

    return (
        <>
            {
                shouldShowMobileTab(pathname) ? (
                    <header data-visibility="visible" className={"pb-1 flex items-center w-full fixed bottom-0 px-1 z-40 bg-bg-light lg:hidden"}>
                        <nav className="flex bg-bg border border-border/70 rounded-xl app-container px-2.5">
                            <ul className="grid grid-cols-5 gap-2 w-full">
                                {
                                    items.map(item => (
                                        <li key={item.id} className="text-center group overflow-hidden" data-state="">
                                            <Link href={item.href}
                                                data-state={pathname === item.href ? "active" : ""}
                                                className="flex flex-col items-center text-center gap-1 text-fg-muted hover:text-fg-title w-full py-2.5 border-t-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-fg-primary">
                                                {item.icon}
                                                <span className="text-xs sm:text-sm truncate w-full">{item.name}</span>
                                            </Link>
                                        </li>
                                    ))
                                }
                            </ul>
                        </nav>
                    </header>
                ) : null
            }
        </>
    )
}
