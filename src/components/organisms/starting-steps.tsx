import { SquareStack } from 'lucide-react'

export const GettingStartedSteps = () => {
    return (
        <section className="app-container px-4 sm:px-6 lg:px-4 mt-24 lg:my-32">
            <div className="flex flex-col max-w-5xl mx-auto">
                <h2 className="font-semibold text-transparent bg-clip-text bg-gradient-to-br from-fg-title to-fg-muted/70 text-2xl md:text-4xl lg:text-5xl max-w-xl">
                    Comment obtenir des opportunités sur Leap ?
                </h2>
                <p className="max-w-md text-sm mt-5">
                    Suivez ces étapes pour maximiser vos chances en tant qu'artiste sur notre plateforme.
                </p>
            </div>
            <div className="bg-bg border rounded-3xl mx-auto max-w-5xl p-1 mt-16">
                <div className="grid sm:grid-cols-2 lg:grid-cols-3 divide-x divide-y lg:divide-y-0 *:px-6 sm:*:px-8 *:py-5">
                    <div className="relative">
                        <div className="relative">
                            <span className="absolute right-0 text-4xl font-bold opacity-20 text-fg-muted">01</span>
                            <span className="p-2.5 rounded-xl bg-muted flex w-max border">
                                <SquareStack />
                            </span>
                        </div>
                        <h3 className="font-semibold md:text-lg text-fg-title mt-8">
                            Créez votre profil
                        </h3>
                        <p className='text-sm text-fg-muted mt-3'>
                            Inscrivez-vous et complétez votre profil avec vos informations, expériences et réalisations artistiques.
                        </p>
                    </div>
                    <div className="relative">
                        <div className="relative">
                            <span className="absolute right-0 text-4xl font-bold opacity-20 text-fg-muted">02</span>
                            <span className="p-2.5 rounded-xl bg-muted flex w-max border">
                                <SquareStack />
                            </span>
                        </div>
                        <h3 className="font-semibold md:text-lg text-fg-title mt-8">
                            Explorez les opportunités
                        </h3>
                        <p className='text-sm text-fg-muted mt-3'>
                            Parcourez les offres disponibles et trouvez celles qui correspondent à votre talent et à vos aspirations.
                        </p>
                    </div>
                    <div className="relative">
                        <div className="relative">
                            <span className="absolute right-0 text-4xl font-bold opacity-20 text-fg-muted">03</span>
                            <span className="p-2.5 rounded-xl bg-muted flex w-max border">
                                <SquareStack />
                            </span>
                        </div>
                        <h3 className="font-semibold md:text-lg text-fg-title mt-8">
                            Postulez et démarquez-vous
                        </h3>
                        <p className='text-sm text-fg-muted mt-3'>
                            Soumettez votre candidature, mettez en avant vos atouts et échangez avec les recruteurs pour saisir les meilleures opportunités.
                        </p>
                    </div>
                </div>
               
            </div>
        </section>
    )
}
