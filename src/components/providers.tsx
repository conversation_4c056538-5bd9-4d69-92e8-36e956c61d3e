'use client'

import { NuqsAdapter } from 'nuqs/adapters/next/app'

import { ThemeProvider } from './theme-provider'
import { useRouter } from 'next/navigation'
import { RouterProvider } from 'react-aria-components'
import { AuthProvider } from './AuthProvider'

declare module 'react-aria-components' {
  interface RouterConfig {
    routerOptions: NonNullable<Parameters<ReturnType<typeof useRouter>['push']>[1]>
  }
}

export function Providers({ children }: { children: React.ReactNode }) {
  const router = useRouter()

  return (
    <RouterProvider navigate={router.push}>
      <NuqsAdapter>
        <ThemeProvider disableTransitionOnChange enableSystem attribute="class">
          <AuthProvider>
            {children}
          </AuthProvider>
        </ThemeProvider>
      </NuqsAdapter>
    </RouterProvider>
  )
}
