"use client"
import { useState } from 'react';
import { Star, StarOff } from 'lucide-react';
import { cn } from '@/lib/utils';


interface StarRatingProps {
  /**
   * The current rating value (1-5)
   */
  value: number;
  /**
   * Number of stars to display
   */
  maxStars?: number;
  /**
   * Whether the rating can be changed by user interaction
   */
  interactive?: boolean;
  /**
   * Callback when rating changes (only used if interactive)
   */
  onChange?: (value: number) => void;
  /**
   * Additional classes to apply to the star container
   */
  className?: string;
  /**
   * Size of the stars in pixels
   */
  size?: number;
  /**
   * Precision of interactive ratings (e.g., 0.5 for half stars, 0.1 for decimal)
   */
  precision?: number;
}

export function StarRatingInForm({
  value,
  maxStars = 5,
  interactive = false,
  onChange,
  className,
  size = 24,
  precision = 0.5,
}: StarRatingProps) {
  const [hoverValue, setHoverValue] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  // Ensure value is within range
  const ratingValue = Math.max(0, Math.min(value, maxStars));

  // Calculate the width of each step (for precise ratings)
  const getStepWidth = () => {
    return 100 / maxStars / (1 / precision);
  };

  // Calculate the visual to display (filled, half, or empty)
  const getStarType = (starPosition: number) => {
    const displayValue = hoverValue !== null ? hoverValue : ratingValue;

    if (displayValue >= starPosition) {
      return 'filled'; // Completely filled star
    } else if (displayValue > starPosition - 1) {
      return 'half'; // Partially filled star
    }

    return 'empty'; // Empty star
  };

  // Handle user interactions
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>, index: number) => {
    if (!interactive) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const width = rect.width;
    const relativeX = e.clientX - rect.left;
    const percent = relativeX / width;

    // Calculate the value based on precision
    let newValue = index - 1 + percent;
    newValue = Math.ceil(newValue / precision) * precision;
    newValue = Math.max(precision, Math.min(newValue, index));

    setHoverValue(newValue);
  };

  const handleClick = () => {
    if (!interactive || hoverValue === null) return;
    onChange?.(hoverValue);
  };

  const handleTouchStart = () => {
    if (!interactive) return;
    setIsDragging(true);
  };

  const handleTouchEnd = () => {
    if (!interactive) return;
    setIsDragging(false);
    if (hoverValue !== null) {
      onChange?.(hoverValue);
    }
  };

  const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>, index: number) => {
    if (!interactive || !isDragging) return;

    const touch = e.touches[0];
    const rect = e.currentTarget.getBoundingClientRect();
    const width = rect.width;
    const relativeX = touch.clientX - rect.left;
    const percent = relativeX / width;

    let newValue = index - 1 + percent;
    newValue = Math.ceil(newValue / precision) * precision;
    newValue = Math.max(precision, Math.min(newValue, index));

    setHoverValue(newValue);
  };

  return (
    <div
      className={cn('flex items-center', className)}
      onMouseLeave={() => !isDragging && setHoverValue(null)}
    >
      {Array.from({ length: maxStars }).map((_, index) => {
        const starPosition = index + 1;
        const starType = getStarType(starPosition);

        return (
          <div
            key={index}
            className={cn(
              'relative cursor-default transition-transform duration-200',
              interactive && 'cursor-pointer hover:scale-110'
            )}
            onMouseMove={(e) => handleMouseMove(e, starPosition)}
            onMouseDown={() => interactive && setIsDragging(true)}
            onMouseUp={() => {
              if (interactive) {
                setIsDragging(false);
                handleClick();
              }
            }}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            onTouchMove={(e) => handleTouchMove(e, starPosition)}
          >
            {starType === 'filled' ? (
              <Star size={size} className="text-amber-400 fill-amber-400" />
            ) : starType === 'half' ? (
              <div className="relative">
                <StarOff size={size} className="text-gray-300" />
                <div
                  className="absolute inset-0 overflow-hidden"
                  style={{
                    width: `${((hoverValue !== null ? hoverValue : ratingValue) - (starPosition - 1)) * 100}%`
                  }}
                >
                  <Star size={size} className="text-amber-400 fill-amber-400" />
                </div>
              </div>
            ) : (
              <StarOff size={size} className="text-gray-300" />
            )}
          </div>
        );
      })}
      {interactive && (
        <span className="ml-2 text-sm font-medium text-gray-700">
          {hoverValue !== null ? hoverValue.toFixed(1) : ratingValue.toFixed(1)}
        </span>
      )}
    </div>
  );
}