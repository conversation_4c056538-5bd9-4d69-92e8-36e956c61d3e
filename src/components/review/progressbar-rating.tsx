import React from 'react'



interface ProgressBarProps {
    value: number;
    max: number;
    label?: string;
    count?: number;
    className?: string;
    showPercentage?: boolean;
}

export default function ProgressBarRate({
    value,
    max,
    label,
    count,
    className = '',
    showPercentage = false,
}: ProgressBarProps) {
    const percentage = (value / max) * 100;
    return (
        <div className={`flex items-center gap-2 ${className}`}>
            {label && <span className="text-sm w-16">{label}</span>}
            <div className="flex-1 h-2 bg-bg-surface rounded-full overflow-hidden">
                <div
                    className="h-full bg-primary rounded-full transition-all duration-500 ease-out"
                    style={{ width: `${percentage}%` }}
                />
            </div>
            {count !== undefined && (
                <span className="text-sm text-gray-500 w-10 text-right">({count})</span>
            )}
            {showPercentage && (
                <span className="text-sm text-gray-500 ml-2">{percentage.toFixed(0)}%</span>
            )}
        </div>
    )
}
