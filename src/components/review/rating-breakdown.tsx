import ProgressBarRate from "./progressbar-rating";

interface RatingDistribution {
  label: string;
  count: number;
}

interface RatingBreakdownProps {
  distributions: RatingDistribution[];
  totalReviews: number;
  className?: string;
}

const RatingBreakdown: React.FC<RatingBreakdownProps> = ({
  distributions,
  totalReviews,
  className = '',
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {distributions.map((dist, index) => (
        <ProgressBarRate
          key={index}
          label={dist.label}
          value={dist.count}
          max={totalReviews}
          count={dist.count}
          className="group"
        />
      ))}
    </div>
  );
};

export default RatingBreakdown;