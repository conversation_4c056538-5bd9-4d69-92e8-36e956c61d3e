import React from 'react';
import StarRating from './star-rating';


interface RatingCategory {
  name: string;
  rating: number;
}

interface RatingCategoriesProps {
  categories: RatingCategory[];
  className?: string;
}

const RatingCategories: React.FC<RatingCategoriesProps> = ({
  categories,
  className = '',
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      {categories.map((category, index) => (
        <div key={index} className="flex justify-between items-center">
          <span className="text-sm text-fg">{category.name}</span>
          <div className="flex items-center gap-2">
            <StarRating rating={category.rating} size="sm" />
            <span className="text-sm font-medium">{category.rating.toFixed(1)}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default RatingCategories;