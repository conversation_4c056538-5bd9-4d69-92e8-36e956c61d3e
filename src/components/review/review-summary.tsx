"use client"

import React, { useState } from 'react';
import StarRating from './star-rating';
import RatingCategories from './rating-caterogies';
import RatingBreakdown from './rating-breakdown';
import { Modal } from '@/components/ui/modal';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { MessageSquarePlus, Star } from 'lucide-react';
import { Input, Label } from '@/components/ui/field';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { StarRatingInForm } from './StarRating';


interface ReviewSummaryProps {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: { label: string; count: number; }[];
  ratingCategories: { name: string; rating: number; }[];
  isOwnerOrArtiste?: boolean
}

interface RatingState {
  communication: number;
  quality: number;
  value: number;

}

const ReviewSummary: React.FC<ReviewSummaryProps> = ({
  totalReviews,
  averageRating,
  ratingDistribution,
  ratingCategories,
  isOwnerOrArtiste = false
}) => {
  const [ratings, setRatings] = useState<RatingState>({
    communication: 3.5,
    quality: 3.5,
    value: 3.5
  });

  const handleRatingChange = (type: keyof RatingState, value: number) => {
    setRatings(prev => ({
      ...prev,
      [type]: value
    }));
  };

  return (
    <div className={`bg-bg border border-border/60 rounded-xl p-5`}>
      <div className="flex flex-col md:flex-row md:items-start gap-8">
        {/* Overall Rating Section */}
        <div className="flex-1">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-fg-title">{totalReviews} Avis</h2>
            <div className="flex items-center gap-2">
              <StarRating rating={averageRating} size="sm" />
              <span className="text-lg font-bold">{averageRating.toFixed(1)}</span>
            </div>
          </div>

          {/* Rating Breakdown */}
          <RatingBreakdown
            distributions={ratingDistribution}
            totalReviews={totalReviews}
            className="mb-6"
          />
        </div>

        {/* Detailed Rating Categories */}
        <div className="md:w-80 border-t pt-6 md:pt-0 md:border-t-0 md:border-l md:pl-8">
          <h3 className="text-lg font-medium text-fg mb-4">Détails de la notation</h3>
          <RatingCategories categories={ratingCategories} />
        </div>
      </div>
      {
        isOwnerOrArtiste ? null
          :
          <div className="pt-5 flex">
            <Modal>
              <Button size="small">
                <MessageSquarePlus className="size-4" strokeWidth={1.2} />
                Ajouter un avis
              </Button>
              <Modal.Content classNames={{ overlay: "backdrop-blur-lg" }}>
                <Modal.Header className="bg-bg-surface/40">
                  <div className="flex items-center gap-4">
                    <span className="size-12 flex items-center justify-center bg-bg-surface border border-border rounded-xl">
                      <Star className="size-5" />
                    </span>
                    <div className="flex-1">
                      <Modal.Title className="text-xl font-semibold">
                        Évaluer la prestation
                      </Modal.Title>
                      <Modal.Description className="text-fg-muted">
                        Partagez votre expérience avec cet artiste.
                      </Modal.Description>
                    </div>
                  </div>
                </Modal.Header>
                <Form>
                  <Modal.Body className="py-4 space-y-4 flex flex-col">
                    <div className='space-y-1'>
                      <Label htmlFor='quote'>Citation</Label>
                      <Input type="text" id="quote" />
                    </div>
                    <div className='space-y-1'>
                      <Label htmlFor='content'>Contenu</Label>
                      <Textarea id='content' />
                    </div>

                    <div className="space-y-4">
                      <div>
                        <Label id="level_com">
                          Niveau de communication
                        </Label>
                        <div className="flex items-center gap-6 justify-between">
                          <Slider
                            className={"flex-1"}
                            minValue={0}
                            id='level_com'
                            maxValue={5}
                            step={0.1}
                            output="none"
                            value={[ratings.communication]}
                            onChange={(e) => handleRatingChange('communication', (e as number[])[0])}
                          />
                          <StarRatingInForm value={ratings.communication} size={16} />
                        </div>
                      </div>

                      <div>
                        <Label id="qualite_prest">
                          Qualité prestation
                        </Label>
                        <div className="flex items-center gap-6 justify-between">
                          <Slider
                            className={"flex-1"}
                            minValue={0}
                            id='qualite_prest'
                            maxValue={5}
                            step={0.1}
                            output="none"
                            value={[ratings.quality]}
                            onChange={(e) => handleRatingChange('quality', (e as number[])[0])}
                          />
                          <StarRatingInForm value={ratings.quality} size={16} />
                        </div>
                      </div>

                      <div>
                        <Label id="valeur_produit">
                          Valeur de la prestation
                        </Label>
                        <div className="flex items-center gap-6 justify-between">
                          <Slider
                            className={"flex-1"}
                            minValue={0}
                            id='valeur_produit'
                            maxValue={5}
                            step={0.1}
                            output="none"
                            value={[ratings.value]}
                            onChange={(e) => handleRatingChange('value', (e as number[])[0])}
                          />
                          <StarRatingInForm value={ratings.value} size={16} />
                        </div>
                      </div>
                    </div>
                  </Modal.Body>
                  <Modal.Footer className="flex justify-end pt-3 border-t border-border">
                    <Button>
                      Soumettre
                    </Button>
                  </Modal.Footer>
                </Form>
              </Modal.Content>
            </Modal>
          </div>
      }
    </div>
  );
};

export default ReviewSummary;