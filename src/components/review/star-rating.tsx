import React from 'react';
import { Star } from 'lucide-react';

interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  className?: string;
}

const StarRating: React.FC<StarRatingProps> = ({
  rating,
  maxRating = 5,
  size = 'md',
  showValue = false,
  className = '',
}) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  
  const starSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };
  
  const fontSize = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <div className="flex">
        {Array.from({ length: maxRating }).map((_, i) => {
          const isFilled = i < fullStars;
          const isHalf = !isFilled && i === fullStars && hasHalfStar;
          
          return (
            <Star
              key={i}
              className={`${starSizes[size]} ${
                isFilled || isHalf ? 'text-yellow-400 fill-yellow-400' : 'text-bg-surface'
              } ${isHalf ? 'fill-yellow-400 half' : ''}`}
              strokeWidth={1.5}
            />
          );
        })}
      </div>
      {showValue && (
        <span className={`font-semibold ${fontSize[size]} ml-1`}>
          {rating.toFixed(1)}
        </span>
      )}
    </div>
  );
};

export default StarRating;