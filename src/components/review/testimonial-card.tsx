
import Image from 'next/image';
import React from 'react';
import { StarRatingInForm } from './StarRating';

export interface TestimonialProps {
    quote: string;
    note: number;
    description: string;
    author: {
        name: string;
        title: string;
        image: string;
    };
}

export const TestimonialCard: React.FC<TestimonialProps> = ({
    quote,
    description,
    author,
    note
}) => {
    return (
        <div className="bg-bg border border-border/60 rounded-xl p-5 flex flex-col">
            <div className="mb-4 text-fg-muted/40 flex justify-between">
                <svg xmlns="http://www.w3.org/2000/svg" width={32} height={32} fill="currentColor" className="size-7" viewBox="0 0 256 256"><path d="M100,56H40A16,16,0,0,0,24,72v64a16,16,0,0,0,16,16h60v8a32,32,0,0,1-32,32,8,8,0,0,0,0,16,48.05,48.05,0,0,0,48-48V72A16,16,0,0,0,100,56Zm0,80H40V72h60ZM216,56H156a16,16,0,0,0-16,16v64a16,16,0,0,0,16,16h60v8a32,32,0,0,1-32,32,8,8,0,0,0,0,16,48.05,48.05,0,0,0,48-48V72A16,16,0,0,0,216,56Zm0,80H156V72h60Z" /></svg>
                <span className="flex items-center text-sm text-fg">
                    <StarRatingInForm value={note} size={14} /> ({note})
                </span>
            </div>
            <h3 className="font-medium text-fg-title pb-5 text-xl">
                {quote}
            </h3>
            <p className="mb-6 text-fg">
                {description}
            </p>
            <div className="border-t border-border mt-auto pt-4 flex items-center">
                <Image
                    src={author.image}
                    alt={author.name}
                    width={600}
                    height={600}
                    className="size-10 rounded-full mr-3"
                />
                <div>
                    <p className="font-medium text-fg-title">{author.name}</p>
                    <p className="text-sm text-fg">{author.title}</p>
                </div>
            </div>
        </div>
    );
};