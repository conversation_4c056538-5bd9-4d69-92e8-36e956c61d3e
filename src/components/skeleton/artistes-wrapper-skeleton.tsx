"use client"

import { Layers2, <PERSON><PERSON>ilter, Search, Tag } from "lucide-react"
import { TextField } from "../ui/text-field"
import { Button, buttonStyles } from "../ui/button"
import { ReactNode } from "react"


export const ArtistesWrapperSkeleton = ({ children }: { children: ReactNode }) => {
    return (
        <div className="w-full lg:app-container px-4 sm:px-6 lg:px-4">
            <div className="w-full h-14 border-b bg-bg/40 backdrop-blur-2xl pt-0.5 sticky top-16 flex items-center justify-between z-30">
                <div className="relative overflow-hidden flex w-max pr-2 justify-start sm:flex-1 md:hidden">
                    <span className={buttonStyles({ intent: "outline", size: "extra-small", className: "size-9 justify-center bg-bg-surface animate-pulse" })}>
                        <Tag strokeWidth={1.2} className="size-4" />
                    </span>
                </div>
                <div className="flex-1 pr-5 h-full hidden md:flex items-center">
                    <div className="flex items-center gap-2 h-full">
                        <div className="h-8 w-32 bg-bg-surface/70 animate-pulse rounded-xl flex items-center px-3">
                            <div className="size-4 bg-fg-muted/20 rounded mr-2"></div>
                            <div className="h-4 flex-1 bg-fg-muted/20 rounded"></div>
                        </div>
                        {[1,2,3,4].map(i => (
                            <div key={i} className="h-8 w-24 bg-bg-surface/70 animate-pulse rounded-xl flex items-center px-3">
                                <div className="size-1.5 rounded-full bg-fg-muted/20 mr-1.5"></div>
                                <div className="h-4 flex-1 bg-fg-muted/20 rounded"></div>
                            </div>
                        ))}
                    </div>
                </div>
                <div className="relative pr-2 flex max-sm:flex-1 sm:min-w-max">
                    <Search className="size-4 absolute left-3 top-2.5 text-fg-muted/60 pointer-events-none" strokeWidth={1.2} />
                    <div className="h-9 w-full min-w-[85px] rounded-md bg-bg-surface/70 animate-pulse"></div>
                </div>
                <div className="relative w-max min-w-max">
                    <div className="h-9 px-4 rounded-md bg-bg-surface/70 animate-pulse flex items-center justify-center">
                        <div className="size-4 bg-fg-muted/20 rounded mr-2"></div>
                        <div className="hidden sm:block w-12 h-4 bg-fg-muted/20 rounded"></div>
                    </div>
                </div>
            </div>
            <div className="flex items-center gap-4 justify-between pt-5">
                <div className="h-6 w-32 bg-bg-surface/70 animate-pulse rounded-lg"></div>
                <div className="flex gap-2">
                    <div className="h-8 w-24 bg-bg-surface/70 animate-pulse rounded-lg"></div>
                    <div className="h-8 w-24 bg-bg-surface/70 animate-pulse rounded-lg"></div>
                </div>
            </div>
            {children}
        </div>
    )
}
