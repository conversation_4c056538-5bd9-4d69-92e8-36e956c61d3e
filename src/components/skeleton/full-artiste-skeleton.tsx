import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { GetStartedChat } from '@/components/artiste/get-started-chat'
import { Bookmark, BrickWallFire, ImageIcon, MapPin, Star, Tag, Timer } from 'lucide-react'


export const SkeletonCardFullArtiste = () => {
    return (
        <article className="w-full bg-bg border border-border/70 p-5 sm:p-6 rounded-xl flex flex-col relative isolate z-[5] overflow-hidden">
            <div className="pointer-events-none flex items-start gap-5">
                <div className="size-12 sm:size-14 bg-bg-surface/50 rounded-full animate-pulse">
                </div>
                <div className="flex-1 flex flex-col">
                    <div className="flex items-center flex-wrap gap-x-5 gap-y-2">
                        <h2 className="font-semibold sm:text-xl text-transparent bg-muted/50 rounded-md animate-pulse w-32">
                            <PERSON>
                        </h2>
                        <div className='text-sm text-fg-muted flex items-center gap-3.5'>
                            <span className="flex items-center">
                                <Star strokeWidth={0} className="size-4 fill-amber-500 mr-0.5" />
                                <span className='text-transparent bg-muted/50 rounded-md animate-pulse w-4'>4</span>
                            </span>
                            <span className='flex items-center'>
                                <BrickWallFire strokeWidth={1.2} className='size-4 mr-0.5' />
                                <span className='text-transparent bg-muted/50 rounded-md animate-pulse w-20'>3Prestations</span>
                            </span>
                        </div>
                    </div>
                    <div className="mt-1.4">
                        <span className="text-sm text-transparent bg-muted/50 rounded-md animate-pulse w-48">
                            Hekki ddksdks dskl
                        </span>
                    </div>

                </div>
            </div>
            <div className="relative mt-5 z-[4] w-full flex md:grid md:grid-cols-4 gap-1 sm:gap-2 overflow-x-auto md:overflow-x-hidden scroll-hidden">
                {
                    [1,2,3,4].map((_, index)=><div
                        key={`placeholder-${index}`}
                        className="relative flex items-center justify-center bg-bg-surface/30 border border-dashed animate-pulse w-[45%] sm:w-[38%] min-w-[45%] sm:min-w-[38%] md:min-w-0 md:w-auto aspect-[4/5] overflow-hidden rounded-md"
                    >
                        <span className='absolute w-3/5 aspect-square top-1/2 left-1/2 -translate-1/2 border border-dashed border-gray-400/60 rotate-12 flex' />
                        <ImageIcon strokeWidth={1.2} className="size-10 text-fg-muted/70" />
                    </div>)
                }
            </div>
           
            <div className="pointer-events-none mt-4 flex flex-col flex-1 justify-start">
                <div className="flex items-center gap-3 flex-wrap">
                    <span className='text-sm text-fg-muted flex items-center'>
                        <MapPin className='size-4 mr-0.5' strokeWidth={1.2} />
                        <span className='text-transparent bg-muted/50 rounded-md animate-pulse w-20'>Bruxelles</span>
                    </span>

                    <span className='text-sm text-fg-muted flex items-center'>
                        <Timer className='size-4 mr-0.5' strokeWidth={1.2} />
                       <span className='text-transparent bg-muted/50 rounded-md animate-pulse w-24'>Réponse rapide</span>
                    </span>
                    <Badge intent="none" className="bg-muted/50 text-transparent rounded-lg w-max animate-pulse">
                        <Tag strokeWidth={1.2} className='size-4' />
                        helo tag
                    </Badge>
                </div>
                <div className="mt-3.5 flex flex-wrap items-start gap-1.5 mb-5">
                    {["tag1","tag testa", "tag re","ddk e","Base ldjf ldf"].map((competence, index) => (
                        <span key={index} className="h-max border border-border/60 text-sm text-transparent bg-muted/50 px-2 py-0.5 rounded-lg animate-pulse">
                            {competence}
                        </span>
                    ))}
                    
                </div>
            </div>
            <div className="flex justify-end items-center mt-auto gap-2 pt-4 border-t border-border/80">
                <Button intent="outline" size="square-petite">
                    <Bookmark strokeWidth={1.2} stroke='currentColor' className='size-4' />
                </Button>
                <GetStartedChat btnText="Prendre contact" btnClass="w-max text-transparent bg-muted/50 animate-pulse" />
            </div>
        </article>
    )
}