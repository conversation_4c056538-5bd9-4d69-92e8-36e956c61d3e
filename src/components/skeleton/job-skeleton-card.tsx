"use client"

import { Bookmark, Building2, Calendar, MapPin, Tag } from "lucide-react"
import { Button } from "../ui/button"
import { Badge } from "../ui/badge"
import { cn } from "@/lib/utils"


export const JobSkeletonCard = (job: { className?: string, titleClass?: string }) => {
    return (
        <div className={`${cn("flex relative flex-col border border-border/70 bg-bg rounded-xl isolate overflow-hidden p-0.5", job.className)}`}>
            <div className="w-full aspect-[3/2.3] rounded-xl object-cover pointer-events-none bg-muted animate-pulse" />
            <span className="text-sm flex items-center absolute top-2 left-2 bg-gray-300/80 dark:bg-gray-800/70 px-1 py-0.5 rounded-md animate-pulse text-transparent">
                <Calendar strokeWidth={1.2} className="size-4 mr-0.5" />
                Hello hello
            </span>
            <div className="p-5 flex flex-col pointer-events-none">
                <div className="flex items-center gap-2 justify-between pointer-events-none">
                    <Badge intent="none" className="bg-muted text-transparent rounded-md w-max">
                        <Tag strokeWidth={1.2} className='size-4' />
                        Music md
                    </Badge>
                </div>
                <h3 className={cn("font-semibold text-transparent bg-muted animate-pulse mt-2 pointer-events-none", job.titleClass)}>
                    dsdlksldksld
                </h3>
                <div className="mt-3 flex flex-col gap-2 pointer-events-none">
                    <p className="line-clamp-1 text-sm text-transparent bg-muted rounded-lg animate-pulse">
                        Hello hello hello but but
                    </p>
                    <div className="flex flex-wrap gap-3 items-center animate-pulse">
                        <span className="text-sm text-fg-muted flex items-center">
                            <MapPin strokeWidth={1.2} className="size-4 mr-1 opacity-50" />
                            <span className="bg-muted text-transparent rounded-lg">dsdjskjd kd</span>
                        </span>
                        <span className="size-1 rounded-full flex bg-fg-muted/50"></span>
                        <span className="text-sm text-fg-muted flex items-center animate-pulse">
                            <Building2 strokeWidth={1.2} className="size-4 mr-1 opacity-50" />
                            <span className="bg-muted text-transparent rounded-lg">dsdjskjd kd</span>
                        </span>
                    </div>
                </div>
            </div>

            <div className="flex justify-end items-center gap-2 px-3.5 pb-3.5 mt-auto animate-pulse">
                <Button intent="none" size="square-petite" className={"relative z-[2] bg-muted text-transparent"}>
                    <Bookmark strokeWidth={1.2} stroke='currentColor' className='size-4' />
                </Button>
                <Button intent="none" size="small" className={"flex-1 justify-center w-full bg-muted text-transparent"}>
                    Voir les détails
                </Button>
            </div>
        </div>
    )
}
