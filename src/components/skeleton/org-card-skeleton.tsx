"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from '@/lib/utils';
import { MapPin } from "lucide-react";



export const OrgCardSkeleton = () => {
    return (
        <div className={`${cn("flex relative flex-col border border-border/70 bg-bg rounded-xl isolate overflow-hidden p-0.5 animate-pulse")}`}>
            <div
                className="w-full bg-muted animate-pulse aspect-[3/2.3] rounded-xl object-cover pointer-events-none"
            />
            <div className="p-5 flex flex-col pointer-events-none">
                <h3 className="font-semibold text-transparent bg-muted rounded-lg  mt-2 pointer-events-none">
                    Hello hello
                </h3>
                <div className="mt-3 flex flex-col gap-2 pointer-events-none">
                    <div className="flex items-center text-fg-muted text-sm">
                        <MapPin strokeWidth={1.2} className="size-4 mr-1" />
                        <span className="text-transparent bg-muted rounded-lg">Ici et là</span>
                    </div>
                </div>
            </div>

            <div className="flex justify-end items-center gap-2 px-3.5 pb-3.5 mt-auto">
                <Button intent="white/dark" size="small" className="flex-1 justify-center w-full bg-muted text-transparent">
                    Voir l'organisme
                </Button>
            </div>
        </div>
    )
}
