"use client"

import { MessageSquare } from 'lucide-react'
import { AnimatedChatDemo } from './chat-box-demo'

export const CardRealChat = () => {
    return (
        <>
            <div className="relative rounded-xl bg-bg border p-0.5">
                <div className="relative flex h-full flex-col overflow-hidden">
                    <div className="px-5 pt-5 sm:px-10 sm:pt-10">
                        <div className="flex items-center">
                            <MessageSquare className="h-6 w-6 text-fg-primary mr-2" />
                            <p className="text-lg font-medium tracking-tight text-fg-title max-lg:text-center">Chat en temps réel</p>
                        </div>
                        <p className="mt-2 max-w-lg text-sm/6 text-fg-muted max-lg:text-center">
                            Communication transparente entre artistes et organisations avec partage de fichiers et notifications.
                        </p>
                    </div>
                    <div className="flex flex-1 min-h-14 w-full items-center justify-center">
                        <AnimatedChatDemo/>
                    </div>
                </div>
            </div>
        </>
    )
}
