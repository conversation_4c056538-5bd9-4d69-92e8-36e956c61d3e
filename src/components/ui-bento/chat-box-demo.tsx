"use client"

import { motion, AnimatePresence } from "framer-motion"
import { Send } from "lucide-react"
import { useEffect, useState } from "react"
import { buttonStyles } from "../ui/button"




// Chat messages data - Extended with more messages
const messages = [
  {
    id: 1,
    text: "Salut Elena ! Ravi de travailler avec vous sur notre événement.",
    sender: "user",
    delay: 1000,
  },
  {
    id: 2,
    text: "Merci ! J'ai hâte. Quel genre de musique préférez-vous ?",
    sender: "other",
    delay: 2500,
  },
]

// Typing indicator component
const TypingIndicator = () => (
  <motion.div
    className="flex justify-start"
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -10 }}
    transition={{ duration: 0.3 }}
  >
    <div className="bg-bg border text-gray-800 text-xs p-3 rounded-lg max-w-[80%] shadow-sm">
      <div className="flex space-x-1">
        <motion.div
          className="w-2 h-2 bg-gray-400 rounded-full"
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, delay: 0 }}
        />
        <motion.div
          className="w-2 h-2 bg-gray-400 rounded-full"
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, delay: 0.2 }}
        />
        <motion.div
          className="w-2 h-2 bg-gray-400 rounded-full"
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, delay: 0.4 }}
        />
      </div>
    </div>
  </motion.div>
)

// Message component
const ChatMessage = ({ message, index }: { message: (typeof messages)[0]; index: number }) => {
  const isUser = message.sender === "user"

  return (
    <motion.div
      className={`flex ${isUser ? "justify-end" : "justify-start"}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        type: "spring",
        stiffness: 200,
        damping: 20,
        delay: index * 0.1,
      }}
    >
      <motion.div
        className={`text-xs p-2 rounded-lg max-w-[80%] ${
          isUser ? "bg-primary text-fg-primary-btn" : "bg-bg border text-fg-muted"
        }`}
        transition={{ type: "spring", stiffness: 400, damping: 25 }}
      >
        {message.text}
      </motion.div>
    </motion.div>
  )
}

export const AnimatedChatDemo = () => {
  const [visibleMessages, setVisibleMessages] = useState<typeof messages>([])
  const [showTyping, setShowTyping] = useState(false)
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0)
  const [inputValue, setInputValue] = useState("")
  const [isInputFocused, setIsInputFocused] = useState(false)

  // Auto-play animation sequence
  useEffect(() => {
    const timer = setTimeout(
      () => {
        if (currentMessageIndex < messages.length) {
          const currentMessage = messages[currentMessageIndex]

          // Show typing indicator for messages from "other"
          if (currentMessage.sender === "other") {
            setShowTyping(true)

            setTimeout(() => {
              setShowTyping(false)
              setVisibleMessages((prev) => [...prev, currentMessage])
              setCurrentMessageIndex((prev) => prev + 1)
            }, currentMessage.delay)
          } else {
            // For user messages, show immediately
            setVisibleMessages((prev) => [...prev, currentMessage])
            setCurrentMessageIndex((prev) => prev + 1)
          }
        } else {
          // Reset animation after all messages are shown
          setTimeout(() => {
            setVisibleMessages([])
            setCurrentMessageIndex(0)
            setInputValue("")
          }, 4000) // Extended pause to see all messages
        }
      },
      currentMessageIndex === 0 ? 500 : messages[currentMessageIndex - 1]?.delay || 1000,
    )

    return () => clearTimeout(timer)
  }, [currentMessageIndex])

  // Simulate typing in input field
  useEffect(() => {
    if (visibleMessages.length === messages.length) {
      const typingText = "Des morceaux classiques pendant le dîner seraient parfaits."
      let currentIndex = 0

      const typingTimer = setInterval(() => {
        if (currentIndex <= typingText.length) {
          setInputValue(typingText.slice(0, currentIndex))
          currentIndex++
        } else {
          clearInterval(typingTimer)
        }
      }, 100)

      return () => clearInterval(typingTimer)
    }
  }, [visibleMessages.length])

  return (
    <motion.div
      className="w-full h-full flex flex-col justify-between bg-muted rounded-t-xl p-4"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      {/* Messages Container */}
      <motion.div className="space-y-3 flex-1 overflow-hidden" layout>
        <AnimatePresence mode="popLayout">
          {visibleMessages.map((message, index) => (
            <ChatMessage key={message.id} message={message} index={index} />
          ))}

          {showTyping && <TypingIndicator key="typing" />}
        </AnimatePresence>
      </motion.div>

      {/* Input Area */}
      <motion.div
        className="mt-4 flex items-center gap-2 border-t pt-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.4 }}
      >
        <motion.input
          type="text"
          placeholder="Écrivez votre message..."
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onFocus={() => setIsInputFocused(true)}
          onBlur={() => setIsInputFocused(false)}
          className="flex-1 bg-transparent text-xs text-fg-muted outline-none placeholder:text-fg-subtle pb-3 border-b-2 transition-colors"
          animate={{
            borderColor: isInputFocused ? "#3b82f6" : "#e5e7eb",
          }}
          transition={{ duration: 0.2 }}
        />

        <motion.span
          className={buttonStyles({ size: "square-petite", className: "size-7 cursor-pointer" })}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          animate={{
            opacity: isInputFocused? 1 : 0.6,
          }}
          transition={{ duration: 0.2 }}
        >
          <Send className="size-3" />
        </motion.span>
      </motion.div>
    </motion.div>
  )
}
