"use client"

import {
    format,
    isSameMonth,
    isToday,
    startOfMonth,
    startOfWeek,
    addDays,
    endOfWeek,
    endOfMonth,
    isBefore,
} from "date-fns"
import { cn } from "@/lib/utils"
import { motion } from 'framer-motion';

// Mock availability data for demonstration
const mockAvailabilityData = [
    { date: "2024-12-15", status: "available", id: "1" },
    { date: "2024-12-16", status: "booked", id: "2" },
    { date: "2024-12-17", status: "unavailable", id: "3" },
    { date: "2024-12-20", status: "available", id: "4" },
    { date: "2024-12-21", status: "available", id: "5" },
    { date: "2024-12-22", status: "booked", id: "6" },
    { date: "2024-12-25", status: "unavailable", id: "7" },
    { date: "2024-12-28", status: "available", id: "8" },
    { date: "2024-12-29", status: "available", id: "9" },
    { date: "2024-12-30", status: "booked", id: "10" },
]


const CalendarGrid = ({ currentDate }: { currentDate: Date }) => {
    const getAvailabilityStatus = (date: Date): string | null => {
        const dateString = format(date, "yyyy-MM-dd")
        const match = mockAvailabilityData.find((item) => item.date === dateString)
        return match?.status || null
    }

    const getStatusDot = (status: string | null) => {
        if (!status) return null

        const colorMap: Record<string, string> = {
            available: "bg-emerald-500",
            unavailable: "bg-red-500",
            booked: "bg-amber-500",
        }

        return <div className={`size-1.5 rounded-full ${colorMap[status] || colorMap.available}`} />
    }

    const renderMonthView = () => {
        const monthStart = startOfMonth(currentDate)
        const monthEnd = endOfMonth(monthStart)
        const startDate = startOfWeek(monthStart)
        const endDate = endOfWeek(monthEnd)

        const rows = []
        let days = []
        let day = startDate

        while (day <= endDate) {
            for (let i = 0; i < 7; i++) {
                const cloneDay = new Date(day)
                const status = getAvailabilityStatus(cloneDay)
                const isCurrentMonth = isSameMonth(day, monthStart)
                const isPastDate = isBefore(day, new Date().setHours(0, 0, 0, 0))

                days.push(
                    <motion.div
                        key={day.toString()}
                        className={`
              size-10  text-sm p-2 border border-border/70 relative rounded-xl
              ${!isCurrentMonth ? "bg-bg-surface/30" : ""}
              ${isToday(day) ? "bg-primary-50 dark:bg-primary-950/40 text-fg-primary" : ""}
              ${isPastDate && isCurrentMonth ? "bg-bg" : ""}
            `}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <div className="size-auto flex items-center justify-center rounded-full">{format(day, "d")}</div>
                        {status && <div className="absolute bottom-1 right-1">{getStatusDot(status)}</div>}
                    </motion.div>,
                )
                day = addDays(day, 1)
            }
            rows.push(
                <div key={day.toString()} className="grid grid-cols-7 gap-0.5">
                    {days}
                </div>,
            )
            days = []
        }

        return (
            <div className="space-y-0.5 mt-2">
                {rows}
            </div>
        )
    }

    return renderMonthView()
}

interface NonFunctionalCalendarProps {
    className?: string
}

export const NonFunctionalCalendar = ({ className = "" }: NonFunctionalCalendarProps) => {
    // Fixed current date - no state management
    const currentDate = new Date(2024, 11, 1) // December 2024


    return (
        <div className={cn("overflow-hidden min-w-[300px]", className)}>
             <CalendarGrid currentDate={currentDate} />
        </div>
    )
}
