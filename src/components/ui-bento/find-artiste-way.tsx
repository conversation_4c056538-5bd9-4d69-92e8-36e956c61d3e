import { User, Users } from 'lucide-react'
import React from 'react'
import { AnimatedBentoProfile } from './skeleton-ui/profile'

export const FindArtisteWay = () => {
    return (
        <>
            <div className="lg:row-span-2 flex flex-col gap-4">
                <div className="relative flex flex-col rounded-xl p-8 sm:p-10 bg-bg border lg:rounded-l-3xl">
                    <div className="flex items-center">
                        <Users className="h-6 w-6 text-fg-primary mr-2" />
                        <p className="text-lg font-medium tracking-tight text-fg-title max-lg:text-center">
                            Profils Détaillés
                        </p>
                    </div>
                    <p className="mt-2 max-w-lg text-sm/6 text-fg-muted max-lg:text-center">
                        Créez votre profil professionnel avec portfolio, expériences, compétences et recevez des
                        recommandations de vos collaborateurs.
                    </p>
                </div>
                <div className="w-full h-full flex flex-col rounded-xl bg-bg border flex-1 overflow-hidden">
                    <div className="px-8 pt-8 sm:px-10 sm:pt-10">
                        <div className="flex items-center">
                            <User className="h-6 w-6 text-fg-primary mr-2" />
                            <p className="text-lg font-medium tracking-tight text-fg-title max-lg:text-center">
                                Personnalisation du Profil
                            </p>
                        </div>
                        <p className="mt-2 max-w-lg text-sm/6 text-fg-muted max-lg:text-center">
                            Personnalisez votre profil avec des galeries médias, liens sociaux et mettez en avant vos
                            réalisations marquantes.
                        </p>
                    </div>
                    <div className="@container relative min-h-120 w-full grow max-lg:mx-auto max-lg:max-w-sm ml-6">
                        <div className="absolute bottom-0 overflow-hidden">
                            <AnimatedBentoProfile />
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}
