import { Calendar } from 'lucide-react'
import React from 'react'
import { NonFunctionalCalendar } from './demo-calendar'

export const ScheduleArtiste = () => {
    return (
        <div className="relative max-lg:row-start-3 lg:col-start-2 lg:row-start-2 rounded-xl bg-bg border">
            <div className="relative flex h-full flex-col overflow-hidden p-0.5">
                <div className="px-5 pt-5 sm:px-10 sm:pt-10">
                    <div className="flex items-center">
                        <Calendar className="h-6 w-6 text-fg-primary mr-2" />
                        <p className="text-lg font-medium tracking-tight text-fg-title max-lg:text-center">
                            Planification Intelligente
                        </p>
                    </div>
                    <p className="mt-2 max-w-lg text-sm/6 text-fg-muted max-lg:text-center">
                        Système de calendrier intégré affichant la disponibilité des artistes et empêchant les conflits de double réservation.
                    </p>
                </div>
                <div className="@container rounded-t-xl bg-muted overflow-hidden flex flex-1 items-center justify-center">
                    <div className="overflow-hidden pl-4">
                       <NonFunctionalCalendar/>
                    </div>
                </div>
            </div>
        </div>
    )
}
