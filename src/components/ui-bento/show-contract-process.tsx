"use client"

import { FileText } from 'lucide-react'
import { motion, AnimatePresence } from "framer-motion"
import { useState, useEffect } from "react"
import { FilePenLineIcon as Signature, Shield, Check } from "lucide-react"


const contractSteps = [
    { id: 1, text: "CONTRAT DE PRESTATION ARTISTIQUE", type: "title" },
    { id: 2, text: "Entre l'organisme [NOM] et l'artiste [ARTISTE]", type: "subtitle" },
    { id: 3, text: "Article 1 - Objet du contrat", type: "article" },
    { id: 4, text: "L'artiste s'engage à réaliser une prestation de danse contemporaine", type: "content" },
    { id: 5, text: "Article 2 - Rémunération", type: "article" },
    { id: 6, text: "Montant: 2,500€ TTC", type: "content" },
    { id: 7, text: "Article 3 - Conditions", type: "article" },
    { id: 8, text: "Durée: 3 jours • Lieu: Théâtre National", type: "content" },
]


export const ShowContractProcess = () => {
    const [currentStep, setCurrentStep] = useState(0)
    const [showSignature, setShowSignature] = useState(false)
    const [isComplete, setIsComplete] = useState(false)

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentStep((prev) => {
                if (prev < contractSteps.length - 1) {
                    return prev + 1
                } else if (!showSignature) {
                    setShowSignature(true)
                    return prev
                } else if (!isComplete) {
                    setIsComplete(true)
                    return prev
                } else {
                    // Reset animation
                    setShowSignature(false)
                    setIsComplete(false)
                    return 0
                }
            })
        }, 800)

        return () => clearInterval(timer)
    }, [showSignature, isComplete])

    return (
        <>
            <div className="relative flex flex-col lg:row-span-2 rounded-tl-xl bg-bg border overflow-hidden">
                <div className="px-8 pt-8 pb-3 sm:px-10 sm:pt-10 sm:pb-0">
                    <div className="flex items-center">
                        <FileText className="h-6 w-6 text-fg-primary mr-2" />
                        <p className="text-lg font-medium tracking-tight text-fg-title max-lg:text-center">Contrats Intelligents</p>
                    </div>
                    <p className="mt-2 max-w-lg text-sm/6 text-fg-muted max-lg:text-center">
                        Générez des contrats professionnels avec des conditions préremplies, des signatures numériques et une gestion
                        sécurisée des documents.
                    </p>
                </div>
                <div className="relative min-h-120 w-full grow">
                    <div className="absolute top-10 right-0 bottom-0 left-10 overflow-hidden rounded-tl-xl bg-muted pl-px pt-px">
                        <div className="w-full max-w-md mx-auto">
                            <motion.div
                                className="bg-bg rounded-tl-xl p-6 min-h-100 overflow-hidden"
                                initial={{ scale: 0.9, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ duration: 0.5 }}
                            >
                                {/* Header */}
                                <div className="flex items-center gap-2 mb-6 pb-3 border-b">
                                    <FileText className="h-5 w-5 text-fg-title" />
                                    <span className="font-semibold text-sm">Contrat Intelligent</span>
                                    <motion.div className="ml-auto" animate={{ rotate: isComplete ? 360 : 0 }} transition={{ duration: 0.5 }}>
                                        {isComplete ? (
                                            <Check className="h-4 w-4 text-emerald-500" />
                                        ) : (
                                            <div className="h-2 w-2 bg-fg-primary rounded-full animate-pulse" />
                                        )}
                                    </motion.div>
                                </div>

                                {/* Contract Content */}
                                <div className="space-y-3 mb-6">
                                    <AnimatePresence>
                                        {contractSteps.slice(0, currentStep + 1).map((step, index) => (
                                            <motion.div
                                                key={step.id}
                                                initial={{ opacity: 0, x: -20 }}
                                                animate={{ opacity: 1, x: 0 }}
                                                transition={{ duration: 0.5, delay: index * 0.1 }}
                                                className={`
                  ${step.type === "title" ? "text-lg font-semibold text-fg-title text-center" : ""}
                  ${step.type === "subtitle" ? "text-sm text-center text-fg-muted" : ""}
                  ${step.type === "article" ? "text-sm font-semibold mt-4 text-fg-title" : ""}
                  ${step.type === "content" ? "text-xs text-fg-muted ml-2" : ""}
                `}
                                            >
                                                {step.text}
                                            </motion.div>
                                        ))}
                                    </AnimatePresence>
                                </div>

                                {/* Signature Section */}
                                <AnimatePresence>
                                    {showSignature && (
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            className="border-t pt-4 space-y-4"
                                        >
                                            <div className="flex items-center gap-2 text-sm font-medium">
                                                <Signature className="h-4 w-4 text-fg-title" />
                                                Signatures numériques
                                            </div>

                                            <div className="grid grid-cols-2 gap-4">
                                                <motion.div
                                                    className="text-center"
                                                    initial={{ scale: 0 }}
                                                    animate={{ scale: 1 }}
                                                    transition={{ delay: 0.3 }}
                                                >
                                                    <div className="h-12 bg-emerald-50 dark:bg-indigo-900/20 border-indigo-300 dark:border-indigo-800/60 border-dashed border flex items-center justify-center mb-1 rounded-xl">
                                                        <motion.div
                                                            className="teindigo-600 font-script text-lg"
                                                            initial={{ pathLength: 0 }}
                                                            animate={{ pathLength: 1 }}
                                                            transition={{ duration: 1, delay: 0.5 }}
                                                        >
                                                            Organisation
                                                        </motion.div>
                                                    </div>
                                                    <span className="text-xs text-fg-muted">Signé</span>
                                                </motion.div>

                                                <motion.div
                                                    className="text-center"
                                                    initial={{ scale: 0 }}
                                                    animate={{ scale: 1 }}
                                                    transition={{ delay: 0.6 }}
                                                >
                                                    <div className="h-12 bg-emerald-50 dark:bg-emerald-900/20 border-emerald-300 dark:border-emerald-800/60 border-dashed border flex items-center justify-center mb-1 rounded-xl">
                                                        <motion.div
                                                            className="text-emerald-600 font-script text-lg"
                                                            initial={{ pathLength: 0 }}
                                                            animate={{ pathLength: 1 }}
                                                            transition={{ duration: 1, delay: 0.8 }}
                                                        >
                                                            Artiste
                                                        </motion.div>
                                                    </div>
                                                    <span className="text-xs text-fg-muted">Signé</span>
                                                </motion.div>
                                            </div>

                                            {/* Security Badge */}
                                            <motion.div
                                                className="flex items-center justify-center gap-2 text-xs text-fg-muted bg-muted/50 rounded p-2"
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: 1 }}
                                                transition={{ delay: 1.2 }}
                                            >
                                                <Shield className="h-3 w-3" />
                                                Sécurisé par blockchain
                                            </motion.div>
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </motion.div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}


