"use client"

import { ShareOptionsProfile } from "@/components/artiste/options-share-profile"
import { Badge } from "@/components/ui/badge"
import { AnimatePresence, motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { BrickWallIcon as <PERSON><PERSON>allF<PERSON>, MapPin, Star, Tag,Timer } from "lucide-react"
import { useEffect, useState } from "react"
import { buttonStyles } from "@/components/ui/button"

// Mock data for the real content
const profileData = {
  name: "<PERSON>",
  username: "jean<PERSON><PERSON>",
  title: "Photographe Professionnel",
  genre: "Photographie",
  rating: "4.8 (124 avis)",
  missions: "43 missions",
  city: "Paris, France",
  responseTime: "< 2 heures",
  avatar: "/avatar_def1.webp",
  cover: "/default_cover.webp",
}

// Skeleton component with shimmer effect
const SkeletonBox = ({ className }: { className: string }) => (
  <div className={`bg-gray-200 dark:bg-gray-700 animate-pulse rounded ${className}`} />
)

// Animation variants for the content reveal
const itemVariants = {
  initial: { opacity: 0, y: 10 },
  animate: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.2,
      duration: 0.6,
      ease: "easeOut",
    },
  }),
}

export const AnimatedBentoProfile = () => {
  const [loading, setLoading] = useState(true)

  // Simulate loading time - extended to see skeleton clearly
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false)
    }, 4000) // 4 seconds to clearly see the skeleton

    return () => clearTimeout(timer)
  }, [])

  return (
    <header className="rounded-tl-lg p-1 bg-bg border-l border-t border-border/70">
      {/* Cover Image Area */}
      <div className="h-40 rounded-lg overflow-hidden relative">
        <AnimatePresence mode="wait">
          {loading ? (
            <motion.div
              key="skeleton-cover"
              className="h-full w-full bg-gray-200 dark:bg-gray-700 animate-pulse sm:rounded-lg"
              initial={{ opacity: 1 }}
              exit={{ opacity: 0, transition: { duration: 0.3 } }}
            />
          ) : (
            <motion.div
              key="real-cover"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              <Image
                src={profileData.cover || "/placeholder.svg"}
                alt={"Couverture du profil"}
                width={1000}
                height={1000}
                className="size-full object-cover sm:rounded-lg"
              />
              <span className="absolute inset-0 hidden dark:flex bg-gradient-to-b from-primary-950 via-primary-400/20 to-transparent"></span>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Avatar and Share Options */}
      <div className="px-3 sm:px-4 md:px-5 -mt-16 relative flex items-end justify-between">
        <div className="flex justify-center sm:justify-start flex-1">
          <div className="size-24 overflow-hidden rounded-full border-4 border-bg">
            <AnimatePresence mode="wait">
              {loading ? (
                <motion.div
                  key="skeleton-avatar"
                  className="size-full bg-gray-200 dark:bg-gray-700 animate-pulse"
                  initial={{ opacity: 1 }}
                  exit={{ opacity: 0, transition: { duration: 0.3 } }}
                />
              ) : (
                <motion.div
                  key="real-avatar"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <Image
                    src={profileData.avatar || "/placeholder.svg"}
                    width={200}
                    height={200}
                    alt={profileData.name}
                    className="size-full object-cover rounded-lg"
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Profile Information */}
      <div className="text-center sm:text-left px-4 md:px-5 pb-4 md:pb-5 mt-3">
        {/* Name and Username */}
        <div className="flex flex-wrap items-center justify-center sm:justify-start gap-2">
          <AnimatePresence mode="wait">
            {loading ? (
              <SkeletonBox key="skeleton-name" className="h-7 w-36" />
            ) : (
              <motion.h1
                key="real-name"
                className="font-semibold text-fg-title"
                custom={0}
                initial="initial"
                animate="animate"
                variants={itemVariants}
              >
                {profileData.name}
              </motion.h1>
            )}
          </AnimatePresence>

          <span className="size-1 rounded-full bg-gray-300 dark:bg-gray-600 flex"></span>

          <AnimatePresence mode="wait">
            {loading ? (
              <SkeletonBox key="skeleton-username" className="h-5 w-28" />
            ) : (
              <motion.span
                key="real-username"
                className="text-sm text-fg-muted"
                custom={1}
                initial="initial"
                animate="animate"
                variants={itemVariants}
              >
                @{profileData.username}
              </motion.span>
            )}
          </AnimatePresence>
        </div>

        {/* Title */}
        <div className="mt-2">
          <AnimatePresence mode="wait">
            {loading ? (
              <SkeletonBox key="skeleton-title" className="h-5 w-48 mx-auto sm:mx-0" />
            ) : (
              <motion.h3
                key="real-title"
                className="text-fg text-sm"
                custom={2}
                initial="initial"
                animate="animate"
                variants={itemVariants}
              >
                {profileData.title}
              </motion.h3>
            )}
          </AnimatePresence>
        </div>

        {/* Badges and Info */}
        <div className="mt-4 flex items-center justify-center sm:justify-start flex-wrap gap-x-5 gap-y-3">
          {/* Genre Badge */}
          <AnimatePresence mode="wait">
            {loading ? (
              <SkeletonBox key="skeleton-genre" className="h-7 w-24 rounded-xl" />
            ) : (
              <motion.div key="real-genre" custom={3} initial="initial" animate="animate" variants={itemVariants}>
                <Badge intent="none" className="bg-primary text-fg-primary-btn w-max rounded-xl">
                  <Tag strokeWidth={1.2} className="size-4" />
                  {profileData.genre}
                </Badge>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Rating */}
          <AnimatePresence mode="wait">
            {loading ? (
              <SkeletonBox key="skeleton-rating" className="h-5 w-28" />
            ) : (
              <motion.div key="real-rating" custom={4} initial="initial" animate="animate" variants={itemVariants}>
                <Link
                  href={`/${profileData.username}/avis`}
                  className="flex items-center hover:underline text-sm text-fg-muted"
                >
                  <Star strokeWidth={0} className="size-4 fill-amber-500 mr-0.5" />
                  {profileData.rating}
                </Link>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Missions */}
          <AnimatePresence mode="wait">
            {loading ? (
              <SkeletonBox key="skeleton-missions" className="h-5 w-24" />
            ) : (
              <motion.span
                key="real-missions"
                className="flex items-center text-sm text-fg-muted"
                custom={5}
                initial="initial"
                animate="animate"
                variants={itemVariants}
              >
                <BrickWallFire strokeWidth={1.2} className="size-4 mr-0.5" />
                {profileData.missions}
              </motion.span>
            )}
          </AnimatePresence>

          {/* City */}
          <AnimatePresence mode="wait">
            {loading ? (
              <SkeletonBox key="skeleton-city" className="h-5 w-32" />
            ) : (
              <motion.span
                key="real-city"
                className="text-sm text-fg-muted flex items-center"
                custom={6}
                initial="initial"
                animate="animate"
                variants={itemVariants}
              >
                <MapPin className="size-4 mr-0.5" strokeWidth={1.2} />
                {profileData.city}
              </motion.span>
            )}
          </AnimatePresence>

          {/* Response Time */}
          <AnimatePresence mode="wait">
            {loading ? (
              <SkeletonBox key="skeleton-response" className="h-5 w-16" />
            ) : (
              <motion.span
                key="real-response"
                className="text-sm text-fg-muted flex items-center"
                custom={7}
                initial="initial"
                animate="animate"
                variants={itemVariants}
              >
                <Timer className="size-4 mr-0.5" strokeWidth={1.2} />
                {profileData.responseTime}
              </motion.span>
            )}
          </AnimatePresence>
        </div>
      </div>
    </header>
  )
}
