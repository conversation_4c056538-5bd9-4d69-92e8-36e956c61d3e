import { twMerge } from "tailwind-merge"

interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  constrained?: boolean
  ref?: React.RefObject<HTMLDivElement>
}

const Container = ({ className, constrained = false, ref, ...props }: ContainerProps) => (
  <div
    className={twMerge(
      "w-full lg:app-container",
      constrained ? "sm:px-6 lg:px-8" : "px-4 sm:px-6 lg:px-8",
      className,
    )}
    {...props}
    ref={ref}
  />
)

export type { ContainerProps }
export { Container }
