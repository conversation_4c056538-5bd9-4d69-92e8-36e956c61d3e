"use client"

import { useCallback, useState } from "react";
import { CloudUpload, X } from "lucide-react";
import { motion } from "framer-motion";

interface FileUploadProps {
  accept: string;
  multiple?: boolean;
  onFilesChange: (files: File[]) => void;
  maxSize?: number;
  children: React.ReactNode;
}

export function FileUpload({ accept, multiple = false, onFilesChange, maxSize = 10 * 1024 * 1024, children }: FileUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validateFiles = (files: File[]): File[] => {
    const validFiles: File[] = [];
    let errorMessage = "";

    for (const file of files) {
      if (file.size > maxSize) {
        errorMessage = `File ${file.name} is too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB.`;
        continue;
      }

      // Check file type
      const acceptedTypes = accept.split(',').map(type => type.trim());
      const isValidType = acceptedTypes.some(type => {
        if (type.startsWith('.')) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        } else if (type.includes('/*')) {
          return file.type.startsWith(type.replace('/*', ''));
        } else {
          return file.type === type;
        }
      });

      if (!isValidType) {
        errorMessage = `File ${file.name} is not a supported format.`;
        continue;
      }

      validFiles.push(file);
    }

    if (errorMessage) {
      setError(errorMessage);
    } else {
      setError(null);
    }

    return validFiles;
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const validFiles = validateFiles(files);
    onFilesChange(validFiles);
  }, [onFilesChange, validateFiles]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = validateFiles(files);
    onFilesChange(validFiles);
    // Reset input
    e.target.value = '';
  };

  const handleClick = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = accept;
    input.multiple = multiple;
    input.onchange = (e: Event) => {
      const inputElement = e.target as HTMLInputElement;
      handleFileSelect({ target: inputElement } as React.ChangeEvent<HTMLInputElement>);
    };
    input.click();
  };

  return (
    <div>
      <motion.div
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors duration-200 ${
          isDragOver 
            ? "border-primary bg-primary/5" 
            : "border-slate-300 hover:border-primary"
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <div className="space-y-2">
          <CloudUpload className={`mx-auto text-3xl ${isDragOver ? "text-primary" : "text-slate-400"}`} />
          <p className={`text-sm font-medium ${isDragOver ? "text-primary" : "text-slate-700"}`}>
            {children}
          </p>
          <p className="text-xs text-slate-500">or click to browse</p>
        </div>
      </motion.div>
      
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-600 text-sm flex items-center justify-between"
        >
          <span>{error}</span>
          <button
            onClick={() => setError(null)}
            className="ml-2 text-red-400 hover:text-red-600"
          >
            <X className="h-4 w-4" />
          </button>
        </motion.div>
      )}
    </div>
  );
}
