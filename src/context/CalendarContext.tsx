"use client"

import React, { createContext, useState, useContext, useMemo } from 'react';
import { MonthData, AvailabilityStatus, DayInfo, DateEntry } from '../types/calendar';
import { generateMonthData } from '../utils/calendarUtils';

interface CalendarContextType {
  currentMonth: number;
  currentYear: number;
  monthData: MonthData;
  dateEntries: DateEntry[];
  navigateToPreviousMonth: () => void;
  navigateToNextMonth: () => void;
  navigateToToday: () => void;
  getDayStatusClass: (status: AvailabilityStatus) => string;
  getDayStatusText: (status: AvailabilityStatus) => string;
  updateDateEntry: (entry: DateEntry) => void;
}

const CalendarContext = createContext<CalendarContextType | undefined>(undefined);

// Sample date entries for demonstration
const initialDateEntries: DateEntry[] = [
];

export const useCalendar = () => {
  const context = useContext(CalendarContext);
  if (!context) {
    throw new Error('useCalendar must be used within a CalendarProvider');
  }
  return context;
};

export const CalendarProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const today = new Date();
  const [currentMonth, setCurrentMonth] = useState(today.getMonth());
  const [currentYear, setCurrentYear] = useState(today.getFullYear());
  const [dateEntries, setDateEntries] = useState<DateEntry[]>(initialDateEntries);
  
  // Generate month data whenever month/year or dateEntries changes
  const monthData = useMemo(() => {
    return generateMonthData(currentYear, currentMonth, dateEntries);
  }, [currentYear, currentMonth, dateEntries]);

  const navigateToPreviousMonth = () => {
    setCurrentMonth(prevMonth => {
      if (prevMonth === 0) {
        setCurrentYear(prevYear => prevYear - 1);
        return 11;
      }
      return prevMonth - 1;
    });
  };

  const navigateToNextMonth = () => {
    setCurrentMonth(prevMonth => {
      if (prevMonth === 11) {
        setCurrentYear(prevYear => prevYear + 1);
        return 0;
      }
      return prevMonth + 1;
    });
  };

  const navigateToToday = () => {
    setCurrentMonth(today.getMonth());
    setCurrentYear(today.getFullYear());
  };

  const updateDateEntry = (entry: DateEntry) => {
    setDateEntries(prevEntries => {
      const index = prevEntries.findIndex(e => e.date === entry.date);
      if (index >= 0) {
        return [
          ...prevEntries.slice(0, index),
          entry,
          ...prevEntries.slice(index + 1)
        ];
      }
      return [...prevEntries, entry];
    });
  };

  const getDayStatusClass = (status: AvailabilityStatus): string => {
    switch (status) {
      case 'available':
        return 'bg-teal-100 text-teal-800 border-teal-200 hover:bg-teal-200';
      case 'unavailable':
        return 'bg-slate-100 text-slate-500 border-slate-200 hover:bg-slate-200';
      case 'booked':
        return 'bg-amber-100 text-amber-800 border-amber-200 hover:bg-amber-200';
      default:
        return 'bg-white';
    }
  };

  const getDayStatusText = (status: AvailabilityStatus): string => {
    switch (status) {
      case 'available':
        return 'Disponible';
      case 'unavailable':
        return 'Indisponible';
      case 'booked':
        return 'Deja pris';
      default:
        return '';
    }
  };

  return (
    <CalendarContext.Provider
      value={{
        currentMonth,
        currentYear,
        monthData,
        dateEntries,
        navigateToPreviousMonth,
        navigateToNextMonth,
        navigateToToday,
        getDayStatusClass,
        getDayStatusText,
        updateDateEntry
      }}
    >
      {children}
    </CalendarContext.Provider>
  );
};
