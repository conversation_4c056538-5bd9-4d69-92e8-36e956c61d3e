"use client"

import { createContext, useContext, useReducer, ReactNode } from 'react';

// Define action types
type NavbarActionType = 'OPEN_NAVBAR' | 'CLOSE_NAVBAR' | 'TOGGLE_NAVBAR';

// Define action interfaces
interface NavbarAction {
  type: NavbarActionType;
}

// Define state interface
interface NavbarState {
  isOpen: boolean;
}

// Define initial state
const initialState: NavbarState = {
  isOpen: false,
};

// Create context
const NavbarStateContext = createContext<NavbarState | undefined>(undefined);
const NavbarDispatchContext = createContext<React.Dispatch<NavbarAction> | undefined>(
  undefined
);

// Reducer function
function navbarReducer(state: NavbarState, action: NavbarAction): NavbarState {
  switch (action.type) {
    case 'OPEN_NAVBAR':
      return { ...state, isOpen: true };
    case 'CLOSE_NAVBAR':
      return { ...state, isOpen: false };
    case 'TOGGLE_NAVBAR':
      return { ...state, isOpen: !state.isOpen };
    default:
      return state;
  }
}

// Provider component
interface NavbarProviderProps {
  children: ReactNode;
}

export function AppNavbarProvider({ children }: NavbarProviderProps) {
  const [state, dispatch] = useReducer(navbarReducer, initialState);

  return (
    <NavbarStateContext.Provider value={state}>
      <NavbarDispatchContext.Provider value={dispatch}>
        {children}
      </NavbarDispatchContext.Provider>
    </NavbarStateContext.Provider>
  );
}

// Custom hooks for using the context
export function useNavbarState() {
  const context = useContext(NavbarStateContext);
  if (context === undefined) {
    throw new Error('useNavbarState must be used within a NavbarProvider');
  }
  return context;
}

export function useNavbarDispatch() {
  const context = useContext(NavbarDispatchContext);
  if (context === undefined) {
    throw new Error('useNavbarDispatch must be used within a NavbarProvider');
  }
  return context;
}
