export interface Candidature {
    id: number
    mission: string
    company: string
    applicationDate: string
    status: CandidatureStatus
    description?: string
}

export type CandidatureStatus = 'rejeté' | 'en_cours' | 'accepté' | 'en_attente'

export interface ViewState {
    currentView: 'list' | 'kanban'
}

export const mockCandidatures: Candidature[] = [
    {
        id: 1,
        mission: "Développeur Frontend React",
        company: "Tech Solutions SA",
        applicationDate: "2024-03-15",
        status: "en_cours",
        description: "Développement d'une application web moderne"
    },
    {
        id: 2,
        mission: "UX Designer",
        company: "Design Studio",
        applicationDate: "2024-03-10",
        status: "rejeté",
        description: "Conception d'interfaces utilisateur innovantes"
    },
    {
        id: 3,
        mission: "Chef de Projet Digital",
        company: "Marketing Pro SARL",
        applicationDate: "2024-03-20",
        status: "en_attente",
        description: "Gestion de projets digitaux"
    },
    {
        id: 4,
        mission: "Développeur Full Stack",
        company: "Innovation Labs",
        applicationDate: "2024-03-18",
        status: "accepté",
        description: "Développement d'applications web complètes"
    },
    {
        id: 5,
        mission: "Architecte Cloud",
        company: "Cloud Systems",
        applicationDate: "2024-03-22",
        status: "en_cours",
        description: "Conception d'architectures cloud scalables"
    }
]