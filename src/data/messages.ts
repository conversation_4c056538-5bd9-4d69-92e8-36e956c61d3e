import { artisteProfiles } from "./artiste-profiles"
import { organisations } from "./organizations"
import { ArtisteProfile } from "@/types/artiste-profile"
import { Organization } from "@/types/org_"

export type Chat = {
    id: string,
    slug: string,
    contactType: 'artiste' | 'organisation'
    messages: U_Message[]
} & ({
    contactType: 'artiste',
    contact: ArtisteProfile,
    owner: Organization
} | {
    contactType: 'organisation',
    contact: Organization,
    owner: ArtisteProfile
})

export interface U_Message {
    id: string
    type: 'text' | 'attachment' | 'action'
    content: string
    sender: 'user' | 'contact' | 'system'
    timestamp: Date
    status?: 'read' | 'delivered' | 'sending' | 'sent'
    attachments?: Attachment[]
    action?: {
        type: string
        data: Record<string, any>
    }
}

interface Attachment {
    id: string
    name: string
    type: string
    url: string
    size: number
    thumbnail: string
}

export const mockChats: Chat[] = [
    {
        id: '01',
        slug: "theatre-xskjdhr01229dj",
        contactType: 'organisation',
        contact: organisations[0],
        owner: artiste<PERSON>rofiles[0],
        messages: [
            {
                id: '1',
                type: 'text',
                content: 'Bonjour ! Nous avons remarqué votre profil d\'artiste et nous sommes très intéressés par votre travail.',
                sender: 'contact',
                timestamp: new Date(Date.now() - 3600000),
                status: 'read',
            },
            {
                id: "2",
                type: "text",
                content: "Bonjour ! Je vous remercie de votre intérêt. Je suis ravi(e) que mon travail ait retenu votre attention.",
                sender: "user",
                timestamp: new Date(Date.now() - 3500000),
                status: "read",
            },
            {
                id: "3",
                type: "text",
                content: "Nous préparons actuellement notre prochaine saison et nous aimerions discuter d'une possible collaboration.",
                sender: "contact",
                timestamp: new Date(Date.now() - 3000000),
                status: "read",
            },
            {
                id: "4",
                type: "attachment",
                content: "Voici quelques extraits de mes dernières performances",
                sender: "user",
                timestamp: new Date(Date.now() - 2800000),
                status: "read",
                attachments: [
                    {
                        id: "att1",
                        name: "performance-2023.mp4",
                        type: "video",
                        url: "/shorts/aurel-zola-2.0.mp4",
                        size: 245760,
                        thumbnail: "/placeholder.svg?height=100&width=150",
                    }
                ],
            },
            {
                id: "5",
                type: "text",
                content: "C'est impressionnant ! Votre style correspond parfaitement à ce que nous recherchons.",
                sender: "contact",
                timestamp: new Date(Date.now() - 2400000),
                status: "read",
            },
            {
                id: "6",
                type: "action",
                content: "Rendez-vous programmé",
                sender: "system",
                timestamp: new Date(Date.now() - 1800000),
                action: {
                    type: "meeting",
                    data: {
                        title: "Discussion de Collaboration Artistique",
                        date: "Demain, 14h00",
                        participants: ["Vous", "Direction Artistique", "Responsable de Production"],
                    },
                },
            },
            {
                id: "7",
                type: "text",
                content: "Parfait ! J'ai hâte de discuter de ce projet avec vous demain.",
                sender: "user",
                timestamp: new Date(Date.now() - 1200000),
                status: "delivered",
            },
        ]
    }, {
        id: '02',
        slug: "theatre-xskjdhr02329dj",
        contactType: 'artiste',
        contact: artisteProfiles[0],
        owner: organisations[0],
        messages: [
            {
                id: '1',
                type: 'text',
                content: 'Bonjour ! Nous avons remarqué votre profil d\'artiste et nous sommes très intéressés par votre travail.',
                sender: "user",
                timestamp: new Date(Date.now() - 3600000),
                status: 'read',
            },
            {
                id: "2",
                type: "text",
                content: "Bonjour ! Je vous remercie de votre intérêt. Je suis ravi(e) que mon travail ait retenu votre attention.",
                sender: "contact",
                timestamp: new Date(Date.now() - 3500000),
                status: "read",
            },
            {
                id: "3",
                type: "text",
                content: "Nous préparons actuellement notre prochaine saison et nous aimerions discuter d'une possible collaboration.",
                sender: "user",
                timestamp: new Date(Date.now() - 3000000),
                status: "read",
            },
            {
                id: "4",
                type: "attachment",
                content: "Voici quelques extraits de mes dernières performances",
                sender: "contact",
                timestamp: new Date(Date.now() - 2800000),
                status: "read",
                attachments: [
                    {
                        id: "att1",
                        name: "performance-2023.mp4",
                        type: "video",
                        url: "/shorts/aurel-zola-2.0.mp4",
                        size: 245760,
                        thumbnail: "/placeholder.svg?height=100&width=150",
                    }
                ],
            },
            {
                id: "5",
                type: "text",
                content: "C'est impressionnant ! Votre style correspond parfaitement à ce que nous recherchons.",
                sender: "user",
                timestamp: new Date(Date.now() - 2400000),
                status: "read",
            },
            {
                id: "6",
                type: "action",
                content: "Rendez-vous programmé",
                sender: "system",
                timestamp: new Date(Date.now() - 1800000),
                action: {
                    type: "meeting",
                    data: {
                        title: "Discussion de Collaboration Artistique",
                        date: "Demain, 14h00",
                        participants: ["Vous", "Direction Artistique", "Responsable de Production"],
                    },
                },
            },
            {
                id: "7",
                type: "text",
                content: "Parfait ! J'ai hâte de discuter de ce projet avec vous demain.",
                sender: "contact",
                timestamp: new Date(Date.now() - 1200000),
                status: "delivered",
            },
        ]
    }
]