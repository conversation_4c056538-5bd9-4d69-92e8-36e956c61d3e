import { artisteProfiles } from "./artiste-profiles";
import { organisations } from "./organizations";

// Helper function to generate a random date within the last 7 days
const getRandomRecentDate = () => {
  const now = new Date();
  const daysAgo = Math.floor(Math.random() * 7);
  const hoursAgo = Math.floor(Math.random() * 24);
  const minutesAgo = Math.floor(Math.random() * 60);
  
  return new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000) - (hoursAgo * 60 * 60 * 1000) - (minutesAgo * 60 * 1000));
};

// Message templates for chat conversations
const messageTemplates = [
  "Bonjour, j'aimerais discuter d'une opportunité de collaboration.",
  "Merci pour votre message. Je suis intéressé(e) par cette proposition.",
  "Pouvez-vous me donner plus de détails sur le projet?",
  "Quelles sont vos disponibilités pour une rencontre?",
  "J'ai regardé votre portfolio et j'aime beaucoup votre travail.",
  "Nous organisons un événement le mois prochain et nous aimerions vous y inviter.",
  "Votre expérience correspond parfaitement à ce que nous recherchons.",
  "Je vous envoie les détails du contrat comme convenu.",
  "Avez-vous des questions concernant notre collaboration?",
  "Je suis disponible pour un appel cette semaine si cela vous convient.",
  "Pourriez-vous m'envoyer votre tarif pour ce type de prestation?",
  "Nous avons bien reçu votre candidature et souhaitons vous rencontrer.",
  "Suite à notre entretien, nous sommes ravis de vous proposer cette mission.",
  "Merci pour votre performance, le retour du public a été excellent.",
  "Nous préparons un nouveau spectacle et pensons à vous pour un rôle.",
  "Votre profil a retenu notre attention pour notre prochain événement."
];

// Type for mock chat entries
export type MockChat = {
  id: string;
  slug: string;
  name: string;
  avatar: string;
  lastMessage: string;
  timestamp: Date;
  unread: number;
  isOnline: boolean;
};

// Generate mock chats for artistes (organizations who chatted with the artiste)
export const mockChatsForArtiste: MockChat[] = Array.from({ length: 16 }, (_, index) => {
  const orgIndex = index % organisations.length;
  const org = organisations[orgIndex];
  const randomMessage = messageTemplates[Math.floor(Math.random() * messageTemplates.length)];
  const timestamp = getRandomRecentDate();
  const unread = Math.random() > 0.7 ? Math.floor(Math.random() * 3) + 1 : 0;
  const isOnline = Math.random() > 0.6;
  
  return {
    id: `org-${org.id}-${index}`,
    slug: `${org.slug}-${index}`,
    name: org.name,
    avatar: org.profileImage || `/avatar/m_user_${(index % 6) + 1}.webp`,
    lastMessage: randomMessage,
    timestamp,
    unread,
    isOnline
  };
});

// Generate mock chats for organizations (artistes who chatted with the org)
export const mockChatsForOrg: MockChat[] = Array.from({ length: 16 }, (_, index) => {
  const artisteIndex = index % artisteProfiles.length;
  const artiste = artisteProfiles[artisteIndex];
  const randomMessage = messageTemplates[Math.floor(Math.random() * messageTemplates.length)];
  const timestamp = getRandomRecentDate();
  const unread = Math.random() > 0.7 ? Math.floor(Math.random() * 3) + 1 : 0;
  const isOnline = Math.random() > 0.6;
  const gender = artiste.about.gender === 'male' ? 'm' : 'w';
  
  return {
    id: `artiste-${artiste.id}-${index}`,
    slug: `${artiste.username}-${index}`,
    name: `${artiste.about.firstName} ${artiste.about.lastName}`,
    avatar: `/avatar/${gender}_user_${(index % 6) + 1}.webp`,
    lastMessage: randomMessage,
    timestamp,
    unread,
    isOnline
  };
});