import { CardNotificationProps, NotificationType } from "@/types";

export const user_notifications: CardNotificationProps[] = [
    {
        type: 'facture' as NotificationType,
        title: 'Nouvelle Facture',
        message: 'a été envoyée par l\'administrateur.',
        sender: 'Admin',
        time: 'il y a 3 heures',
        source: 'Système',
    },
    {
        type: 'rendez_vous' as NotificationType,
        title: 'Rendez-vous Confirmé',
        message: 'a été accepté par l\'artiste.',
        sender: 'Artiste',
        time: 'hier',
        source: '<PERSON><PERSON><PERSON>',
    },
    {
        type: 'demande_rendez_vous' as NotificationType,
        title: 'Demande de Rendez-vous',
        message: 'a été envoyée par l\'organisation.',
        sender: 'Organisation',
        time: 'il y a 1 jour',
        source: 'Calendrier',
        onApprove: () => alert('Rendez-vous accepté !'),
        onDeny: () => alert('Rendez-vous refusé !'),
    },
    {
        type: 'demande_signature' as NotificationType,
        title: 'Demande de Signature',
        message: 'pour le contrat a été envoyée.',
        sender: 'Contrats',
        time: 'à l\'instant',
        source: 'Documents',
    },
    {
        type: 'action' as NotificationType,
        title: 'synergy.fig',
        message: 'vous invite à',
        sender: 'Sophia Williams',
        time: 'il y a 2 heures',
        source: 'Synergy RH',
        onApprove: () => alert('Approuvé !'),
        onDeny: () => alert('Refusé !'),
    },
    {
        type: 'simple' as NotificationType,
        title: 'Bienvenue !',
        message: 'a rejoint la plateforme.',
        sender: 'Malik',
        time: 'il y a 5 minutes',
        source: 'Admin',
    },
    {
        type: 'file' as NotificationType,
        title: 'projet.pdf',
        message: 'vous a envoyé un fichier',
        sender: 'Marion',
        time: 'il y a 1 jour',
        source: 'Projets',
        fileName: 'projet.pdf',
    },
    {
        type: 'message' as NotificationType,
        title: 'Nouveau Message',
        message: 'vous a envoyé un message',
        sender: 'Cedric',
        time: 'à l\'instant',
        source: 'Boîte de réception',
    },
];

export const org_notifications: CardNotificationProps[] = [
    {
        type: 'facture' as NotificationType,
        title: 'Nouvelle Facture',
        message: 'a été envoyée par l\'administrateur.',
        sender: 'Admin',
        time: 'il y a 3 heures',
        source: 'Système',
    },
    {
        type: 'rendez_vous' as NotificationType,
        title: 'Rendez-vous Confirmé',
        message: 'a accepté votre rendez-vous.',
        sender: 'John Doe',
        time: 'hier',
        source: 'Calendrier',
    },
    {
        type: 'demande_signature' as NotificationType,
        title: 'Demande de Signature',
        message: 'Pour le contrat a été envoyée.',
        sender: '',
        time: 'à l\'instant',
        source: 'Documents',
    },
    {
        type: 'action' as NotificationType,
        title: 'Leap',
        message: 'A envoyé une facture',
        sender: 'Sophia Williams',
        time: 'il y a 2 heures',
        source: 'Leap',
        onApprove: () => alert('Approuvé !'),
        onDeny: () => alert('Refusé !'),
    },
    {
        type: 'simple' as NotificationType,
        title: 'Bienvenue !',
        message: 'a rejoint la plateforme.',
        sender: 'Malik',
        time: 'il y a 5 minutes',
        source: 'Admin',
    },
    {
        type: 'file' as NotificationType,
        title: 'projet.pdf',
        message: 'vous a envoyé un fichier',
        sender: 'Marion',
        time: 'il y a 1 jour',
        source: 'Projets',
        fileName: 'projet.pdf',
    },
    {
        type: 'message' as NotificationType,
        title: 'Nouveau Message',
        message: 'vous a envoyé un message',
        sender: 'Cedric',
        time: 'à l\'instant',
        source: 'Boîte de réception',
    },
];