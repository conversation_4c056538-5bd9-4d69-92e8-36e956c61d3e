import { Opportunity } from "@/types";
import { organisations } from "./organizations";


export const opportunites: Opportunity[] = [
    {
        id: 1,
        jobTitle: "Danseur Ballet Classique",
        slug: "danseur-ballet-classique",
        genre: "Ballet",
        coverImage: "/job/Ballet.webp",
        description: "Recherche danseur.se de ballet classique pour production du Lac des Cygnes. Excellente maîtrise technique requise.",
        organization: organisations[0],
        datePublished: "2024-06-15",
        periode: {
            debut: "2024-09-10",
            fin: "2024-12-10"
        },
        skillsRequired: ["Ballet classique", "Pas de deux", "Technique Vaganova", "Performance scénique"],
        location: {
            city: "Bruxelles",
            country: "Belgique",
            type: "onsite"
        }
    },
    {
        id: 2,
        jobTitle: "Troupe de danse contemporaine",
        slug: "danse-contemporaine-festival",
        genre: "Contemporain",
        coverImage: "/job/Comptemporain.webp",
        description: "Festival d'été recherche une troupe de 4-6 danseurs pour une performance contemporaine sur le thème de l'écologie.",
        organization: organisations[3],
        datePublished: "2024-06-10",
        periode: {
            debut: "2024-08-15",
            fin: "2024-08-20"
        },
        skillsRequired: ["Danse contemporaine", "Improvisation", "Performance collective", "Expression corporelle"],
        location: {
            city: "Liège",
            country: "Belgique",
            type: "onsite"
        }
    },
    {
        id: 3,
        jobTitle: "Danseur Hip-Hop",
        slug: "danseur-hip-hop-show",
        genre: "Hip-Hop",
        coverImage: "/job/danseurs-hip-hop.webp",
        description: "Recherche danseur.se hip-hop pour spectacle urbain innovant. Maîtrise du breakdance et popping indispensable.",
        organization: organisations[3],
        datePublished: "2024-06-20",
        periode: {
            debut: "2024-07-25",
            fin: "2024-08-25"
        },
        skillsRequired: ["Breakdance", "Popping", "Freestyle", "Battle"],
        location: {
            city: "Anvers",
            country: "Belgique",
            type: "onsite"
        }
    },
    {
        id: 4,
        jobTitle: "Danseur Modern Jazz",
        slug: "danseur-modern-jazz",
        genre: "Modern Jazz",
        coverImage: "/job/moderne-jazz.webp",
        description: "Compagnie recherche danseur.se pour création modern jazz fusion. Style unique mêlant techniques contemporaines et jazz.",
        organization: organisations[2],
        datePublished: "2024-06-25",
        periode: {
            debut: "2024-08-01",
            fin: "2024-10-03"
        },
        skillsRequired: ["Modern Jazz", "Contemporain", "Improvisation", "Technique Graham"],
        location: {
            city: "Gand",
            country: "Belgique",
            type: "onsite"
        }
    },
    {
        id: 5,
        jobTitle: "Danseur Traditionnel",
        slug: "danseur-folklore-francais",
        genre: "Folklore",
        coverImage: "/job/tradi2.webp",
        description: "Festival de danses traditionnelles recherche danseur.se maîtrisant les danses folkloriques belges.",
        organization: organisations[4],
        datePublished: "2024-06-18",
        periode: {
            debut: "2024-07-15",
            fin: "2024-07-30"
        },
        skillsRequired: ["Danses traditionnelles", "Bourrée", "Gavotte", "Farandole"],
        location: {
            city: "Charleroi",
            country: "Belgique",
            type: "onsite"
        }
    },
    {
        id: 6,
        jobTitle: "Professeur de Danse Contemporaine",
        slug: "professeur-danse-contemporaine",
        genre: "Contemporain",
        coverImage: "/job/Professeur-de-danse-comptemporaine.webp",
        description: "École de danse recherche professeur pour cours avancés. Expérience pédagogique requise.",
        organization: organisations[1],
        datePublished: "2025-06-01",
        periode: {
            debut: "2025-09-01",
            fin: "2026-06-30"
        },
        skillsRequired: ["Pédagogie", "Danse contemporaine", "Chorégraphie", "Analyse du mouvement"],
        location: {
            city: "Bruxelles",
            country: "Belgique",
            type: "onsite"
        }
    },
    {
        id: 7,
        jobTitle: "Danseur pour Comédie Musicale",
        slug: "danseur-comedie-musicale",
        genre: "Comédie Musicale",
        coverImage: "/job/danseurs-pour-comedie-musical.webp",
        description: "Production majeure recherche danseurs polyvalents pour nouvelle comédie musicale.",
        organization: organisations[2],
        datePublished: "2025-06-05",
        periode: {
            debut: "2025-08-15",
            fin: "2025-12-31"
        },
        skillsRequired: ["Jazz", "Claquettes", "Chant", "Acting"],
        location: {
            city: "Anvers",
            country: "Belgique",
            type: "onsite"
        }
    },
    {
        id: 8,
        jobTitle: "Danseur Salsa",
        slug: "danseur-salsa-show",
        genre: "Salsa",
        coverImage: "/job/tradi1.webp",
        description: "Spectacle de danses latines recherche danseurs professionnels pour tournée nationale.",
        organization: organisations[0],
        datePublished: "2025-06-10",
        periode: {
            debut: "2025-07-01",
            fin: "2025-08-30"
        },
        skillsRequired: ["Salsa", "Bachata", "Merengue", "Performance scénique"],
        location: {
            city: "Liège",
            country: "Belgique",
            type: "onsite"
        }
    },
    {
        id: 9,
        jobTitle: "Chorégraphe Assistant",
        slug: "choregraphe-assistant",
        genre: "Contemporain",
        coverImage: "/job/contemp2.webp",
        description: "Compagnie contemporaine cherche assistant chorégraphe pour nouvelle création.",
        organization: organisations[4],
        datePublished: "2025-06-15",
        periode: {
            debut: "2025-08-01",
            fin: "2025-11-30"
        },
        skillsRequired: ["Chorégraphie", "Direction artistique", "Gestion d'équipe", "Création contemporaine"],
        location: {
            city: "Gand",
            country: "Belgique",
            type: "onsite"
        }
    },
    {
        id: 10,
        jobTitle: "Danseur Flamenco",
        slug: "danseur-flamenco",
        genre: "Flamenco",
        coverImage: "/job/djazz2.webp",
        description: "Festival international de flamenco recherche danseurs expérimentés.",
        organization: organisations[3],
        datePublished: "2025-06-20",
        periode: {
            debut: "2025-09-15",
            fin: "2025-09-30"
        },
        skillsRequired: ["Flamenco", "Zapateado", "Castagnettes", "Expression dramatique"],
        location: {
            city: "Bruxelles",
            country: "Belgique",
            type: "onsite"
        }
    },
    {
        id: 11,
        jobTitle: "Danseur Ballet Néoclassique",
        slug: "danseur-ballet-neoclassique",
        genre: "Ballet Néoclassique",
        coverImage: "/job/ballet1.webp",
        description: "Compagnie de ballet recherche danseurs pour création néoclassique innovante.",
        organization: organisations[1],
        datePublished: "2025-06-25",
        periode: {
            debut: "2025-10-01",
            fin: "2026-01-31"
        },
        skillsRequired: ["Ballet classique", "Danse contemporaine", "Répertoire néoclassique", "Partnering"],
        location: {
            city: "Anvers",
            country: "Belgique",
            type: "onsite"
        }
    }
]