"use client"

import { useMediaQuery } from "./use-media-query";

export function useBreakpoints() {
    return {
        isMobile: useMediaQuery("(max-width: 639px)"),
        isMiddle: useMediaQuery("(max-width: 767px) and (min-width: 640px)"),
        isTablet: useMediaQuery("(min-width: 768px) and (max-width: 1023px)"),
        isDesktop: useMediaQuery("(min-width: 1024px)"),
        isLargeDesktop: useMediaQuery("(min-width: 1280px)"),
        isPortrait: useMediaQuery("(orientation: portrait)"),
        isLandscape: useMediaQuery("(orientation: landscape)"),
    };
}