import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'

/**
 * Hook to detect if the current route is a specific chat detail page
 * @param initialValue - Optional initial value (can be passed from server props)
 * @returns Whether the current route is a chat detail page
 */
export function useChatRoute(initialValue?: boolean): boolean {
  const pathname = usePathname()
  const [isDetailRoute, setIsDetailRoute] = useState(initialValue ?? false)
  
  useEffect(() => {
    // Check if we're on a specific chat page by examining the URL
    // For messages routes like /messages/[chatId]
    const pathParts = pathname.split('/')
    const hasPathSlug = pathParts.length > 2 && 
                       pathParts[1] === 'messages' && 
                       pathParts[2] !== ''
    
    setIsDetailRoute(hasPathSlug)
  }, [pathname])
  
  return isDetailRoute
}