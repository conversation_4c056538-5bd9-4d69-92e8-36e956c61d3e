import { create } from 'zustand';
import { fakeAuthStore } from '../fakeAuthStore';
import { setAuth<PERSON><PERSON>ie, clearAuth<PERSON>ookie } from '@/actions/auth';
import type { App_User, ArtisteUser, OrganizationUser } from '../fakeAuthStore';
import type { LoginFormData, RegisterFormData } from './validationSchemas';

interface AuthState {
  user: App_User | null;
  isLoading: boolean;
  error: string | null;
  login: (data: LoginFormData) => Promise<void>;
  register: (data: RegisterFormData) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  isLoading: false,
  error: null,

  login: async (data) => {
    set({ isLoading: true, error: null });
    try {
      const user = fakeAuthStore.validateCredentials(data.email, data.password);
      if (!user) {
        throw new Error('Invalid credentials');
      }
      await setAuthCookie(user.id);
      set({ user, isLoading: false, error: null });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Login failed', isLoading: false });
    }
  },

  register: async (data) => {
    set({ isLoading: true, error: null });
    try {
      const existingUser = fakeAuthStore.findByEmail(data.email);
      if (existingUser) {
        throw new Error('Email already registered');
      }

      let newUser: App_User;
      if (data.type === 'artiste') {
        newUser = {
          id: Date.now(),
          type: 'artiste',
          email: data.email,
          password: data.password,
          artisteData: {},
        } as ArtisteUser;
      } else {
        newUser = {
          id: Date.now(),
          type: 'org',
          email: data.email,
          password: data.password,
          name: '',
          orgData: {},
        } as OrganizationUser;
      }

      fakeAuthStore.addUser(newUser);
      await setAuthCookie(newUser.id);
      set({ user: newUser, isLoading: false });
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Registration failed', isLoading: false });
    }
  },

  logout: async () => {
    set({ isLoading: true });
    try {
      await clearAuthCookie();
      set({ user: null, isLoading: false });
    } catch (error) {
      set({ error: 'Logout failed', isLoading: false });
    }
  },

  checkAuth: async () => {
    set({ isLoading: true });
    try {
      const user = await fakeAuthStore.findById(Number(localStorage.getItem('auth_user_id')));
      set({ user, isLoading: false });
    } catch (error) {
      set({ user: null, isLoading: false });
    }
  },
}));