import { ArtisteProfile } from "@/types/artiste-profile";
import { Organization } from "@/types/org_";
import { artisteProfiles } from "@/data/artiste-profiles";
import { organisations } from "@/data/organizations";

export type OrganizationUser = {
  id: number;
  type: 'org';
  email: string;
  name: string;
  password: string;
  orgData: Organization | Record<string, never>;
};

export type ArtisteUser = {
  id: number;
  type: 'artiste';
  email: string;
  password: string;
  artisteData: ArtisteProfile | Record<string, never>;
};

export type App_User = ArtisteUser | OrganizationUser;

// Initialize with some fake users from our data
let users: App_User[] = [
  // Create artiste users
  ...artisteProfiles.slice(0, 5).map(artiste => ({
    id: artiste.id,
    type: 'artiste' as const,
    email: artiste.auth.email,
    password: 'password123',
    artisteData: artiste,
  })),
  
  // Create organization users
  ...organisations.slice(0, 5).map(org => ({
    id: org.id + 1000, // Avoid ID conflicts
    type: 'org' as const,
    email: org.contactEmail,
    name: org.name,
    password: 'password123',
    orgData: org,
  })),
];

export const fakeAuthStore = {
  getAll: () => users,
  findByEmail: (email: string) => users.find(u => u.email === email),
  findById: (id: number) => users.find(u => u.id === id),
  addUser: (user: App_User) => {
    users.push(user);
    return user;
  },
  updateUser: (id: number, userData: Partial<App_User>) => {
    const index = users.findIndex(u => u.id === id);
    if (index !== -1) {
      const existingUser = users[index];
      if (existingUser.type === 'artiste' && 'artisteData' in userData) {
        users[index] = { ...existingUser, ...userData } as ArtisteUser;
      } else if (existingUser.type === 'org' && 'orgData' in userData) {
        users[index] = { ...existingUser, ...userData } as OrganizationUser;
      }
      return users[index];
    }
    return null;
  },
  validateCredentials: (email: string, password: string) => {
    const user = users.find(u => u.email === email && u.password === password);
    return user || null;
  },
};