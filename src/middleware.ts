import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const authUserId = request.cookies.get('auth_user_id')?.value;
  const isAuthPage = request.nextUrl.pathname.startsWith('/auth/connexion') || 
                     request.nextUrl.pathname.startsWith('/auth/inscription') ||
                     request.nextUrl.pathname.startsWith('/auth/mot-de-passe-oublie');

  // If trying to access auth pages while logged in, redirect to dashboard
  if (isAuthPage && authUserId) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // If trying to access protected pages while not logged in, redirect to login
  const isProtectedPage = request.nextUrl.pathname.startsWith('/mon-espace') || 
                          request.nextUrl.pathname.includes('/parametres');
  
  if (isProtectedPage && !authUserId) {
    return NextResponse.redirect(new URL('/auth/connexion', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/mon-espace/:path*',
    '/auth/:path*',
    '/parametres/:path*',
  ],
};