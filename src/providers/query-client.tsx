"use client"

import { getAppQueryClient } from '@/lib/queryClient';
import { isServer, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactNode } from 'react'
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools'




let browserQueryClient: QueryClient |undefined = undefined;

function getQueryClient() {
    if (isServer) {
        return getAppQueryClient();
    }
    else{

    if (!browserQueryClient) {
        browserQueryClient = getAppQueryClient();
    }
    return browserQueryClient;
    }



}

export const AppGlobalQueryProvider = ({
    children,
}: {
    children: ReactNode
}) => {
    const queryClient = getQueryClient();
    return (
        <QueryClientProvider client={queryClient}>
            {children}
            {/* <ReactQueryDevtools initialIsOpen={false} /> */}
        </QueryClientProvider>
    )
}