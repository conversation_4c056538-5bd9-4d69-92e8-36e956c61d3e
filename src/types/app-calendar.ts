export type UserRole = 'artist' | 'organization';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  profileImage?: string;
}

export interface Artist extends User {
  role: 'artist';
  bio?: string;
  specialties?: string[];
}

export interface Organization extends User {
  role: 'organization';
  description?: string;
}

export type AvailabilityStatus = 'available' | 'unavailable' | 'booked';

export interface TimeSlot {
  start: Date;
  end: Date;
}

export interface AvailabilitySlot extends TimeSlot {
  id: string;
  artistId: string;
  status: AvailabilityStatus;
  recurrenceRule?: RecurrenceRule;
}

export interface RecurrenceRule {
  frequency: 'daily' | 'weekly' | 'monthly';
  interval: number;
  daysOfWeek?: number[]; // 0-6, where 0 is Sunday
  endDate?: Date;
}

export type BookingStatus = 'requested' | 'confirmed' | 'rejected' | 'cancelled';

export interface Booking {
  id: string;
  artistId: string;
  organizationId: string;
  title: string;
  description?: string;
  slot: TimeSlot;
  status: BookingStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  read: boolean;
  createdAt: Date;
  relatedBookingId?: string;
}