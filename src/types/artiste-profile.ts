export type MediaType = 'video' | 'image';

export interface ArtisteMedia {
    id:string,
    type: MediaType;
    url: string;
    title?: string;
    description?: string;
    date?: string; // Optional date for the media
}

export interface ArtisteAbout {
    title: string; // Title to display on the profile
    biography: string; // Artist's biography
    firstName: string;
    lastName: string;
    gender: 'male' | 'female' | 'other';
    birthDate: string;
    isBirthDatePublic: boolean;
    city: string;
    phoneNumber: string;
}

export interface ArtisteJobPreference {
    title: string;
    location: string;
    canWorkCountrywide: boolean;
    contractType: 'CDI' | 'CDD' | 'Freelance';
    experience: '0-1' | '1-2' | '2-5' | '5-10' | '10+';
    context: string[];
}

export interface ArtisteExperience {
    title: string;
    organization: string;
    location: string;
    contractType: 'CDI' | 'CDD' | 'Freelance';
    startDate: string;
    endDate: string;
    description: string;
    skills: string[];
}

export interface ArtisteMediaContent {
    id:string,
    title: string;
    url: string;
    forExperience?: string; // ID or reference to experience
    date?: string; // Optional date for the media content
}

export interface ArtisteAuth {
    email: string;
    phone: string;
}

export interface ArtisteProfile {
    id: number;
    username: string;
    about: ArtisteAbout;
    jobPreference: ArtisteJobPreference;
    experiences: ArtisteExperience[];
    skills: string[];
    genre: string;
    auth: ArtisteAuth;
    avatar: string;
    mediaCover: string;
    marketingMedia: ArtisteMedia[];
    rating: number;
    completedPrestations: number;
    quickResponse: boolean;
    responseTime: string; 
    experience?:string,
    isPro?:boolean,
    isVerified?:boolean,
    isFeatured?:boolean,
    isActive?:boolean,
    joinedAt?:string,
    isGroup?:boolean
}