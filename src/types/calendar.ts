export type AvailabilityStatus = 'available' | 'unavailable' | 'booked';

export interface DateEntry {
  date: string; // ISO date string
  status: AvailabilityStatus;
  notes?: string;
}

export interface DayInfo {
  date: Date;
  status: AvailabilityStatus;
  isCurrentMonth: boolean;
  isToday: boolean;
  notes?: string;
}

export interface MonthData {
  year: number;
  month: number; // 0-indexed (0 = January)
  days: DayInfo[];
}