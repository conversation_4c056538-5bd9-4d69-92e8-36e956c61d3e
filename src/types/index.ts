import { Organization } from "./org_";

export type NotificationType = 'action' | 'simple' | 'file' | 'message' | 'facture' | 'rendez_vous' | 'demande_rendez_vous' | 'demande_signature';

export interface CardNotificationProps {
    type: NotificationType;
    title: string;
    message: string;
    sender: string;
    time: string;
    source?: string; // e.g. Synergy HR
    onApprove?: () => void;
    onDeny?: () => void;
    fileName?: string;
}


interface Location {
    city: string;
    country: string;
    type: 'onsite';
}

interface Period {
    debut: string;
    fin: string;
}

export type Opportunity = {
    id: number;
    jobTitle: string;
    slug: string;
    genre: string;
    coverImage: string;
    description: string;
    organization: Organization;
    datePublished: string;
    periode: Period;
    skillsRequired: string[];
    location: Location;
}