import { ArtisteProfile } from "./artiste-profile"
import { Organization } from "./org_"

export type SingleUserMessage ={
        id: string,
        type: "text" | "attachment" | "action" | "system"
        content: string
        sender: "user" | "contact" | "system"
        senderIs:"artiste"|"organisation",
        timestamp: Date
        status?: "sending" | "sent" | "delivered" | "read"
        attachments?: Array<{
            id: string
            name: string
            type: "file" | "image" | "video" | "audio" | "link"
            url: string
            size?: number
            thumbnail?: string
        }>
        action?: {
            type: "payment" | "meeting" | "task" | "reminder"
            data: any
        }
    
}


export type UserMessage = {
    id: string
    messages: SingleUserMessage[]
} & ({
    user:ArtisteProfile,
    is:"artiste"
} |{
    is:"organisation",
    user:Organization
})