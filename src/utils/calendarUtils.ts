

import { MonthData, DayInfo, DateEntry } from '../types/calendar';

export const generateMonthData = (year: number, month: number, dateEntries: DateEntry[]): MonthData => {
  const firstDayOfMonth = new Date(year, month, 1);
  const lastDayOfMonth = new Date(year, month + 1, 0);
  const daysInMonth = lastDayOfMonth.getDate();
  
  const firstDayOfWeek = firstDayOfMonth.getDay();
  const days: DayInfo[] = [];
  
  // Add days from previous month
  const prevMonthLastDay = new Date(year, month, 0).getDate();
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const prevMonthDay = prevMonthLastDay - i;
    const date = new Date(year, month - 1, prevMonthDay);
    const entry = findDateEntry(date, dateEntries);
    days.push({
      date,
      status: entry?.status || 'unavailable',
      isCurrentMonth: false,
      isToday: isToday(date),
      notes: entry?.notes
    });
  }
  
  // Add days of the current month
  for (let i = 1; i <= daysInMonth; i++) {
    const date = new Date(year, month, i);
    const entry = findDateEntry(date, dateEntries);
    days.push({
      date,
      status: entry?.status || 'unavailable',
      isCurrentMonth: true,
      isToday: isToday(date),
      notes: entry?.notes
    });
  }
  
  // Add days from next month
  const totalDaysNeeded = 42;
  const nextMonthDays = totalDaysNeeded - days.length;
  for (let i = 1; i <= nextMonthDays; i++) {
    const date = new Date(year, month + 1, i);
    const entry = findDateEntry(date, dateEntries);
    days.push({
      date,
      status: entry?.status || 'unavailable',
      isCurrentMonth: false,
      isToday: isToday(date),
      notes: entry?.notes
    });
  }
  
  return {
    year,
    month,
    days,
  };
};

const findDateEntry = (date: Date, entries: DateEntry[]): DateEntry | undefined => {
  const dateString = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  return entries.find(entry => entry.date === dateString);
};

export const isToday = (date: Date): boolean => {
  const today = new Date();
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
};

export const getMonthName = (month: number): string => {
  return new Date(0, month).toLocaleString('default', { month: 'long' });
};

export const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

export const DAYS_OF_WEEK = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];